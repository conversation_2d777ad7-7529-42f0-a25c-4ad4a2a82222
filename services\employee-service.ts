import { firestore, auth, actionCodeSettings } from '@/config/firebase';
import { collection, addDoc, getDocs, query, where, doc, updateDoc, deleteDoc, getDoc, setDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword, sendPasswordResetEmail, sendEmailVerification, signOut, signInWithEmailAndPassword } from 'firebase/auth';
import * as Clipboard from 'expo-clipboard';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

export interface Employee {
  id?: string;
  name: string;
  email: string;
  phone_number: string;
  cnic: string;
  age: number;
  gender: string;
  role: string;
  joining_date: Date;
  status: string;
  ownerId: string;
  farmId?: string;
  farmIds?: string[]; // Array of farm IDs this employee has access to
  assignedFarms?: string[]; // Array of farm IDs this employee is assigned to
  password?: string;
  photo?: string; // URL to employee photo
  // Multi-tenancy fields
  tenantId?: string; // ID of the owner (tenant) who owns the farm this employee is associated with
  uid?: string; // Firebase Auth UID
  // Timestamps
  created_at?: Date;
  updated_at?: Date;
  createdAt?: Date; // Alternative timestamp field name
  isEmployee?: boolean; // Flag to identify as employee
}

export const addEmployee = async (employeeData: Partial<Employee>): Promise<string> => {
  try {
    // Create the employee document
    const employeeRef = doc(collection(firestore, 'users'));
    const employeeId = employeeRef.id;
    
    // Set default values and add ID
    const newEmployee = {
      ...employeeData,
      id: employeeId,
      role: employeeData.role || 'caretaker',
      status: employeeData.status || 'active',
      created_at: new Date(),
      updated_at: new Date(),
    };
    
    // Ensure assignedFarms matches farmIds
    if (employeeData.farmIds) {
      newEmployee.assignedFarms = employeeData.farmIds;
    }
    
    await setDoc(employeeRef, newEmployee);

    
    // Update farm staff arrays if farmIds are provided
    const farmIds = employeeData.farmIds || [];
    for (const farmId of farmIds) {
      const farmRef = doc(firestore, 'farms', farmId);
      const farmDoc = await getDoc(farmRef);
      
      if (farmDoc.exists()) {
        const farmData = farmDoc.data();
        const currentStaff = farmData.staff || [];
        const currentStaffCount = farmData.staffCount || 0;
        
        // Only add if not already in staff array
        if (!currentStaff.includes(employeeId)) {
          await updateDoc(farmRef, {
            staff: [...currentStaff, employeeId],
            staffCount: currentStaffCount + 1,
            updatedAt: Date.now()
          });

        }
      }
    }
    
    return employeeId;
  } catch (error) {
    throw error;
  }
};

export const getEmployeesByOwner = async (ownerId: string) => {
  try {
    const q = query(collection(firestore, 'users'), 
      where('ownerId', '==', ownerId),
      where('isEmployee', '==', true));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    throw error;
  }
};

export const getEmployeesByFarm = async (farmId: string): Promise<Employee[]> => {
  try {
    // First, get the farm document to get the staff array
    const farmRef = doc(firestore, 'farms', farmId);
    const farmDoc = await getDoc(farmRef);
    
    if (!farmDoc.exists()) {
      return [];
    }
    
    const farmData = farmDoc.data();
    const staffIds = farmData.staff || [];

    if (staffIds.length === 0) {
      return [];
    }
    
    // Fetch each employee by ID
    const employeePromises = staffIds.map((staffId:any) => getEmployeeById(staffId));
    const employees = await Promise.all(employeePromises);

    return employees;
  } catch (error) {
    throw error;
  }
};

export const updateEmployee = async (id: string, data: Partial<Employee>): Promise<void> => {
  try {
    const employeeRef = doc(firestore, 'users', id);
    
    // If farmIds are being updated, handle farm staff arrays and assignedFarms
    if (data.farmIds) {
      // Get the current employee data to compare farm IDs
      const employeeDoc = await getDoc(employeeRef);
      if (employeeDoc.exists()) {
        const currentData = employeeDoc.data();
        const previousFarmIds = currentData.farmIds || [];
        const newFarmIds = data.farmIds;
        
        // Find farms that were added and removed
        const addedFarmIds = newFarmIds.filter(id => !previousFarmIds.includes(id));
        const removedFarmIds = previousFarmIds.filter((id:any) => !newFarmIds.includes(id));
        
        // Update added farms
        for (const farmId of addedFarmIds) {
          const farmRef = doc(firestore, 'farms', farmId);
          const farmDoc = await getDoc(farmRef);
          
          if (farmDoc.exists()) {
            const farmData = farmDoc.data();
            const currentStaff = farmData.staff || [];
            const currentStaffCount = farmData.staffCount || 0;
            
            // Only add if not already in staff array
            if (!currentStaff.includes(id)) {
              await updateDoc(farmRef, {
                staff: [...currentStaff, id],
                staffCount: currentStaffCount + 1,
                updatedAt: Date.now()
              });
              console.log(`Added employee ${id} to farm ${farmId}`);
            }
          }
        }
        
        // Update removed farms
        for (const farmId of removedFarmIds) {
          const farmRef = doc(firestore, 'farms', farmId);
          const farmDoc = await getDoc(farmRef);
          
          if (farmDoc.exists()) {
            const farmData = farmDoc.data();
            const currentStaff = farmData.staff || [];
            const currentStaffCount = farmData.staffCount || 0;
            
            // Remove from staff array
            const updatedStaff = currentStaff.filter((staffId:any) => staffId !== id);
            
            await updateDoc(farmRef, {
              staff: updatedStaff,
              staffCount: Math.max(0, currentStaffCount - 1),
              updatedAt: Date.now()
            });
            console.log(`Removed employee ${id} from farm ${farmId}`);
          }
        }
        
        // Also update the assignedFarms array to match farmIds
        data.assignedFarms = data.farmIds;
      }
    }
    
    // Update the employee document
    await updateDoc(employeeRef, {
      ...data,
      updated_at: new Date()
    });
    
  } catch (error) {
    throw error;
  }
};

export const updateEmployeeStatus = async (id: string, status: 'active' | 'inactive') => {
  try {
    const userRef = doc(firestore, 'users', id);
    
    // Update only the status field
    await updateDoc(userRef, {
      status,
      updated_at: Date.now()
    });

    return {
      id,
      status
    };
  } catch (error) {
    throw error;
  }
};

export const deleteEmployee = async (id: string) => {
  try {
    // First, get the employee data to find the farmId
    const userRef = doc(firestore, 'users', id);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('Employee not found');
    }

    const userData = userDoc.data();
    const farmIds = userData.assignedFarms || [];

    // Delete the user document
    await deleteDoc(userRef);

    // Update all farms this employee was associated with
    for (const farmId of farmIds) {
      try {
        const farmRef = doc(firestore, 'farms', farmId);
        const farmDoc = await getDoc(farmRef);

        if (farmDoc.exists()) {
          const farmData = farmDoc.data();
          const currentStaff = farmData.staff || [];
          const currentStaffCount = farmData.staffCount || 0;

          // Remove the employee ID from the staff array
          const updatedStaff = currentStaff.filter((staffId:any) => staffId !== id);

          // Update the farm document with the new staff array and decrement the count
          await updateDoc(farmRef, {
            staff: updatedStaff,
            staffCount: Math.max(0, currentStaffCount - 1), // Ensure count doesn't go below 0
            updatedAt: Date.now()
          });

        }
      } catch (farmError) {
        // Continue even if farm update fails
      }
    }

    return id;
  } catch (error) {
    throw error;
  }
};

export const syncFarmStaffCount = async (farmId: string) => {
  try {
    // Get all employees for this farm
    const employees = await getEmployeesByFarm(farmId);
    const actualStaffCount = employees.length;

    // Get the farm document
    const farmRef = doc(firestore, 'farms', farmId);
    const farmDoc = await getDoc(farmRef);

    if (!farmDoc.exists()) {
      throw new Error(`Farm ${farmId} not found`);
    }

    const farmData = farmDoc.data();
    const currentStaffCount = farmData.staffCount || 0;

    // If the counts don't match, update the farm document
    if (currentStaffCount !== actualStaffCount) {
      console.log(`Updating farm ${farmId} staff count from ${currentStaffCount} to ${actualStaffCount}`);

      // Get all employee IDs
      const employeeIds = employees.map(emp => emp.id);

      await updateDoc(farmRef, {
        staff: employeeIds,
        staffCount: actualStaffCount,
        updatedAt: Date.now()
      });

    }

    return actualStaffCount;
  } catch (error) {
    throw error;
  }
};

export const getEmployeeById = async (id: string): Promise<Employee> => {
  try {
    const userRef = doc(firestore, 'users', id);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      throw new Error('Employee not found');
    }
    
    const userData = userDoc.data();

    // Ensure farmIds is properly extracted and formatted
    let farmIds = userData.farmIds || userData.assignedFarms || [];
    
    // If farmIds is not an array, convert it to an array
    if (farmIds && !Array.isArray(farmIds)) {
      farmIds = [farmIds];
    }
    
    // Ensure assignedFarms is properly extracted and formatted
    let assignedFarms = userData.assignedFarms || userData.farmIds || [];
    
    // If assignedFarms is not an array, convert it to an array
    if (assignedFarms && !Array.isArray(assignedFarms)) {
      assignedFarms = [assignedFarms];
    }

    return {
      id: userDoc.id,
      ...userData,
      farmIds: farmIds,
      assignedFarms: assignedFarms,
    } as Employee;
  } catch (error) {
    throw error;
  }
};

