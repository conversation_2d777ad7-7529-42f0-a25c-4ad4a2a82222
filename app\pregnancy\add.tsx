import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, TouchableOpacity, ActivityIndicator, Platform, Switch } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import SpeciesFilterRow from '@/components/SpeciesFilterRow';
import DatePickerInput from '@/components/DatePickerInput';
import GenericDropdown from '@/components/GenericDropdown';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { useAnimalStore } from '@/store/animal-store';
import { usePregnancyStore } from '@/store/pregnancy-store';
import { useToast } from '@/contexts/ToastContext';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import Button from '@/components/Button';
import { Calendar, Save, MapPin, Heart, Shield } from 'lucide-react-native';
import { useSettingsStore } from '@/store/settings-store';
import { useHealthCheckStore } from '@/store/health-check-store';

interface DropdownItem {
  id: string;
  label: string;
  imageUrl?: string;
}

const PREGNANCY_STATUSES = [
  { id: 'confirmed', label: 'pregnancy.status.confirmed' },
  { id: 'suspected', label: 'pregnancy.status.suspected' },
  { id: 'not_pregnant', label: 'pregnancy.status.notPregnant' },
];

export default function AddPregnancyScreen() {
  const { t, language } = useTranslation();
  const { user } = useAuthStore();
  const { farms } = useFarmStore();
  const themedColors = useThemeColors();
  const { background, text, primary, card, primaryLight } = themedColors;
  const { animals: allAnimals, fetchAnimals: fetchAllAnimals } = useAnimalStore();
  const { addPregnancy, loading } = usePregnancyStore();
  const router = useRouter();
  const { showToast } = useToast();
  const { playFeedback } = useAudioFeedback();
  const { openaiApiKey, aiVisionEnabled } = useSettingsStore();
  const { getHealthChecksByAnimalId } = useHealthCheckStore();

  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(null);
  const [selectedFarm, setSelectedFarm] = useState<string | null>(null);
  const [femaleAnimals, setFemaleAnimals] = useState<DropdownItem[]>([]);
  const [selectedFemaleAnimal, setSelectedFemaleAnimal] = useState<DropdownItem | null>(null);
  const [maleAnimals, setMaleAnimals] = useState<DropdownItem[]>([]);
  const [selectedMaleAnimal, setSelectedMaleAnimal] = useState<DropdownItem | null>(null);
  const [conceptionDate, setConceptionDate] = useState<Date>(new Date());
  const [pregnancyStatus, setPregnancyStatus] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<any>({});
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [farmOptions, setFarmOptions] = useState<DropdownItem[]>([]);
  const [animalSpecies, setAnimalSpecies] = useState<string | null>(null);
  const [useAIPlans, setUseAIPlans] = useState<boolean>(false);

  useEffect(() => {
    if (farms && farms.length > 0) {
      const options = farms.map(farm => ({
        id: farm.id,
        label: farm.name,
        description: farm.location,
        icon: <MapPin size={20} color={primary} />
      }));
      setFarmOptions(options);
    }
  }, [farms, primary]);

  useEffect(() => {
    if (selectedFarm) {
      fetchAllAnimals(selectedFarm);
    }
  }, [selectedFarm, fetchAllAnimals]);

  // This effect handles initial filtering based on species selection
  useEffect(() => {
    if (allAnimals && allAnimals.length > 0) {
      console.log('All animals:', allAnimals.map(a => `${a.name} (${a.species}, ${a.gender})`));
      
      // Filter for female animals based on selected species
      const females = allAnimals.filter(animal => 
        animal.gender === 'female' && 
        (!selectedSpecies || animal.species === selectedSpecies)
      );
      
      console.log('Filtered females:', females.map(a => `${a.name} (${a.species}, ${a.gender})`));
      
      const femaleOptions = females.map(animal => ({
        id: animal.id,
        label: animal.name || `${animal.tagId || 'Unknown'}`,
        imageUri: animal.imageUri ? { uri: animal.imageUri } : getPlaceholderImage(animal.species)
      }));
      
      setFemaleAnimals(femaleOptions);
      
      // Filter for male animals based on selected species
      const males = allAnimals.filter(animal => 
        animal.gender === 'male' && 
        (!selectedSpecies || animal.species === selectedSpecies)
      );
      
      console.log('Filtered males:', males.map(a => `${a.name} (${a.species}, ${a.gender})`));
      
      const maleOptions = males.map(animal => ({
        id: animal.id,
        label: animal.name || `${animal.tagId || 'Unknown'}`,
        imageUri: animal.imageUri ? { uri: animal.imageUri } : getPlaceholderImage(animal.species)
      }));
      
      setMaleAnimals(maleOptions);
    } else {
      setFemaleAnimals([]);
      setMaleAnimals([]);
    }
  }, [allAnimals, selectedSpecies]);

  // When female animal is selected, update the species filter
  useEffect(() => {
    if (selectedFemaleAnimal && allAnimals) {
      const animal = allAnimals.find(a => a.id === selectedFemaleAnimal.id);
      if (animal) {
        // Update the species filter based on the selected female animal
        setAnimalSpecies(animal.species);
        setSelectedSpecies(animal.species);
        
        // Filter male animals to match the species of the selected female
        const males = allAnimals.filter(a => 
          a.gender === 'male' && 
          a.species === animal.species
        );
        
        const maleOptions = males.map(a => ({
          id: a.id,
          label: a.name || `${a.tagId || 'Unknown'}`,
          imageUri: a.imageUri ? { uri: a.imageUri } : getPlaceholderImage(a.species)
        }));
        
        setMaleAnimals(maleOptions);
      }
    }
  }, [selectedFemaleAnimal, allAnimals]);

  const validateForm = () => {
    const errors: any = {};

    if (!selectedFarm) {
      errors.farm = t('pregnancy.farmRequired', { defaultValue: 'Farm is required' });
    }

    if (!selectedFemaleAnimal) {
      errors.femaleAnimal = t('pregnancy.femaleAnimalRequired', { defaultValue: 'Female animal is required' });
    }

    if (!selectedMaleAnimal) {
      errors.sireAnimal = t('pregnancy.sireRequired', { defaultValue: 'Sire is required' });
    }

    if (!conceptionDate) {
      errors.conceptionDate = t('pregnancy.conceptionDateRequired', { defaultValue: 'Conception date is required' });
    }

    if (!pregnancyStatus) {
      errors.pregnancyStatus = t('pregnancy.statusRequired', { defaultValue: 'Status is required' });
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // First, let's add a function to generate AI plans
  const generatePregnancyPlans = async (animalData:any) => {
    try {
      console.log('Generating AI plans with data:', animalData);
      
      // Get gestation period based on species
      const species = animalData.species?.toLowerCase();
      const gestationPeriod = species === 'cow' ? 280 :
                               species === 'goat' ? 150 :
                               species === 'sheep' ? 150 :
                               species === 'pig' ? 114 : 280;
      
      // Calculate total weeks
      const totalWeeks = Math.ceil(gestationPeriod / 7);
      
      // Create mock data for testing with multiple weeks
      const dietPlan = [];
      
      // Early stage (first trimester)
      const earlyWeeks = Math.floor(totalWeeks / 3);
      for (let week = 1; week <= earlyWeeks; week++) {
        dietPlan.push({
          week: week,
          stage: 'early',
          title: language === 'ur'
            ? `ہفتہ ${week}: اے آئی سے تیار کردہ ابتدائی مرحلے کی غذائیت`
            : `Week ${week}: AI-Generated Early Stage Nutrition`,
          description: language === 'ur'
            ? `${totalWeeks} ہفتوں کے حمل کے ہفتہ ${week} کے لیے اے آئی سے تیار کردہ غذائی منصوبہ۔`
            : `Custom AI-generated nutrition plan for week ${week} of ${totalWeeks} weeks pregnancy.`,
          nutrients: language === 'ur' ? [
            'اے آئی کی تجویز - کیلشیم (ہڈیوں کی نشوونما کے لیے بڑھایا گیا)',
            'اے آئی کی تجویز - پروٹین (متوسط سطح)',
            'اے آئی کی تجویز - وٹامن اے، ڈی، ای'
          ] : [
            'AI-recommended Calcium (increased for bone development)',
            'AI-recommended Protein (moderate levels)',
            'AI-recommended Vitamins A, D, E'
          ],
          foods: language === 'ur' ? [
            `اے آئی کی تجویز - بھوسہ (${species === 'goat' ? '2-3' : '12-15'} کلو/دن)`,
            `اے آئی کی تجویز - معدنی مکسچر (${species === 'goat' ? '25-30گرام' : '150گرام'}/دن)`,
            'اے آئی کی تجویز - تازہ پانی (مرضی کے مطابق)'
          ] : [
            `AI-recommended Hay (${species === 'goat' ? '2-3' : '12-15'}kg/day)`,
            `AI-recommended Mineral Mix (${species === 'goat' ? '25-30g' : '150g'}/day)`,
            'AI-recommended Fresh Water (ad libitum)'
          ]
        });
      }
      
      // Mid stage (second trimester)
      const midWeeks = Math.floor(totalWeeks * 2 / 3);
      for (let week = earlyWeeks + 1; week <= midWeeks; week++) {
        dietPlan.push({
          week: week,
          stage: 'mid',
          title: language === 'ur'
            ? `ہفتہ ${week}: اے آئی سے تیار کردہ درمیانی مرحلے کی غذائیت`
            : `Week ${week}: AI-Generated Mid Stage Nutrition`,
          description: language === 'ur'
            ? `درمیانی حمل میں جنین کی نشوونما کے لیے اے آئی سے تیار کردہ غذائی منصوبہ۔`
            : `Custom AI-generated nutrition plan for developing fetus in mid-pregnancy.`,
          nutrients: language === 'ur' ? [
            'اے آئی کی تجویز - کیلشیم (اعلیٰ سطح)',
            'اے آئی کی تجویز - پروٹین (بڑھایا گیا سطح)',
            'اے آئی کی تجویز - بی کمپلیکس وٹامنز'
          ] : [
            'AI-recommended Calcium (high levels)',
            'AI-recommended Protein (increased levels)',
            'AI-recommended B-complex vitamins'
          ],
          foods: language === 'ur' ? [
            `اے آئی کی تجویز - معیاری چارہ (${species === 'goat' ? '3-4' : '15-18'} کلو/دن)`,
            `اے آئی کی تجویز - اناج کا مکسچر (${species === 'goat' ? '0.5-1' : '2-3'} کلو/دن)`,
            'اے آئی کی تجویز - معدنی سپلیمنٹس (حمل کے لیے ایڈجسٹ شدہ)'
          ] : [
            `AI-recommended Quality Forage (${species === 'goat' ? '3-4' : '15-18'}kg/day)`,
            `AI-recommended Grain Mix (${species === 'goat' ? '0.5-1' : '2-3'}kg/day)`,
            'AI-recommended Mineral Supplements (adjusted for pregnancy)'
          ]
        });
      }
      
      // Late stage (third trimester)
      for (let week = midWeeks + 1; week <= totalWeeks; week++) {
        dietPlan.push({
          week: week,
          stage: 'late',
          title: language === 'ur'
            ? `ہفتہ ${week}: اے آئی سے تیار کردہ آخری مرحلے کی غذائیت`
            : `Week ${week}: AI-Generated Late Stage Nutrition`,
          description: language === 'ur'
            ? `حمل کے آخری مرحلے کے لیے اے آئی سے تیار کردہ غذائی منصوبہ۔`
            : `Custom AI-generated nutrition plan for final stage of pregnancy.`,
          nutrients: language === 'ur' ? [
            'اے آئی کی تجویز - کیلشیم (سب سے اعلیٰ سطح)',
            'اے آئی کی تجویز - پروٹین (چوٹی کی سطح)',
            'اے آئی کی تجویز - توانائی سے بھرپور غذائی اجزاء'
          ] : [
            'AI-recommended Calcium (highest levels)',
            'AI-recommended Protein (peak levels)',
            'AI-recommended Energy-dense nutrients'
          ],
          foods: language === 'ur' ? [
            `اے آئی کی تجویز - پریمیم چارہ (${species === 'goat' ? '3-4' : '15-20'} کلو/دن)`,
            `اے آئی کی تجویز - توانائی کے سپلیمنٹس (${species === 'goat' ? '1-1.5' : '3-4'} کلو/دن)`,
            'اے آئی کی تجویز - پیدائش سے پہلے کا معدنی مکسچر (ایڈجسٹ شدہ خوراک)'
          ] : [
            `AI-recommended Premium Forage (${species === 'goat' ? '3-4' : '15-20'}kg/day)`,
            `AI-recommended Energy Supplements (${species === 'goat' ? '1-1.5' : '3-4'}kg/day)`,
            'AI-recommended Pre-birth Mineral Mix (adjusted dosage)'
          ]
        });
      }
      
      // Create health plan in the same format as diet plan
      const healthPlan = [];

      // Early stage health recommendations
      for (let week = 1; week <= earlyWeeks; week++) {
        healthPlan.push({
          week: week,
          stage: 'early',
          title: language === 'ur'
            ? `ہفتہ ${week}: اے آئی سے تیار کردہ ابتدائی مرحلے کی صحت کی توجہ`
            : `Week ${week}: AI-Generated Early Stage Health Focus`,
          description: language === 'ur'
            ? `${totalWeeks} ہفتوں کے حمل کے ہفتہ ${week} کے لیے اے آئی سے تیار کردہ صحت کا منصوبہ۔`
            : `Custom AI-generated health plan for week ${week} of ${totalWeeks} weeks pregnancy.`,
          checkups: language === 'ur' ? [
            'اے آئی کی تجویز - حمل کی ڈاکٹری تصدیق (دن 30-60 کے آس پاس الٹراساؤنڈ)',
            'اے آئی کی تجویز - جسمانی حالت کا اسکورنگ',
            'اے آئی کی تجویز - عمومی صحت کا جائزہ'
          ] : [
            'AI-recommended Veterinary confirmation of pregnancy (ultrasound around day 30-60)',
            'AI-recommended Body condition scoring',
            'AI-recommended General health assessment'
          ],
          treatments: language === 'ur' ? [
            'اے آئی کی تجویز - ہفتہ وار صحت کی نگرانی',
            'اے آئی کی تجویز - حمل سے پہلے کے سپلیمنٹس',
            'اے آئی کی تجویز - ہلکی ورزش کا نظام'
          ] : [
            'AI-recommended health monitoring weekly',
            'AI-recommended prenatal supplements',
            'AI-recommended gentle exercise regimen'
          ]
        });
      }

      // Mid stage health recommendations
      for (let week = earlyWeeks + 1; week <= midWeeks; week++) {
        healthPlan.push({
          week: week,
          stage: 'mid',
          title: language === 'ur'
            ? `ہفتہ ${week}: اے آئی سے تیار کردہ درمیانی مرحلے کی صحت کی توجہ`
            : `Week ${week}: AI-Generated Mid Stage Health Focus`,
          description: language === 'ur'
            ? `درمیانی حمل میں جنین کی نشوونما کے لیے اے آئی سے تیار کردہ صحت کا منصوبہ۔`
            : `Custom AI-generated health plan for developing fetus in mid-pregnancy.`,
          checkups: language === 'ur' ? [
            'اے آئی کی تجویز - دو ہفتہ وار صحت کی جانچ',
            'اے آئی کی تجویز - ضرورت کے مطابق ویکسینیشن شیڈول',
            'اے آئی کی تجویز - غذائی جائزہ'
          ] : [
            'AI-recommended Bi-weekly health checks',
            'AI-recommended Vaccination schedule if needed',
            'AI-recommended Nutritional assessment'
          ],
          treatments: language === 'ur' ? [
            'اے آئی کی تجویز - دو ہفتہ وار صحت کی جانچ',
            'اے آئی کی تجویز - ضرورت کے مطابق ویکسینیشن شیڈول',
            'اے آئی کی تجویز - متوسط سرگرمی کی سطح'
          ] : [
            'AI-recommended bi-weekly health checks',
            'AI-recommended vaccination schedule if needed',
            'AI-recommended moderate activity level'
          ]
        });
      }

      // Late stage health recommendations
      for (let week = midWeeks + 1; week <= totalWeeks; week++) {
        healthPlan.push({
          week: week,
          stage: 'late',
          title: language === 'ur'
            ? `ہفتہ ${week}: اے آئی سے تیار کردہ آخری مرحلے کی صحت کی توجہ`
            : `Week ${week}: AI-Generated Late Stage Health Focus`,
          description: language === 'ur'
            ? `حمل کے آخری مرحلے کے لیے اے آئی سے تیار کردہ صحت کا منصوبہ۔`
            : `Custom AI-generated health plan for final stage of pregnancy.`,
          checkups: language === 'ur' ? [
            'اے آئی کی تجویز - بار بار صحت کی نگرانی',
            'اے آئی کی تجویز - پیدائش کی تیاری',
            'اے آئی کی تجویز - پیدائش سے پہلے کا معائنہ'
          ] : [
            'AI-recommended Frequent health monitoring',
            'AI-recommended Preparation for birth',
            'AI-recommended Pre-birth examination'
          ],
          treatments: language === 'ur' ? [
            'اے آئی کی تجویز - بار بار صحت کی نگرانی',
            'اے آئی کی تجویز - پیدائش کی تیاری',
            'اے آئی کی تجویز - کم تناؤ والا ماحول'
          ] : [
            'AI-recommended frequent health monitoring',
            'AI-recommended preparation for birth',
            'AI-recommended reduced stress environment'
          ]
        });
      }

      return {
        dietPlan: dietPlan,
        treatmentPlan: healthPlan
      };
      
      // In production, you would call the actual OpenAI API here
      // const response = await fetch('https://api.openai.com/v1/chat/completions', {...});
      // return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      console.error('Error generating AI plans:', error);
      throw error;
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      playFeedback('error');
      return;
    }
    
    if (!user || !selectedFarm || !selectedFemaleAnimal || !selectedMaleAnimal) {
      return;
    }
    
    setIsSaving(true);
    
    try {
      // Find the complete animal objects
      const femaleAnimal = allAnimals.find(a => a.id === selectedFemaleAnimal.id);
      const maleAnimal = allAnimals.find(a => a.id === selectedMaleAnimal.id);
      
      if (!femaleAnimal || !maleAnimal) {
        throw new Error(t('pregnancy.animalNotFound'));
      }
      
      // Make sure we have a valid user ID
      if (!user.uid && !user.id) {
        throw new Error(t('common.userNotFound'));
      }
      
      // Generate AI plans if toggle is enabled
      let aiGeneratedDietPlan = null;
      let aiGeneratedTreatmentPlan = null;
      
      if (useAIPlans) {
        console.log('AI Plans toggle is enabled, generating plans...');
        try {
          // Get health check data for the animal
          const healthChecks = await getHealthChecksByAnimalId(selectedFarm, selectedFemaleAnimal.id);
          const latestHealthCheck = healthChecks && healthChecks.length > 0 
            ? healthChecks.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0]
            : null;
          
          // Prepare data for AI
          const animalData = {
            species: femaleAnimal.species,
            age: femaleAnimal.age,
            weight: latestHealthCheck?.weight || femaleAnimal.weight,
            temperature: latestHealthCheck?.temperature,
            healthStatus: {
              appetite: latestHealthCheck?.appetite || 'normal',
              hydration: latestHealthCheck?.hydration || 'normal',
              abnormalities: latestHealthCheck?.abnormalities || []
            },
            pregnancyStatus: pregnancyStatus
          };
          
          // Generate plans
          const plans = await generatePregnancyPlans(animalData);
          aiGeneratedDietPlan = plans.dietPlan;
          aiGeneratedTreatmentPlan = plans.treatmentPlan;
          
          console.log('AI plans generated successfully:', plans);
        } catch (aiError) {
          console.error('Error generating AI plans:', aiError);
          // Continue without AI plans if there's an error
        }
      }
      
      const pregnancyData = {
        farmId: selectedFarm,
        animalId: selectedFemaleAnimal.id,
        animalName: femaleAnimal.name || femaleAnimal.tagId,
        species: femaleAnimal.species,
        sireId: selectedMaleAnimal.id,
        sireName: maleAnimal.name || maleAnimal.tagId,
        conceptionDate: conceptionDate.toISOString(),
        status: pregnancyStatus,
        createdAt: new Date().toISOString(),
        createdBy: user.uid || user.id,
        // Add AI-related fields only if AI plans are enabled
        useAIPlans: useAIPlans,
        ...(useAIPlans ? {
          aiGeneratedDietPlan: aiGeneratedDietPlan,
          aiGeneratedHealthPlan: aiGeneratedTreatmentPlan
        } : {})
      };
      
      console.log('Saving pregnancy with data:', JSON.stringify(pregnancyData, null, 2));
      console.log('AI Plans enabled:', useAIPlans);
      console.log('AI Diet Plan:', aiGeneratedDietPlan ? 'Generated' : 'Not generated');
      console.log('AI Health Plan:', aiGeneratedTreatmentPlan ? 'Generated' : 'Not generated');

      await addPregnancy(selectedFarm, selectedFemaleAnimal.id, pregnancyData);
      
      playFeedback('success');
      showToast({
        type: 'success',
        title: t('pregnancy.addSuccess'),
        message: t('pregnancy.addSuccessMessage')
      });
      
      try {
        // Navigate to pregnancies tab instead of using back()
        router.replace('/(tabs)/pregnancy');
      } catch (navError) {
        console.error('Navigation error:', navError);
        // Fallback navigation with delay
        setTimeout(() => {
          router.replace('/(tabs)/pregnancy');
        }, 300);
      }
    } catch (error) {
      console.error('Error saving pregnancy:', error);
      playFeedback('error');
      showToast({
        type: 'error',
        title: t('pregnancy.addError'),
        message: (error as Error).message
      });
    } finally {
      setIsSaving(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
      backgroundColor: background,
    },
    scrollContent: {
      paddingBottom: 40,
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 10,
      color: text,
    },
    label: {
      fontSize: 16,
      marginBottom: 5,
      color: text,
    },
    errorText: {
      color: themedColors.isDarkMode ? '#F87171' : '#DC2626',
      fontSize: 12,
      marginTop: 2,
    },
    buttonContainer: {
      marginTop: 20,
      marginBottom: 40,
    },
    urduText: {
      fontFamily: 'UrduFont',
    },
    aiToggleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 10,
    },
  });

  // Get placeholder image for animals without an image
  const getPlaceholderImage = (species:any) => {
    const placeholderUrl = species?.toLowerCase() === 'cow'
      ? 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D'
      : species?.toLowerCase() === 'goat'
      ? 'https://images.unsplash.com/photo-1524024973431-2ad916746881?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8Z29hdCUyMGZhY2V8ZW58MHx8MHx8fDA%3D'
      : species?.toLowerCase() === 'poultry'
      ? 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8Y2hpY2tlbiUyMGZhY2V8ZW58MHx8MHx8fDA%3D'
      : species?.toLowerCase() === 'fish'
      ? 'https://images.unsplash.com/photo-1524704796725-9fc3044a58b2?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGZpc2glMjBmYWNlfGVufDB8fDB8fHww'
      : 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D';

    return { uri: placeholderUrl };
  };

  // Get gender-specific icons for animals
  const getGenderIcon = (gender: 'male' | 'female') => {
    const iconColor = primary;
    const iconSize = 20;

    if (gender === 'female') {
      // Female symbol - using Heart icon to represent female/motherhood
      return <Heart size={iconSize} color={iconColor} />;
    } else {
      // Male symbol - using Shield icon to represent male/strength
      return <Shield size={iconSize} color={iconColor} />;
    }
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          title: t('pregnancy.addPregnancy'),
          headerBackTitle: t('common.back')
        }} 
      />
      <ScrollView 
        style={styles.container} 
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.section}>
          <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
            {t('farms.selectFarms')}
          </Text>
          <GenericDropdown
            placeholder={t('farms.selectFarms')}
            items={farmOptions}
            value={selectedFarm}
            onSelect={setSelectedFarm}
            modalTitle={t('farms.selectFarms')}
            searchPlaceholder={t('farms.searchFarms')}
            error={validationErrors.farm}
          />
        </View>

            <SpeciesFilterRow
              selectedSpecies={selectedSpecies}
              onSelectSpecies={setSelectedSpecies}
            />

            <View style={styles.section}>
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                {t('pregnancy.selectFemaleAnimal')}
              </Text>
              <GenericDropdown
                placeholder={t('pregnancy.selectFemalePlaceholder')}
                items={femaleAnimals}
                value={selectedFemaleAnimal?.id}
                onSelect={(id) => {
                  const selected = femaleAnimals.find(animal => animal.id === id);
                  setSelectedFemaleAnimal(selected || null);

                  // Debug log to check the selected animal
                  if (selected && allAnimals) {
                    const animalDetails = allAnimals.find(a => a.id === selected.id);
                    console.log('Selected female animal:', animalDetails);
                  }
                }}
                modalTitle={t('pregnancy.selectFemale')}
                searchPlaceholder={t('pregnancy.searchFemales')}
                renderIcon={getGenderIcon('female')}
                error={validationErrors.femaleAnimal}
              />
            </View>

            <View style={styles.section}>
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                {t('pregnancy.selectSire')}
              </Text>
              <GenericDropdown
                placeholder={t('pregnancy.selectSirePlaceholder')}
                items={maleAnimals}
                value={selectedMaleAnimal?.id}
                onSelect={(id) => {
                  const selected = maleAnimals.find(animal => animal.id === id);
                  setSelectedMaleAnimal(selected || null);
                }}
                modalTitle={t('pregnancy.selectSire')}
                searchPlaceholder={t('pregnancy.searchSires')}
                renderIcon={getGenderIcon('male')}
                error={validationErrors.sireAnimal}
              />
            </View>

            <View style={styles.section}>
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                {t('pregnancy.conceptionDate')}
              </Text>
              <DatePickerInput
                value={conceptionDate}
                onChange={setConceptionDate}
                icon={<Calendar size={20} color={primary} />}
                error={validationErrors.conceptionDate}
              />
            </View>

            <View style={styles.section}>
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                {t('pregnancy.statusT')}
              </Text>
              <GenericDropdown
                placeholder={t('pregnancy.selectStatus')}
                items={PREGNANCY_STATUSES.map(status => ({
                  id: status.id,
                  label: t(status.label)
                }))}
                value={pregnancyStatus}
                onSelect={setPregnancyStatus}
                modalTitle={t('pregnancy.selectStatus')}
                renderIcon={false}
                searchPlaceholder={t('pregnancy.searchStatus')}
                error={validationErrors.pregnancyStatus}
              />
            </View>

            <View style={styles.section}>
              <View style={styles.aiToggleContainer}>
                <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                  {t('pregnancy.useAIPlans')}
                </Text>
                <Switch
                  value={useAIPlans}
                  onValueChange={(value) => {
                    if (value && !openaiApiKey) {
                      showToast({
                        type: 'warning',
                        title: t('settings.apiKeyMissing'),
                        message: t('settings.addApiKeyToUseAI')
                      });
                      return;
                    }
                    setUseAIPlans(value);
                  }}
                  trackColor={{
                    false: themedColors.textSecondary || '#E5E7EB',
                    true: primaryLight || primary + '80'
                  }}
                  thumbColor={useAIPlans ? primary : Platform.OS === 'ios' ? '#FFFFFF' : card}
                  ios_backgroundColor={themedColors.textSecondary || '#E5E7EB'}
                />
              </View>
              {useAIPlans && !openaiApiKey && (
                <Text style={styles.errorText}>{t('settings.apiKeyMissing')}</Text>
              )}
            </View>

            <View style={styles.buttonContainer}>
              <Button
                title={t('common.save')}
                onPress={handleSave}
                isLoading={isSaving}
                leftIcon={<Save size={20} color="white" />}
              />
            </View>
          
       
      </ScrollView>
    </>
  );
}
