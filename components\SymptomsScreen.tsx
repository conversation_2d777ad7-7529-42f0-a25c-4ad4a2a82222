import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  TextInput,
  RefreshControl,
  ScrollView
} from 'react-native';
import { useRouter } from 'expo-router';
import { colors } from '@/constants/colors';
import { Search, X, AlertTriangle, Info, ChevronRight } from 'lucide-react-native';
import LoadingIndicator from '@/components/LoadingIndicator';
import { Symptom, Disease } from '@/types/symptom';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
// Import the symptoms and diseases data
import { symptoms, diseases } from '@/data/symptoms';

export default function SymptomsScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const themedColors = useThemeColors();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSymptoms, setSelectedSymptoms] = useState<string[]>([]);
  const [matchedDiseases, setMatchedDiseases] = useState<Disease[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Filter symptoms based on search query
  const filteredSymptoms = searchQuery
    ? symptoms.filter(symptom => 
        t(`symptoms.symptom_${symptom.id}_name`).toLowerCase().includes(searchQuery.toLowerCase()) ||
        (symptom.description && t(`symptoms.symptom_${symptom.id}_description`).toLowerCase().includes(searchQuery.toLowerCase())) ||
        (symptom.bodyPart && symptom.bodyPart.toLowerCase().includes(searchQuery.toLowerCase())) // Assuming bodyPart might not be translated directly or is a key itself
      )
    : symptoms;

  // Find matching diseases based on selected symptoms
  useEffect(() => {
    if (selectedSymptoms.length > 0) {
      setLoading(true);
      
      // Simulate API call delay
      setTimeout(() => {
        const matches = diseases.filter(disease => {
          return selectedSymptoms.some(symptomId => 
            disease.symptoms.includes(symptomId)
          );
        });
        
        matches.sort((a, b) => {
          const aMatches = selectedSymptoms.filter(id => a.symptoms.includes(id)).length;
          const bMatches = selectedSymptoms.filter(id => b.symptoms.includes(id)).length;
          return bMatches - aMatches;
        });
        
        setMatchedDiseases(matches);
        setLoading(false);
      }, 500);
    } else {
      setMatchedDiseases([]);
    }
  }, [selectedSymptoms]);

  const handleRefresh = () => {
    setRefreshing(true);
    // Reset selections
    setSelectedSymptoms([]);
    setMatchedDiseases([]);
    setSearchQuery('');
    setRefreshing(false);
  };

  const toggleSymptom = (symptomId: string) => {
    setSelectedSymptoms(prev => {
      if (prev.includes(symptomId)) {
        return prev.filter(id => id !== symptomId);
      } else {
        return [...prev, symptomId];
      }
    });
  };

  const removeSymptom = (symptomId: string) => {
    setSelectedSymptoms(prev => prev.filter(id => id !== symptomId));
  };

  const getSymptomById = (id: string) => {
    return symptoms.find(s => s.id === id);
  };

  const styles = getStyles(themedColors);

  const renderSymptomItem = ({ item }: { item: Symptom }) => {
    const isSelected = selectedSymptoms.includes(item.id);
    
    return (
      <TouchableOpacity
        style={[styles.symptomItem, isSelected && styles.selectedSymptomItem]}
        onPress={() => toggleSymptom(item.id)}
      >
        <View style={styles.symptomInfo}>
          <Text style={styles.symptomName}>{t(`symptoms.symptom_${item.id}_name`)}</Text>
          {item.description && (
            <Text style={styles.symptomDescription}>{t(`symptoms.symptom_${item.id}_description`)}</Text>
          )}
          <Text style={styles.bodyPart}>{item.bodyPart}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Search bar */}
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Search size={20} color={themedColors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder={t('symptoms.searchPlaceholder')}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={themedColors.textSecondary}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <X size={20} color={themedColors.textSecondary} />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
      
      {/* Selected symptoms */}
      {selectedSymptoms.length > 0 && (
        <View style={styles.selectedSymptomsContainer}>
          <Text style={styles.sectionTitle}>{t('symptoms.selectedSymptomsTitle')}</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.chipContainer}>
              {selectedSymptoms.map(id => {
                const symptom = getSymptomById(id);
                if (!symptom) return null;
                
                return (
                  <TouchableOpacity
                    key={id}
                    style={styles.selectedSymptomChip}
                    onPress={() => removeSymptom(id)}
                  >
                    <Text style={styles.selectedSymptomText}>{t(`symptoms.symptom_${symptom.id}_name`)}</Text>
                    <X size={16} color={themedColors.primary} />
                  </TouchableOpacity>
                );
              })}
            </View>
          </ScrollView>
        </View>
      )}
      
      {/* Symptoms list */}
      {loading ? (
        <LoadingIndicator />
      ) : (
        <>
          {/* Matched diseases */}
          {matchedDiseases.length > 0 && (
            <View style={styles.matchedDiseasesContainer}>
              <Text style={styles.sectionTitle}>{t('symptoms.possibleConditionsTitle')}</Text>
              <FlatList
                data={matchedDiseases}
                keyExtractor={item => item.id}
                renderItem={({ item }) => {
                  const matchedSymptomCount = selectedSymptoms.filter(id => 
                    item.symptoms.includes(id)
                  ).length;
                  
                  const matchedSymptomNames = selectedSymptoms
                    .filter(id => item.symptoms.includes(id))
                    .map(id => {
                      const symptomData = getSymptomById(id);
                      return symptomData ? t(`symptoms.symptom_${symptomData.id}_name`) : '';
                    })
                    .filter(Boolean)
                    .join(', ');
                  
                  return (
                    <View style={styles.diseaseItem}>
                      <View style={styles.diseaseHeader}>
                        <Text style={styles.diseaseName}>{t(`symptoms.disease_${item.id}_name`)}</Text>
                      </View>
                      <Text style={styles.diseaseDescription}>{t(`symptoms.disease_${item.id}_description`)}</Text>
                      <Text style={styles.matchedSymptoms}>
                        {t('symptoms.matchingSymptomsPrefix')}{matchedSymptomNames}
                      </Text>
                    </View>
                  );
                }}
              />
            </View>
          )}
          
          {/* All symptoms */}
          <View style={styles.symptomsContainer}>
            <Text style={styles.sectionTitle}>{t('symptoms.allSymptomsTitle')}</Text>
            <FlatList
              data={filteredSymptoms}
              keyExtractor={item => item.id}
              renderItem={renderSymptomItem}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                />
              }
              ListEmptyComponent={
                <View style={styles.noResults}>
                  <AlertTriangle size={24} color={themedColors.textSecondary} />
                  <Text style={styles.noResultsText}>{t('symptoms.noSymptomsFound')}</Text>
                </View>
              }
            />
          </View>
        </>
      )}
    </View>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.card,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: themedColors.text,
  },
  symptomsContainer: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: themedColors.text,
  },
  symptomItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: themedColors.card,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  selectedSymptomItem: {
    borderColor: themedColors.primary,
    backgroundColor: themedColors.primaryLight,
  },
  symptomInfo: {
    flex: 1,
    marginLeft: 12,
  },
  symptomName: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
  },
  symptomDescription: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginTop: 4,
  },
  bodyPart: {
    fontSize: 12,
    color: themedColors.textSecondary,
    marginTop: 4,
  },
  selectedSymptomsContainer: {
    padding: 16,
    backgroundColor: themedColors.card,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
  },
  selectedSymptomChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.primaryLight,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedSymptomText: {
    color: themedColors.primary,
    marginRight: 4,
  },
  matchedDiseasesContainer: {
    padding: 16,
  },
  diseaseItem: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  diseaseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  diseaseName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  diseaseDescription: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 12,
  },
  matchedSymptoms: {
    fontSize: 14,
    color: themedColors.primary,
    marginTop: 8,
  },
  noResults: {
    padding: 16,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: 16,
    color: themedColors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  }
});
