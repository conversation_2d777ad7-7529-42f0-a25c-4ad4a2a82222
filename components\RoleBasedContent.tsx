import React from 'react';
import { useAuthStore } from '@/store/auth-store';

interface RoleBasedContentProps {
  allowedRoles: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Component that conditionally renders content based on user role
 *
 * @param allowedRoles Array of roles that are allowed to see the content
 * @param children Content to show if the user has an allowed role
 * @param fallback Optional content to show if the user doesn't have an allowed role
 */
export function RoleBasedContent({
  allowedRoles,
  children,
  fallback = null
}: RoleBasedContentProps) {
  const { user } = useAuthStore();

  // If no user is logged in, show fallback
  if (!user) {
    return <>{fallback}</>;
  }

  // If the user's role is in the allowed roles, show the content
  if (allowedRoles.includes(user.role)) {
    console.log('RoleBasedContent - User role is allowed, showing content');
    return <>{children}</>;
  }

  // Otherwise, show the fallback
  console.log('RoleBasedContent - User role is not allowed, showing fallback');
  return <>{fallback}</>;
}
