export interface Farm {
  id: string;
  name: string;
  location: string;
  status: FarmStatus;
  ownerId: string; // ID of the user who created/owns the farm
  animalCount?: number; // Count of animals in this farm
  staffCount?: number; // Count of staff assigned to this farm
  animals?: string[]; // Array of animal IDs in this farm
  staff?: string[]; // Array of staff IDs assigned to this farm
  createdAt: number;
  updatedAt: number;
}

export enum FarmStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  COMPLETED = 'completed',
}
