import { useTranslation } from '@/hooks/useTranslation';

/**
 * Formats a timestamp into a localized date string
 * @param timestamp Unix timestamp in milliseconds
 * @param locale Optional locale string (defaults to 'en-US')
 * @returns Formatted date string
 */
// const { t, language } = useTranslation();

export function formatDate(timestamp: number, locale?: string): string {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString(locale ||'en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}
export function formatDateToYearsandMonths(timestamp: number, locale?: string): string {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString(locale || 'en-US', {
    year: 'numeric',
    month: 'short',
  });
}

/**
 * Formats a timestamp into a localized date and time string
 * @param timestamp Unix timestamp in milliseconds
 * @param locale Optional locale string (defaults to 'en-US')
 * @returns Formatted date and time string
 */
export function formatDateTime(timestamp: number, locale?: string): string {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString(locale || 'en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Gets a relative time string (e.g., "2 days ago")
 * @param timestamp Unix timestamp in milliseconds
 * @returns Relative time string
 */
export function getRelativeTime(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  
  // Convert to seconds
  const seconds = Math.floor(diff / 1000);
  
  if (seconds < 60) {
    return 'just now';
  }
  
  // Convert to minutes
  const minutes = Math.floor(seconds / 60);
  
  if (minutes < 60) {
    return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
  }
  
  // Convert to hours
  const hours = Math.floor(minutes / 60);
  
  if (hours < 24) {
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  }
  
  // Convert to days
  const days = Math.floor(hours / 24);
  
  if (days < 30) {
    return `${days} ${days === 1 ? 'day' : 'days'} ago`;
  }
  
  // Convert to months
  const months = Math.floor(days / 30);
  
  if (months < 12) {
    return `${months} ${months === 1 ? 'month' : 'months'} ago`;
  }
  
  // Convert to years
  const years = Math.floor(months / 12);
  
  return `${years} ${years === 1 ? 'year' : 'years'} ago`;
}