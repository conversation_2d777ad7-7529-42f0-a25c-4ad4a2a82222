{
  "common": {
    "error": "Error",
    "errorOccurred": "An error occurred",
    "microphonePermissionDenied": "Microphone permission is required to record audio",
    "recordingSaved": "Recording saved successfully",
    "speechRecognitionError": "Error converting speech to text. Please try again.",
    "unsupported": "Feature Not Supported",
    "voiceRecordingWebUnsupported": "Voice recording is not supported in web browsers. Please use the mobile app for this feature.",
    "recording": "Recording...",
    "caretaker": "Caretaker",
    "ok": "OK",
    "noFarmsTitle": "No Farms Available",
    "noFarmsMessage": "Please add a farm to continue using this feature."
  },
  "records": {
    "cow-med-antibiotics": "Antibiotics",
    "cow-med-antibioticsDescription": "Used to treat bacterial infections such as mastitis, pneumonia.",
    "cow-med-anthelmintics": "Anthelmintics (Dewormers)",
    "cow-med-anthelminticDescription": "For internal parasites like worms.",
    "cow-med-pain-relievers": "Pain Relievers",
    "cow-med-pain-relieversDescription": "NSAIDs for inflammation or injury-related pain.",
    "cow-med-digestive": "Digestive Boosters",
    "cow-med-digestiveDescription": "Improve rumen function and appetite.",
    
    "cow-vac-fmd": "FMD Vaccine",
    "cow-vac-fmdDescription": "Protects against Foot and Mouth Disease (every 6 months).",
    "cow-vac-hs": "HS Vaccine",
    "cow-vac-hsDescription": "Prevents Hemorrhagic Septicemia (yearly).",
    "cow-vac-bq": "BQ Vaccine",
    "cow-vac-bqDescription": "Prevents Black Quarter (yearly).",
    "cow-vac-brucellosis": "Brucellosis Vaccine",
    "cow-vac-brucellosisDescription": "For female calves to prevent infertility.",
    
    "cow-sur-csection": "C-section",
    "cow-sur-csectionDescription": "In case of difficult calving.",
    "cow-sur-dehorning": "Horn Removal (Dehorning)",
    "cow-sur-dehorningDescription": "Common in young calves for safety.",
    "cow-sur-abscess": "Abscess Drainage",
    "cow-sur-abscessDescription": "Minor surgery for pus-filled swellings.",
    
    "cow-birth-normal": "Normal Calving",
    "cow-birth-normalDescription": "Regular, natural birth record.",
    "cow-birth-assisted": "Assisted Calving",
    "cow-birth-assistedDescription": "Requires manual or vet-assisted delivery.",
    "cow-birth-stillbirth": "Stillbirth",
    "cow-birth-stillbirthDescription": "Record of unsuccessful birth.",
    
    "cow-check-weekly": "Weekly Health Check",
    "cow-check-weeklyDescription": "General inspection: eyes, coat, temperature.",
    "cow-check-hoof": "Hoof Inspection",
    "cow-check-hoofDescription": "Check for infections or overgrowth.",
    "cow-check-feeding": "Feeding Behavior",
    "cow-check-feedingDescription": "Observe appetite and water intake.",
    
    "goat-med-coccidiostats": "Coccidiostats",
    "goat-med-coccidiostatsDescription": "For prevention/treatment of coccidiosis.",
    "goat-med-antibiotics": "Antibiotics",
    "goat-med-antibioticsDescription": "For pneumonia, infections.",
    "goat-med-dewormers": "Dewormers",
    "goat-med-dewormersDescription": "Every 3–6 months depending on region.",
    
    "goat-vac-ppr": "PPR Vaccine",
    "goat-vac-pprDescription": "Protects against Peste des Petits Ruminants.",
    "goat-vac-enterotoxemia": "Enterotoxemia Vaccine",
    "goat-vac-enterotoxemiaDescription": "Prevents overeating disease.",
    "goat-vac-fmd-pox": "FMD / Goat Pox Vaccines",
    "goat-vac-fmd-poxDescription": "Protect from outbreaks.",
    
    // Poultry Medications
    "poultry-med-antibiotics": "Antibiotics",
    "poultry-med-antibioticsDescription": "For respiratory infections and bacterial diseases.",
    "poultry-med-coccidiostats": "Coccidiostats",
    "poultry-med-coccidiostatsDescription": "Prevention and treatment of coccidiosis.",
    "poultry-med-vitamins": "Vitamin Supplements",
    "poultry-med-vitaminsDescription": "For growth and immune system support.",
    "poultry-med-probiotics": "Probiotics",
    "poultry-med-probioticsDescription": "For gut health and improved digestion.",
    
    // Poultry Vaccinations
    "poultry-vac-newcastle": "Newcastle Disease Vaccine",
    "poultry-vac-newcastleDescription": "Essential protection against Newcastle disease.",
    "poultry-vac-infectious-bronchitis": "Infectious Bronchitis Vaccine",
    "poultry-vac-infectious-bronchitisDescription": "Protects respiratory system.",
    "poultry-vac-gumboro": "Gumboro (IBD) Vaccine",
    "poultry-vac-gumboroDescription": "Prevents infectious bursal disease.",
    "poultry-vac-fowl-pox": "Fowl Pox Vaccine",
    "poultry-vac-fowl-poxDescription": "Prevents skin lesions and respiratory issues.",
    
    // Poultry Health Checks
    "poultry-check-flock": "Flock Health Check",
    "poultry-check-flockDescription": "General assessment of the entire flock.",
    "poultry-check-egg": "Egg Production Check",
    "poultry-check-eggDescription": "Monitoring quantity and quality of eggs.",
    "poultry-check-weight": "Weight Monitoring",
    "poultry-check-weightDescription": "Track growth rates and feed conversion.",
    
    // Fish Medications
    "fish-med-antibiotics": "Antibiotics",
    "fish-med-antibioticsDescription": "For bacterial infections in aquaculture.",
    "fish-med-antifungal": "Antifungal Treatment",
    "fish-med-antifungalDescription": "Treats fungal infections on skin and gills.",
    "fish-med-antiparasitic": "Antiparasitic Treatment",
    "fish-med-antiparasiticDescription": "Removes external and internal parasites.",
    "fish-med-water-treatment": "Water Treatment",
    "fish-med-water-treatmentDescription": "Improves water quality and reduces stress.",
    
    // Fish Health Checks
    "fish-check-water": "Water Quality Check",
    "fish-check-waterDescription": "Testing pH, ammonia, nitrite, and oxygen levels.",
    "fish-check-growth": "Growth Monitoring",
    "fish-check-growthDescription": "Tracking size and weight development.",
    "fish-check-behavior": "Behavior Assessment",
    "fish-check-behaviorDescription": "Observing swimming patterns and feeding response.",
    "fish-check-disease": "Disease Screening",
    "fish-check-diseaseDescription": "Checking for signs of common diseases."
  }
}

// Use the existing OpenAI API key from your settings store
// Implement OpenAI's Whisper API for high-quality speech-to-text transcription
// use OpenAI's speech-to-text capabilities within an Expo React Native app. Using OpenAI Whisper for 
//    speech-to-text requires sending audio data to the OpenAI API for transcription. The Expo app can capture
//    audio using Expo Audio APIs, then send that audio file to OpenAI's API.
