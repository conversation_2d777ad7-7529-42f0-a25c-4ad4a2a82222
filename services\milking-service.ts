import { firestore } from '@/config/firebase';
import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  getDoc,
  limit 
} from 'firebase/firestore';
import { MilkingRecord, MilkingStats, MilkQuality } from '@/types/milking';

/**
 * Add a new milking record
 */
export const addMilkingRecord = async (recordData: Omit<MilkingRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    // Clean the data by removing undefined values (Firebase doesn't like undefined)
    const cleanData: Record<string, any> = {};

    Object.entries(recordData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanData[key] = value;
      }
    });

    // Add to Firestore as subcollection under farm
    const farmRef = doc(firestore, 'farms', recordData.farmId);
    const milkingCollectionRef = collection(farmRef, 'milking');
    const recordRef = await addDoc(milkingCollectionRef, {
      ...cleanData,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return recordRef.id;
  } catch (error) {
    console.error('Error adding milking record:', error);
    throw error;
  }
};

/**
 * Update an existing milking record
 */
export const updateMilkingRecord = async (id: string, updates: Partial<MilkingRecord>): Promise<void> => {
  try {
    // Need farmId to locate the record in the subcollection
    if (!updates.farmId) {
      throw new Error('farmId is required to update milking record');
    }

    // Clean the updates by removing undefined values (Firebase doesn't like undefined)
    const cleanUpdates: Record<string, any> = {};

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanUpdates[key] = value;
      }
    });

    const recordRef = doc(firestore, 'farms', updates.farmId, 'milking', id);
    await updateDoc(recordRef, {
      ...cleanUpdates,
      updatedAt: Date.now(),
    });
  } catch (error) {
    console.error('Error updating milking record:', error);
    throw error;
  }
};

/**
 * Delete a milking record
 */
export const deleteMilkingRecord = async (id: string, farmId: string): Promise<void> => {
  try {
    const recordRef = doc(firestore, 'farms', farmId, 'milking', id);
    await deleteDoc(recordRef);
  } catch (error) {
    console.error('Error deleting milking record:', error);
    throw error;
  }
};

/**
 * Get milking records for a specific farm
 */
export const getMilkingRecordsByFarm = async (farmId: string): Promise<MilkingRecord[]> => {
  try {
    // Fetch from farm subcollection
    const farmRef = doc(firestore, 'farms', farmId);
    const milkingCollectionRef = collection(farmRef, 'milking');

    let querySnapshot;
    try {
      // First try with ordering by date
      const q = query(milkingCollectionRef, orderBy('date', 'desc'));
      querySnapshot = await getDocs(q);
    } catch (indexError) {
      console.warn('Index not available, falling back to simple query:', indexError);
      // Fallback to simple query without ordering if index doesn't exist
      querySnapshot = await getDocs(milkingCollectionRef);
    }

    const records: MilkingRecord[] = [];

    querySnapshot.forEach((doc) => {
      records.push({
        id: doc.id,
        ...doc.data(),
        farmId: farmId, // Ensure farmId is set
      } as MilkingRecord);
    });

    // Sort manually if we couldn't use orderBy
    records.sort((a, b) => b.date - a.date);

    return records;
  } catch (error) {
    console.error('Error fetching milking records:', error);
    // Return empty array instead of throwing error to prevent UI crashes
    return [];
  }
};

/**
 * Get milking records for a specific animal
 */
export const getMilkingRecordsByAnimal = async (animalId: string): Promise<MilkingRecord[]> => {
  try {
    // First, we need to find which farm this animal belongs to
    // We'll search through farms to find the animal
    const farmsRef = collection(firestore, 'farms');
    const farmsSnapshot = await getDocs(farmsRef);

    const records: MilkingRecord[] = [];

    for (const farmDoc of farmsSnapshot.docs) {
      const farmId = farmDoc.id;

      // Check if this farm has the animal
      const animalRef = doc(firestore, 'farms', farmId, 'animals', animalId);
      const animalSnap = await getDoc(animalRef);

      if (animalSnap.exists()) {
        // Found the animal, now get its milking records
        const milkingCollectionRef = collection(firestore, 'farms', farmId, 'milking');

        let querySnapshot;
        try {
          const q = query(milkingCollectionRef, where('animalId', '==', animalId), orderBy('date', 'desc'));
          querySnapshot = await getDocs(q);
        } catch (indexError) {
          console.warn('Index not available for animal milking records, using simple query:', indexError);
          const simpleQ = query(milkingCollectionRef, where('animalId', '==', animalId));
          querySnapshot = await getDocs(simpleQ);
        }

        querySnapshot.forEach((doc) => {
          records.push({
            id: doc.id,
            ...doc.data(),
            farmId: farmId, // Ensure farmId is set
          } as MilkingRecord);
        });

        break; // Found the animal, no need to search other farms
      }
    }

    // Sort manually if we couldn't use orderBy
    records.sort((a, b) => b.date - a.date);

    return records;
  } catch (error) {
    console.error('Error fetching animal milking records:', error);
    // Return empty array instead of throwing error to prevent UI crashes
    return [];
  }
};

/**
 * Get milking statistics for a farm
 */
export const getMilkingStatsByFarm = async (farmId: string, days: number = 30): Promise<MilkingStats> => {
  try {
    const startDate = Date.now() - (days * 24 * 60 * 60 * 1000);

    // Fetch from farm subcollection
    const farmRef = doc(firestore, 'farms', farmId);
    const milkingCollectionRef = collection(farmRef, 'milking');

    // Try complex query first, fallback to simple query if index doesn't exist
    let querySnapshot;
    try {
      const q = query(
        milkingCollectionRef,
        where('date', '>=', startDate),
        orderBy('date', 'desc')
      );
      querySnapshot = await getDocs(q);
    } catch (indexError) {
      console.warn('Complex query failed, using simple query for stats:', indexError);
      // Fallback to simple query and filter manually
      querySnapshot = await getDocs(milkingCollectionRef);
    }

    const records: MilkingRecord[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data() as MilkingRecord;
      // Filter by date manually if we used simple query
      if (data.date >= startDate) {
        records.push({
          id: doc.id,
          ...data,
          farmId: farmId, // Ensure farmId is set
        });
      }
    });

    // Sort manually
    records.sort((a, b) => b.date - a.date);
    
    // Calculate statistics
    const totalQuantity = records.reduce((sum, record) => sum + record.quantity, 0);
    const averageDaily = totalQuantity / days;
    
    const uniqueAnimals = new Set(records.map(r => r.animalId));
    const averagePerAnimal = uniqueAnimals.size > 0 ? totalQuantity / uniqueAnimals.size : 0;
    
    // Quality distribution
    const qualityDistribution: Record<MilkQuality, number> = {
      [MilkQuality.EXCELLENT]: 0,
      [MilkQuality.GOOD]: 0,
      [MilkQuality.FAIR]: 0,
      [MilkQuality.POOR]: 0,
    };
    
    records.forEach(record => {
      qualityDistribution[record.quality]++;
    });
    
    // Top producers
    const animalProduction: Record<string, { name: string; total: number; count: number }> = {};
    
    records.forEach(record => {
      if (!animalProduction[record.animalId]) {
        animalProduction[record.animalId] = {
          name: record.animalName,
          total: 0,
          count: 0,
        };
      }
      animalProduction[record.animalId].total += record.quantity;
      animalProduction[record.animalId].count++;
    });
    
    const topProducers = Object.entries(animalProduction)
      .map(([animalId, data]) => ({
        animalId,
        animalName: data.name,
        totalQuantity: data.total,
        averageDaily: data.total / days,
      }))
      .sort((a, b) => b.totalQuantity - a.totalQuantity)
      .slice(0, 5);
    
    // Monthly trend (simplified for last 6 months)
    const monthlyTrend = [];
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date();
      monthStart.setMonth(monthStart.getMonth() - i);
      monthStart.setDate(1);
      monthStart.setHours(0, 0, 0, 0);
      
      const monthEnd = new Date(monthStart);
      monthEnd.setMonth(monthEnd.getMonth() + 1);
      
      const monthRecords = records.filter(r => 
        r.date >= monthStart.getTime() && r.date < monthEnd.getTime()
      );
      
      const monthQuantity = monthRecords.reduce((sum, record) => sum + record.quantity, 0);
      
      monthlyTrend.push({
        month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        quantity: monthQuantity,
      });
    }
    
    return {
      totalQuantity,
      averageDaily,
      averagePerAnimal,
      qualityDistribution,
      topProducers,
      monthlyTrend,
    };
  } catch (error) {
    console.error('Error calculating milking stats:', error);
    throw error;
  }
};

/**
 * Get recent milking records for a farm
 */
export const getRecentMilkingRecords = async (farmId: string, limitCount: number = 10): Promise<MilkingRecord[]> => {
  try {
    // Fetch from farm subcollection
    const farmRef = doc(firestore, 'farms', farmId);
    const milkingCollectionRef = collection(farmRef, 'milking');

    let querySnapshot;
    try {
      const q = query(
        milkingCollectionRef,
        orderBy('date', 'desc'),
        limit(limitCount)
      );
      querySnapshot = await getDocs(q);
    } catch (indexError) {
      console.warn('Index not available for recent records, using simple query:', indexError);
      // Fallback to simple query without ordering
      querySnapshot = await getDocs(milkingCollectionRef);
    }

    const records: MilkingRecord[] = [];

    querySnapshot.forEach((doc) => {
      records.push({
        id: doc.id,
        ...doc.data(),
        farmId: farmId, // Ensure farmId is set
      } as MilkingRecord);
    });

    // Sort manually and limit
    records.sort((a, b) => b.date - a.date);
    return records.slice(0, limitCount);
  } catch (error) {
    console.error('Error fetching recent milking records:', error);
    // Return empty array instead of throwing error
    return [];
  }
};
