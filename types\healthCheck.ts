export type HealthCheck = {
  id: string;
  animalId: string;
  date: number; // timestamp
  temperature?: number;
  weight?: number;
  appetite: 'normal' | 'increased' | 'decreased' | 'none';
  hydration: 'normal' | 'dehydrated' | 'overhydrated';
  respiration: 'normal' | 'increased' | 'decreased' | 'labored';
  gait: 'normal' | 'limping' | 'stiff' | 'unable';
  fecal: 'normal' | 'diarrhea' | 'constipated' | 'bloody';
  coat: 'normal' | 'dull' | 'patchy' | 'irritated';
  eyes: 'normal' | 'discharge' | 'cloudy' | 'red';
  ears: 'normal' | 'discharge' | 'red' | 'swollen';
  notes: string;
  abnormalities: boolean;
  nextCheckDate: number; // timestamp
  createdAt: number; // timestamp
  updatedAt: number; // timestamp
  imageUri?: string; // URI of the captured image
  createdBy?: string; // ID of the user who created the health check

  // Multi-tenancy fields
  tenantId: string; // ID of the owner (tenant) who owns the farm this health check is associated with
};