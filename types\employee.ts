export interface Employee {
  id?: string;
  name: string;
  email: string;
  phone_number?: string;
  cnic?: string;
  age?: number;
  gender?: string;
  role?: 'owner' | 'admin' | 'caretaker';
  status?: 'active' | 'inactive';
  joining_date?: number;
  photo?: string;
  farmIds?: string[];
  assignedFarms?: string[]; // Array of farm IDs the employee is assigned to
  created_at?: Date;
  updated_at?: Date;
  isEmployee?: boolean;
}

