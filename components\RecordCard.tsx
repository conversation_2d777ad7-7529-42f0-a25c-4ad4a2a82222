import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, useColorScheme, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { AnimalRecord, RecordType } from '@/types/record';
import {
  AlertTriangle,
  Calendar,
  FileText,
  Syringe,
  Pill,
  Sciss<PERSON>,
  Baby,
  Stethoscope,
  Skull,
  HelpCircle
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSettingsStore } from '@/store/settings-store';
import { useAnimalStore } from '@/store/animal-store';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';

interface RecordCardProps {
  record: AnimalRecord;
  showAnimalName?: boolean;
  animalName?: string;
}

export const RecordCard: React.FC<RecordCardProps> = ({
  record,
  showAnimalName = false,
  animalName
}) => {
  const router = useRouter();
  const { animals } = useAnimalStore();
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();

  // Find the animal associated with this record
  const animal = animals.find(a => a.id === record.animalId);

  const isDarkMode = themedColors.isDarkMode;
  const styles = getStyles(themedColors, language);

  // Get placeholder image for animals without an image - focused on faces
  const getPlaceholderImage = (species: string) => {
    switch (species?.toLowerCase()) {
      case 'cow':
        return 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop';
      case 'goat':
        return 'https://images.unsplash.com/photo-1524024973431-2ad916746881?q=80&w=1000&auto=format&fit=crop';
      case 'poultry':
        return 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1000&auto=format&fit=crop';
      case 'fish':
        return 'https://images.unsplash.com/photo-1524704796725-9fc3044a58b2?q=80&w=1000&auto=format&fit=crop';
      default:
        return 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop';
    }
  };

  const handlePress = () => {
    router.push(`/records/${record.id}`);
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);

    // Use the appropriate locale based on the selected language
    const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';

    // Format the date according to the locale
    return date.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getSeverityColor = (severity?: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'low':
        return themedColors.secondary;
      case 'medium':
        return themedColors.warning;
      case 'high':
        return themedColors.error;
      default:
        return themedColors.textSecondary;
    }
  };

  const getRecordTypeIcon = (type: RecordType) => {
    switch (type) {
      case RecordType.VACCINATION:
        return <Syringe size={18} color={themedColors.primary} />;
      case RecordType.MEDICATION:
        return <Pill size={18} color={themedColors.primary} />;
      case RecordType.SURGERY:
        return <Scissors size={18} color={themedColors.primary} />;
      case RecordType.GENERAL:
        return <Stethoscope size={18} color={themedColors.primary} />;
      case RecordType.BIRTH:
        return <Baby size={18} color={themedColors.primary} />;
      case RecordType.DEATH:
        return <Skull size={18} color={themedColors.primary} />;
      default:
        return <HelpCircle size={18} color={themedColors.primary} />;
    }
  };

  const getRecordTypeColor = (type: RecordType) => {
    switch (type) {
      case RecordType.VACCINATION:
        return themedColors.primary;
      case RecordType.MEDICATION:
        return themedColors.secondary;
      case RecordType.SURGERY:
        return themedColors.error;
      case RecordType.GENERAL:
        return themedColors.success;
      case RecordType.BIRTH:
        return '#60A5FA'; // Light blue
      case RecordType.DEATH:
        return '#6B7280'; // Gray
      default:
        return themedColors.textSecondary;
    }
  };

  const recordTypeColor = getRecordTypeColor(record.type as RecordType);

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <LinearGradient
        colors={isDarkMode ?
          [themedColors.cardOpaque || 'rgba(55,65,81,0.8)', themedColors.cardTransparent || 'rgba(55,65,81,0.4)'] :
          [themedColors.cardOpaque || 'rgba(255,255,255,0.8)', themedColors.cardTransparent || 'rgba(255,255,255,0.4)']}
        style={styles.cardGradient}
      >
        <View style={styles.cardContent}>
          <View style={styles.cardLayout}>
            {/* Left side - Animal image */}
            <View style={styles.imageSection}>
              {animal && (
                animal.imageUri ? (
                  <Image
                    source={{ uri: animal.imageUri }}
                    style={styles.animalImage}
                    resizeMode="cover"
                  />
                ) : (
                  <Image
                    source={{ uri: getPlaceholderImage(animal.species) }}
                    style={styles.animalImage}
                    resizeMode="cover"
                  />
                )
              )}
            </View>

            {/* Right side - Content */}
            <View style={styles.contentSection}>
              <View style={[
                styles.header,
                language === 'ur' && { flexDirection: 'row-reverse' }
              ]}>
                <View style={[
                  styles.recordTypeIconContainer,
                  language === 'ur' ? {marginLeft: 10 } : {marginRight: 10}
                ]}>
                  {getRecordTypeIcon(record.type as RecordType)}
                </View>

                <View style={[
                  styles.headerContent,
                  language === 'ur' && {
                    flexDirection: 'row-reverse',
                    alignItems: 'center',
                    justifyContent: 'flex-start', // Push icon & text group to right
                    marginLeft: 10, // Add margin for Urdu to space from the icon container
                  } 
                ]}>
                  <View style={[
                    styles.typeContainer,
                    language === 'ur' && { flexDirection: 'row-reverse' }
                  ]}>
                    <Text style={[
                      styles.type,
                      { color: recordTypeColor, fontSize: 15 },
                      language === 'ur' && styles.urduText
                    ]}>
                      {record.type === RecordType.MEDICATION
                        ? t('records.medicationLabel')
                        : t(`records.${record.type.toLowerCase()}`) || record.type}
                    </Text>
                  </View>

                  {record.confirmedDisease && (
                    <View style={[
                      styles.severityBadge,
                      { backgroundColor: getSeverityColor(record.confirmedDisease.severity) }
                    ]}>
                      <AlertTriangle size={12} color="white" />
                      <Text style={[
                        styles.severityText,
                        language === 'ur' && styles.urduText
                      ]}>
                        {t(`records.severity.${record.confirmedDisease.severity}`).toUpperCase()}
                      </Text>
                    </View>
                  )}
                </View>
              </View>


              {showAnimalName && animalName && (
                <Text style={[styles.animalName, language === 'ur' ? styles.urduText : null]}>{animalName}</Text>
              )}

              <View style={styles.titleContainer}>
                <Text style={[styles.title, language === 'ur' ? styles.urduText : null]}>
                  {language === 'ur' ? t(`records.${record.title.toLowerCase().replace(/^.*?:/, "").replaceAll(' ', '')}`) : record.title.startsWith(record.type + ':')
                    ? record.title.substring(record.type.length + 1).trim()
                    : record.title}
                </Text>
              </View>

              {record.description && (
                <Text style={[styles.description, language === 'ur' ? styles.urduText : null]} numberOfLines={2}>
                  {record.description}
                </Text>
              )}

              {record.symptoms && record.symptoms.length > 0 && (
                <View style={[styles.symptomsContainer, isDarkMode && styles.symptomsContainerDark, language === 'ur' ? { alignItems: 'flex-end' } : null]}>
                  <Text style={[styles.symptomsLabel, language === 'ur' ? styles.urduText : null]}>{t('records.symptoms')}:</Text>
                  <Text style={[styles.symptoms, language === 'ur' ? styles.urduText : null]}>
                    {record.symptoms.map(s => s.name || s).join(', ')}
                  </Text>
                </View>
              )}

              <View style={[styles.dateContainer, language === 'ur' ? { flexDirection: 'row-reverse' } : null]}>
                <Calendar size={12} color={themedColors.textSecondary} />
                <Text style={[styles.date, { color: themedColors.textSecondary }, language === 'ur' ? styles.urduText : null]}>{formatDate(record.date)}</Text>
              </View>
            </View>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: themedColors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: themedColors.isDarkMode ? 0.2 : 0.1,
    shadowRadius: 3,
    elevation: themedColors.isDarkMode ? 1 : 2,
    overflow: 'hidden',
    maxHeight: 170,
  },
  cardGradient: {
    width: '100%',
    height: '100%',
  },
  cardContent: {
    padding: 12,
  },
  cardLayout: {
    flexDirection: 'row',
  },
  imageSection: {
    width: 70,
    height: 70,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 12,
  },
  animalImage: {
    width: '100%',
    height: '100%',
  },
  contentSection: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  recordTypeIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: themedColors.isDarkMode ? 'rgba(167, 139, 113, 0.2)' : 'rgba(79, 70, 229, 0.1)', // Themed icon background
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    alignSelf: 'flex-end',
    marginTop: 6,
  },
  date: {
    fontSize: 12,
    // color set dynamically
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  severityText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  animalName: {
    fontSize: 15,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 6,
  },
  titleContainer: {
    marginBottom: 6,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  type: {
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  description: {
    fontSize: 13,
    color: themedColors.text,
    marginBottom: 8,
    lineHeight: 18,
  },
  symptomsContainer: {
    marginTop: 2,
    marginBottom: 6,
    backgroundColor: themedColors.isDarkMode ? 'rgba(239, 68, 68, 0.15)' : 'rgba(254, 226, 226, 0.3)', // Themed symptoms background
    padding: 8,
    borderRadius: 6,
  },
  symptomsLabel: {
    fontSize: 13,
    color: themedColors.textSecondary,
    marginBottom: 2,
    fontWeight: '500',
  },
  symptoms: {
    fontSize: 13,
    color: themedColors.text,
  },
  // urduText: {
  //   fontFamily: 'System',
  //   textAlign: 'right',
  // },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
    writingDirection: 'rtl',
  },


});

// Add default export
export default RecordCard;
