import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Text, Image } from 'react-native';
import { Redirect, router } from 'expo-router';
import { useAuthStore } from '@/store/auth-store';
import { colors } from '@/constants/colors';
import LoadingIndicator from '@/components/LoadingIndicator';
import { LinearGradient } from 'expo-linear-gradient';

export default function AuthIndex() {
  const { isAuthenticated, user } = useAuthStore();
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  useEffect(() => {
    console.log('Auth index mounted');

    // Simple timeout to show splash briefly then proceed
    const timeout = setTimeout(() => {
      setInitialCheckDone(true);
    }, 1500);

    return () => clearTimeout(timeout);
  }, []);

  // Show loading screen briefly
  if (!initialCheckDone) {
    return (
      <LinearGradient
        colors={['#2C3E50', '#4A5568']}
        style={styles.container}
      >
        <View style={styles.loadingContent}>
          <Image
            source={require('../../assets/images/ll.png')}
            style={styles.logo}
          />
          <Text style={styles.title}>Livestock Tracker</Text>
          <Text style={styles.subtitle}>Welcome back...</Text>
        </View>
        <LoadingIndicator message="Loading..." />
      </LinearGradient>
    );
  }

  // If user is authenticated and verified, redirect to main app
  if (isAuthenticated && user?.emailVerified) {
    console.log('User is authenticated, redirecting to main app');
    return <Redirect href="/(tabs)" />;
  }

  // Otherwise show login
  console.log('Showing login screen');
  return <Redirect href="/(auth)/login" />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: '#A78B71',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#E0E0E0',
    marginBottom: 24,
  },
});