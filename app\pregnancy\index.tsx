import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { usePregnancyStore, Pregnancy } from '@/store/pregnancy-store';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { useAnimalStore } from '@/store/animal-store';
import { Plus, Calendar, Search } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import EmptyState from '@/components/EmptyState';
// import SearchInput from '@/components/SearchInput';
import GenericDropdown from '@/components/GenericDropdown';
// Removing SpeciesFilterRow import

interface DropdownItem {
  id: string;
  label: string;
}

export default function PregnancyListScreen() {
  const router = useRouter();
  const colors = useThemeColors();
  const { t, language } = useTranslation();
  const { user } = useAuthStore();
  const { farms } = useFarmStore();
  const { pregnancies, fetchPregnancies, loading } = usePregnancyStore();
  const { animals, fetchAnimals } = useAnimalStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFarm, setSelectedFarm] = useState<string | null>(null);
  // Removing selectedSpecies state
  const [filteredPregnancies, setFilteredPregnancies] = useState<Pregnancy[]>([]);
  const [farmOptions, setFarmOptions] = useState<DropdownItem[]>([]);
  const [femaleAnimals, setFemaleAnimals] = useState<any[]>([]);
  const [selectedFemaleAnimal, setSelectedFemaleAnimal] = useState<any | null>(null);
  
  // Prepare farm options for dropdown
  useEffect(() => {
    if (farms && farms.length > 0) {
      const options = farms.map(farm => ({
        id: farm.id,
        label: farm.name
      }));
      setFarmOptions(options);
      
      // Auto-select the first farm if none is selected
      if (!selectedFarm && options.length > 0) {
        setSelectedFarm(options[0].id);
      }
    }
  }, [farms, selectedFarm]);
  
  // Fetch pregnancies and animals when farm is selected
  useEffect(() => {
    if (selectedFarm) {
      fetchPregnancies(selectedFarm);
      fetchAnimals(selectedFarm);
    }
  }, [selectedFarm, fetchPregnancies, fetchAnimals]);
  
  // Prepare female animals for dropdown
  useEffect(() => {
    if (animals && animals.length > 0) {
      // Filter for female animals
      const females = animals.filter(animal => 
        animal.gender === 'female'
      );
      
      const femaleOptions = females.map(animal => ({
        id: animal.id,
        label: animal.name || animal.tagId || 'Unknown',
        imageUri: animal.imageUri ? { uri: animal.imageUri } : getPlaceholderImage(animal.species)
      }));
      
      setFemaleAnimals(femaleOptions);
    } else {
      setFemaleAnimals([]);
    }
  }, [animals]);
  
  // Filter pregnancies based on search query only (no species filter)
  useEffect(() => {
    if (!pregnancies) {
      setFilteredPregnancies([]);
      return;
    }
    
    let filtered = [...pregnancies];
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(pregnancy => 
        pregnancy.animalName.toLowerCase().includes(query) ||
        pregnancy.sireName.toLowerCase().includes(query)
      );
    }
    
    setFilteredPregnancies(filtered);
  }, [pregnancies, searchQuery]);
  
  // Get placeholder image for animals without an image
  const getPlaceholderImage = (species) => {
    const placeholderUrl = species?.toLowerCase() === 'cow' 
      ? 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D'
      : species?.toLowerCase() === 'goat'
      ? 'https://images.unsplash.com/photo-1524024973431-2ad916746881?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8Z29hdCUyMGZhY2V8ZW58MHx8MHx8fDA%3D'
      : species?.toLowerCase() === 'poultry'
      ? 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8Y2hpY2tlbiUyMGZhY2V8ZW58MHx8MHx8fDA%3D'
      : species?.toLowerCase() === 'fish'
      ? 'https://images.unsplash.com/photo-1524704796725-9fc3044a58b2?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGZpc2glMjBmYWNlfGVufDB8fDB8fHww'
      : 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D';
    
    return { uri: placeholderUrl };
  };
  
  const handleAddPregnancy = () => {
    router.push('/pregnancy/add');
  };
  
  const handlePregnancyPress = (pregnancy: Pregnancy) => {
    router.push(`/pregnancy/details/${pregnancy.id}`);
  };
  
  const renderPregnancyItem = ({ item }: { item: Pregnancy }) => {
    // Calculate progress percentage
    const progress = item.progress || 0;
    
    // Determine color based on pregnancy stage
    let progressColors = ['#4CAF50', '#8BC34A', '#CDDC39']; // Default colors
    
    if (item.status === 'not_pregnant') {
      progressColors = ['#F44336', '#FF5722', '#FF9800']; // Red to orange for not pregnant
    } else if (item.status === 'suspected') {
      progressColors = ['#9C27B0', '#673AB7', '#3F51B5']; // Purple to blue for suspected
    }
    
    return (
      <TouchableOpacity
        style={[styles.pregnancyCard, { backgroundColor: colors.card }]}
        onPress={() => handlePregnancyPress(item)}
      >
        <View style={styles.cardHeader}>
          <Text style={[styles.animalName, { color: colors.text }]}>
            {item.animalName}
          </Text>
          <View style={styles.statusBadge}>
            <Text style={styles.statusText}>
              {t(`pregnancy.${item.status}`)}
            </Text>
          </View>
        </View>
        
        <View style={styles.cardDetails}>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              {t('pregnancy.sire')}:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {item.sireName}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              {t('pregnancy.species')}:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {item.species}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              {t('pregnancy.expectedDate')}:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {item.expectedDate}
            </Text>
          </View>
        </View>
        
        <View style={styles.progressContainer}>
          <LinearGradient
            colors={progressColors}
            style={[styles.progressBar, { width: `${progress}%` }]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          />
          <Text style={styles.progressText}>{`${progress}%`}</Text>
        </View>
      </TouchableOpacity>
    );
  };
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: 16,
      backgroundColor: colors.card,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    searchContainer: {
      marginBottom: 16,
    },
    filterContainer: {
      marginBottom: 8,
    },
    farmDropdownContainer: {
      marginBottom: 16,
    },
    animalDropdownContainer: {
      marginBottom: 16,
    },
    contentContainer: {
      flex: 1,
      padding: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 10,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    pregnancyCard: {
      borderRadius: 8,
      marginBottom: 16,
      padding: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    cardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    animalName: {
      fontSize: 18,
      fontWeight: '600',
    },
    statusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      backgroundColor: colors.primary,
    },
    statusText: {
      color: 'white',
      fontSize: 12,
      fontWeight: '500',
    },
    cardDetails: {
      marginBottom: 12,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 4,
    },
    detailLabel: {
      fontSize: 14,
    },
    detailValue: {
      fontSize: 14,
      fontWeight: '500',
    },
    progressContainer: {
      height: 20,
      backgroundColor: colors.border,
      borderRadius: 10,
      overflow: 'hidden',
    },
    progressBar: {
      height: '100%',
    },
    progressText: {
      position: 'absolute',
      width: '100%',
      textAlign: 'center',
      color: 'white',
      fontWeight: '600',
      fontSize: 12,
      lineHeight: 20,
    },
    addButton: {
      position: 'absolute',
      bottom: 20,
      right: 20,
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    urduText: {
      fontFamily: 'UrduFont',
    },
  });
  
  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: t('pregnancy.title'),
          headerBackTitle: t('common.back')
        }} 
      />
      
      <View style={styles.header}>
        <View style={styles.farmDropdownContainer}>
          <GenericDropdown
            placeholder={t('farms.selectFarmPlaceholder')}
            items={farmOptions}
            value={selectedFarm}
            onSelect={(id) => {
              setSelectedFarm(id);
              setSelectedFemaleAnimal(null); // Reset animal selection when farm changes
            }}
            modalTitle={t('farms.selectFarm')}
            searchPlaceholder={t('farms.searchFarms')}
            renderIcon={false}
          />
        </View>
        
        <View style={styles.animalDropdownContainer}>
          <GenericDropdown
            placeholder={t('pregnancy.selectFemalePlaceholder')}
            items={femaleAnimals}
            value={selectedFemaleAnimal?.id}
            onSelect={(id) => {
              const selected = femaleAnimals.find(animal => animal.id === id);
              setSelectedFemaleAnimal(selected || null);
            }}
            modalTitle={t('pregnancy.selectFemale')}
            searchPlaceholder={t('pregnancy.searchFemales')}
            renderIcon={false}
          />
        </View>
        
        {/* Removing the species filter row */}
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : filteredPregnancies.length > 0 ? (
        <FlatList
          data={filteredPregnancies}
          renderItem={renderPregnancyItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.contentContainer}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <EmptyState
            icon={<Calendar size={48} color={colors.primary} />}
            title={t('pregnancy.noPregnancies')}
            message={t('pregnancy.noPregnanciesMessage')}
            actionLabel={t('pregnancy.addPregnancy')}
            onAction={handleAddPregnancy}
          />
        </View>
      )}
      
      <TouchableOpacity style={styles.addButton} onPress={handleAddPregnancy}>
        <Plus size={24} color="white" />
      </TouchableOpacity>
    </View>
  );
}









