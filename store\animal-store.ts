import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { collection, doc, getDoc, getDocs, addDoc, updateDoc, deleteDoc, query, where } from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { firestore } from '@/config/firebase';
import { Animal } from '@/types/animal';

interface AnimalState {
  animals: Animal[];
  isLoading: boolean;
  error: string | null;
  fetchAnimals: (userId: string) => Promise<Animal[]>;
  fetchAnimalsByFarm: (farmId: string) => Promise<Animal[]>;
  getAnimal: (id: string) => Animal | undefined;
  addAnimal: (animal: Omit<Animal, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Animal>;
  updateAnimal: (id: string, updates: Partial<Animal>) => Promise<Animal | undefined>;
  deleteAnimal: (id: string) => Promise<void>;
  updateAnimalNextHealthCheck: (animalId: string, nextCheckDate: number) => Promise<void>;
  clearAnimals: () => void;
}

export const useAnimalStore = create<AnimalState>()(
  persist(
    (set, get) => ({
      animals: [],
      isLoading: false,
      error: null,
      fetchAnimals: async (userId: string) => {
        set({ isLoading: true, error: null });
        try {
          // Get the user document to check role and farm associations
          const userDoc = await getDoc(doc(firestore, 'users', userId));
          
          if (!userDoc.exists()) {
            set({ isLoading: false, error: 'User not found' });
            return [];
          }

          const userData = userDoc.data();
          const userRole = userData.role;
          
          const fetchedAnimals: Animal[] = [];
          
          // Get all farms associated with this user
          let userFarms: string[] = [];
          
          // If user is owner, get owned farms
          if (userData.ownedFarms && userData.ownedFarms.length > 0) {
            userFarms = [...userFarms, ...userData.ownedFarms];
          }
          
          // If user has assigned farms, add those too
          if (userData.assignedFarms && userData.assignedFarms.length > 0) {
            userFarms = [...userFarms, ...userData.assignedFarms];
          }
          
          // Remove duplicates
          const uniqueFarms = [...new Set(userFarms)];

          if (uniqueFarms.length === 0) {
            set({ animals: [], isLoading: false });
            return [];
          }
          
          // Fetch animals from each farm's subcollection
          for (const farmId of uniqueFarms) {
            try {
              const farmRef = doc(firestore, 'farms', farmId);
              const animalsCollectionRef = collection(farmRef, 'animals');
              const animalsSnapshot = await getDocs(animalsCollectionRef);

              animalsSnapshot.forEach((doc) => {
                fetchedAnimals.push({ 
                  id: doc.id, 
                  ...doc.data(),
                  farmId: farmId // Ensure farmId is set
                } as Animal);
              });
            } catch (farmError) {
              // Continue with other farms even if one fails
            }
          }
          
          set({ animals: fetchedAnimals, isLoading: false });
          return fetchedAnimals;
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch animals', 
            isLoading: false,
            animals: [] // Clear animals on error
          });
          return [];
        }
      },
      
      fetchAnimalsByFarm: async (farmId: string) => {
        set({ isLoading: true, error: null });
        try {
          // Try to fetch from farm subcollection first (this is the correct path based on your code)
          const farmAnimalsRef = collection(firestore, `farms/${farmId}/animals`);
          const farmAnimalsSnapshot = await getDocs(farmAnimalsRef);

          let fetchedAnimals: Animal[] = [];

          if (farmAnimalsSnapshot.empty) {
            // If no animals in farm subcollection, try top-level collection
            const animalsRef = collection(firestore, 'animals');
            const q = query(animalsRef, where("farmId", "==", farmId));
            const querySnapshot = await getDocs(q);

            querySnapshot.forEach((doc) => {
              fetchedAnimals.push({ id: doc.id, ...doc.data() } as Animal);
            });
          } else {
            farmAnimalsSnapshot.forEach((doc) => {
              fetchedAnimals.push({
                id: doc.id,
                ...doc.data(),
                farmId: farmId // Ensure farmId is set
              } as Animal);
            });
          }

          // Update the store with the fetched animals
          set(state => ({
            animals: [...state.animals.filter(a => a.farmId !== farmId), ...fetchedAnimals],
            isLoading: false
          }));

          return fetchedAnimals;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch animals',
            isLoading: false
          });
          return [];
        }
      },

      getAnimal: (id: string) => {
        return get().animals.find(animal => animal.id === id);
      },

      addAnimal: async (animalData) => {
        set({ isLoading: true, error: null });
        try {
          // Create a clean data object without undefined values
          const cleanData: Record<string, any> = {};

          // Only add defined properties to the clean data object
          Object.entries(animalData).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              cleanData[key] = value;
            }
          });

          // Ensure required fields are present
          if (!cleanData.name || !cleanData.species || !cleanData.ownerId || !cleanData.farmId) {
            throw new Error('Missing required fields: name, species, ownerId, or farmId');
          }

          // Handle image upload if there's an imageUri
          if (cleanData.imageUri) {
            try {
              const storage = getStorage();
              const timestamp = Date.now();
              const imagePath = `animals/${cleanData.ownerId}/${timestamp}.jpg`;
              const imageRef = ref(storage, imagePath);
              
              let blob: Blob;
              
              if (cleanData.imageUri.startsWith('data:image')) {
                // Handle base64 images
                const response = await fetch(cleanData.imageUri);
                blob = await response.blob();
              } else if (cleanData.imageUri.startsWith('file:') || 
                        cleanData.imageUri.startsWith('content:') || 
                        cleanData.imageUri.startsWith('ph:')) {
                // Handle file/content URIs
                const response = await fetch(cleanData.imageUri);
                blob = await response.blob();
              } else {
                throw new Error('Unsupported image URI format');
              }

              // Upload with metadata
              const metadata = {
                contentType: 'image/jpeg',
                customMetadata: {
                  'uploaded-by': cleanData.ownerId,
                  'timestamp': timestamp.toString()
                }
              };

              await uploadBytes(imageRef, blob, metadata);
              
              // Get download URL
              const downloadURL = await getDownloadURL(imageRef);
              cleanData.imageUri = downloadURL;
              
            } catch (error) {
              // Image upload failed, continue without image
            }
          }

          const timestamp = Date.now();
          const newAnimalData = {
            ...cleanData,
            createdAt: timestamp,
            updatedAt: timestamp,
          };

          // Add to Firestore as subcollection under farm
          const farmRef = doc(firestore, 'farms', cleanData.farmId);
          const animalCollectionRef = collection(farmRef, 'animals');
          const docRef = await addDoc(animalCollectionRef, newAnimalData);

          // Create the complete animal object
          const newAnimal: Animal = {
            id: docRef.id,
            ...newAnimalData,
          } as Animal;

          // Update the farm document with animal count
          try {
            const farmDoc = await getDoc(farmRef);
            
            if (farmDoc.exists()) {
              const farmData = farmDoc.data();
              await updateDoc(farmRef, {
                animalCount: (farmData.animalCount || 0) + 1,
                updatedAt: timestamp
              });
            }
          } catch (farmError) {
            // Farm update failed, continue
          }

          // Update local state
          set(state => ({
            animals: [...state.animals, newAnimal],
            isLoading: false,
          }));

          return newAnimal;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to add animal', 
            isLoading: false 
          });
          throw error;
        }
      },

      updateAnimal: async (id: string, updates: Partial<Animal>) => {
        set({ isLoading: true, error: null });
        try {
          // Get the current animal to find its farmId
          const currentAnimal = get().animals.find(a => a.id === id);
          if (!currentAnimal || !currentAnimal.farmId) {
            throw new Error('Animal not found or missing farmId');
          }
          
          const farmId = updates.farmId || currentAnimal.farmId;
          const animalRef = doc(firestore, 'farms', farmId, 'animals', id);
          
          const updatedData = {
            ...updates,
            updatedAt: Date.now(),
          };

          // Update Firestore
          await updateDoc(animalRef, updatedData);

          // Immediately update local state
          set(state => ({
            animals: state.animals.map(animal =>
              animal.id === id ? { ...animal, ...updatedData } : animal
            ),
            isLoading: false,
          }));

          // Get the updated animal
          const updatedAnimal = get().animals.find(a => a.id === id);
          return updatedAnimal;
        } catch (error) {
          set({ error: 'Failed to update animal', isLoading: false });
          throw error;
        }
      },

      deleteAnimal: async (id) => {
        set({ isLoading: true, error: null });
        try {
          // Get the current animal to find its farmId
          const currentAnimal = get().animals.find(a => a.id === id);
          if (!currentAnimal || !currentAnimal.farmId) {
            throw new Error('Animal not found or missing farmId');
          }
          
          // Delete from Firestore subcollection
          const animalRef = doc(firestore, 'farms', currentAnimal.farmId, 'animals', id);
          await deleteDoc(animalRef);

          // Update local state
          set(state => ({
            animals: state.animals.filter(animal => animal.id !== id),
            isLoading: false,
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to delete animal',
            isLoading: false
          });
          throw error;
        }
      },

      updateAnimalNextHealthCheck: async (animalId, nextCheckDate) => {
        try {
          const animal = get().getAnimal(animalId);
          if (animal) {
            await get().updateAnimal(animalId, {
              nextHealthCheck: nextCheckDate,
              updatedAt: Date.now()
            });
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update next health check date'
          });
          throw error;
        }
      },

      clearAnimals: () => {
        set({ animals: [], error: null });
      },
    }),
    {
      name: 'animal-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);


