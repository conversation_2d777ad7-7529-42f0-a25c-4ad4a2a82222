{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --tunnel", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel", "android": "expo run:android", "ios": "expo run:ios", "postinstall": "patch-package"}, "dependencies": {"@expo/config-plugins": "~9.0.0", "@expo/vector-icons": "^14.0.2", "@firebase/app": "^0.11.4", "@firebase/auth": "^1.10.0", "@firebase/firestore": "^4.7.10", "@firebase/storage": "^0.13.7", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-navigation/native": "^7.0.0", "cors-anywhere": "^0.4.4", "date-fns": "^4.1.0", "expo": "~52.0.36", "expo-av": "^15.0.2", "expo-blur": "~14.0.1", "expo-clipboard": "^7.0.1", "expo-constants": "~17.0.7", "expo-dev-client": "^5.0.20", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "^14.0.1", "expo-image": "~2.0.6", "expo-image-picker": "^16.0.6", "expo-linear-gradient": "^14.0.2", "expo-linking": "~7.0.3", "expo-location": "~18.0.7", "expo-media-library": "~17.0.6", "expo-router": "~4.0.21", "expo-speech": "~13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.6", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.1", "firebase": "^11.6.0", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "patch-package": "^8.0.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-toast-message": "^2.3.0", "react-native-web": "~0.19.13", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~18.3.12", "typescript": "~5.8.2"}, "resolutions": {"expo-permissions": "~14.4.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["@firebase/app", "@firebase/auth", "@firebase/firestore", "@firebase/storage", "firebase"]}}}, "private": true}