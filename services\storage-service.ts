import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';

/**
 * Uploads an image to Firebase Storage
 * @param uri Local URI of the image
 * @param path Path in Firebase Storage where the image should be stored
 * @returns Promise that resolves to the download URL of the uploaded image
 */
export const uploadImage = async (uri: string, path: string): Promise<string> => {
  try {
    console.log('Starting image upload. URI:', uri);
    
    // Get a reference to the storage service
    const storage = getStorage();
    
    // Create a storage reference with file extension
    const storageRef = ref(storage, path);
    
    // Convert URI to blob
    let blob;
    
    if (Platform.OS === 'web') {
      // For web, fetch the image and convert to blob
      console.log('Web platform: Fetching image...');
      const response = await fetch(uri);
      blob = await response.blob();
    } else {
      // For native platforms (iOS/Android)
      console.log('Native platform detected');
      
      try {
        // First try direct fetch
        console.log('Attempting direct fetch...');
        const response = await fetch(uri);
        blob = await response.blob();
        console.log('Direct fetch successful');
      } catch (fetchError) {
        console.error('Direct fetch failed:', fetchError);
        
        try {
          // If direct fetch fails, try reading as base64
          console.log('Attempting base64 conversion...');
          const base64 = await FileSystem.readAsStringAsync(uri, {
            encoding: FileSystem.EncodingType.Base64,
          });
          console.log('Read file as base64, length:', base64.length);
          
          const response = await fetch(`data:image/jpeg;base64,${base64}`);
          blob = await response.blob();
          console.log('Base64 conversion successful');
        } catch (base64Error) {
          console.error('Base64 conversion failed:', base64Error);
          throw new Error('Failed to process image: ' + base64Error.message);
        }
      }
    }
    
    if (!blob) {
      throw new Error('Failed to create blob from image');
    }
    
    console.log('Image blob created, size:', blob.size);
    console.log('Uploading to path:', path);
    
    // Upload the blob
    const snapshot = await uploadBytes(storageRef, blob);
    console.log('Uploaded image to:', snapshot.ref.fullPath);
    
    // Get the download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    console.log('Download URL:', downloadURL);
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

/**
 * Deletes an image from Firebase Storage
 * @param url URL of the image to delete
 * @returns Promise that resolves when the image is deleted
 */
export const deleteImage = async (url: string): Promise<void> => {
  try {
    // Get a reference to the storage service
    const storage = getStorage();
    
    // Extract the path from the URL
    const path = url.split('?')[0].split('/o/')[1].replace(/%2F/g, '/');
    
    // Create a storage reference
    const storageRef = ref(storage, decodeURIComponent(path));
    
    // Delete the file
    await deleteObject(storageRef);
    console.log('Deleted image:', path);
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
};