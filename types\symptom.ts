export interface Symptom {
  id: string;
  name: string;
  description?: string;
  bodyPart: string;
  severity: 'low' | 'medium' | 'high';
}

export interface Disease {
  id: string;
  name: string;
  description: string;
  symptoms: string[]; // Array of symptom IDs
  treatment: string;
  prevention: string;
  zoonotic: boolean;
  severity: 'low' | 'medium' | 'high';
  species: string[]; // Array of affected species
}
