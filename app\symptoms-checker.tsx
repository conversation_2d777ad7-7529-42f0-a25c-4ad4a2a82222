import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/hooks/useTranslation';
import SymptomsScreen from '@/components/SymptomsScreen';
import { useThemeColors } from '@/hooks/useThemeColors';

export default function SymptomsCheckerScreen() {
  const { t } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen 
        options={{ 
          title: t('symptoms.title'),
          headerBackTitle: t('common.back')
        }} 
      />
      <SymptomsScreen />
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
});
