import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  Platform,
  ScrollView,
} from 'react-native';
import { ChevronLeft, ChevronRight, X } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import { TouchableOpacity } from 'react-native'; // Ensure TouchableOpacity is imported

interface DatePickerInputProps {
  label?: string;
  value?: Date;
  date?: Date; // Alternative prop name for compatibility
  onChange?: (date: Date) => void;
  onDateChange?: (date: Date) => void; // Alternative prop name for compatibility
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  icon?: React.ReactNode; // Alternative prop name for compatibility
  error?: string;
  helperText?: string;
}

const DatePickerInput: React.FC<DatePickerInputProps> = ({
  label,
  value,
  date,
  onChange,
  onDateChange,
  leftIcon,
  rightIcon,
  icon,
  error,
  helperText,
}) => {
  // Handle alternative prop names for compatibility
  const actualDate = date || value || new Date();
  const handleDateChange = onDateChange || onChange || (() => {});
  const actualLeftIcon = leftIcon || icon;
  const { t, language } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const themedColors = useThemeColors();
  const [tempDate, setTempDate] = useState(new Date(actualDate)); // Ensure tempDate is a new Date object
  const [currentView, setCurrentView] = useState<'calendar' | 'year'>('calendar');

  // Update tempDate when the value prop changes
  React.useEffect(() => {
    setTempDate(new Date(actualDate));
  }, [actualDate]);

  // Format date for display
  const formatDate = (date: Date | undefined): string => {
    if (!date) return '';

    // Use different date formats based on language
    const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';

    try {
      return date.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Get month name
  const getMonthName = (date: Date): string => {
    const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';
    return date.toLocaleDateString(locale, { month: 'long' });
  };

  // Get days in month
  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Get day of week for first day of month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = (year: number, month: number): number => {
    return new Date(year, month, 1).getDay();
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = tempDate.getFullYear();
    const month = tempDate.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i);
    }

    return days;
  };

  // Generate years for year picker
  const generateYears = () => {
    const currentYear = new Date().getFullYear();
    const years = [];

    // Show 100 years (50 years before and 50 years after current year)
    for (let i = currentYear - 50; i <= currentYear + 50; i++) {
      years.push(i);
    }

    return years;
  };

  // Handle month navigation
  const handlePrevMonth = () => {
    const newDate = new Date(tempDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setTempDate(newDate);
  };

  const handleNextMonth = () => {
    const newDate = new Date(tempDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setTempDate(newDate);
  };

  // Handle day selection
  const handleDaySelect = (day: number) => {
    const newDate = new Date(tempDate);
    newDate.setDate(day);
    setTempDate(newDate);
  };

  // Handle year selection
  const handleYearSelect = (year: number) => {
    const newDate = new Date(tempDate);
    newDate.setFullYear(year);
    setTempDate(newDate);
    setCurrentView('calendar');
  };

  // Handle confirm date
  const handleConfirm = () => {
    handleDateChange(tempDate);
    setModalVisible(false);
  };

  // Handle cancel
  const handleCancel = () => {
    setTempDate(actualDate);
    setModalVisible(false);
  };

  // Check if a day is the currently selected day
  const isSelectedDay = (day: number) => {
    return (
      tempDate.getDate() === day &&
      tempDate.getMonth() === tempDate.getMonth() &&
      tempDate.getFullYear() === tempDate.getFullYear()
    );
  };

  // Check if a year is the currently selected year
  const isSelectedYear = (year: number) => {
    return tempDate.getFullYear() === year;
  };

  // Check if a day is today
  const isToday = (day: number) => {
    const today = new Date();
    return (
      today.getDate() === day &&
      today.getMonth() === tempDate.getMonth() &&
      today.getFullYear() === tempDate.getFullYear()
    );
  };

  // Memoize styles to prevent re-creation on every render unless colors or language change
  const styles = React.useMemo(() => getStyles(themedColors, language, t), [themedColors, language, t]);

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}

      <TouchableOpacity
        style={[
          styles.input,
          error ? styles.inputError : null,
        ]}
        onPress={() => setModalVisible(true)}
      >
        {actualLeftIcon && <View style={styles.leftIcon}>{actualLeftIcon}</View>}
        <Text style={styles.inputText}>{formatDate(actualDate)}</Text>
        {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}
      </TouchableOpacity>
      {error && <Text style={[styles.errorText, { color: themedColors.error }, language === 'ur' ? styles.urduText : null]}>{t(error)}</Text>}
      {helperText && !error && <Text style={[styles.helperText, { color: themedColors.textSecondary }, language === 'ur' ? styles.urduText : null]}>{t(helperText)}</Text>}

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancel}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('common.selectDate')}</Text>
              <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
                <X size={24} color={themedColors.text} />
              </TouchableOpacity>
            </View>

            {currentView === 'calendar' ? (
              <View style={styles.calendarContainer}>
                <View style={styles.calendarHeader}>
                  <TouchableOpacity
                    style={styles.monthYearButton}
                    onPress={() => setCurrentView('year')}
                  >
                    <Text style={styles.monthYearText}>
                      {getMonthName(tempDate)} {tempDate.getFullYear()}
                    </Text>
                  </TouchableOpacity>
                  <View style={styles.navigationButtons}>
                    <TouchableOpacity
                      style={[styles.navButton, { backgroundColor: themedColors.background }]}
                      onPress={handlePrevMonth}
                    >
                      <ChevronLeft size={24} color={themedColors.text} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.navButton, { backgroundColor: themedColors.background }]}
                      onPress={handleNextMonth}
                    >
                      <ChevronRight size={24} color={themedColors.text} />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.weekdaysContainer}>
                  {[
                    t('common.weekdays.sun'),
                    t('common.weekdays.mon'),
                    t('common.weekdays.tue'),
                    t('common.weekdays.wed'),
                    t('common.weekdays.thu'),
                    t('common.weekdays.fri'),
                    t('common.weekdays.sat')
                  ].map((day, index) => (
                    <Text key={index} style={styles.weekdayText}>{day}</Text>
                  ))}
                </View>

                <View style={styles.daysContainer}>
                  {generateCalendarDays().map((day, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.dayButton,
                        day === null ? styles.emptyDay : null,
                        isSelectedDay(day as number) ? [styles.selectedDay, { backgroundColor: themedColors.primary }] : null,
                        isToday(day as number) ? styles.todayDay : null,
                      ]}
                      onPress={() => day !== null && handleDaySelect(day as number)}
                      disabled={day === null}
                    >
                      {day !== null && (
                        <Text style={[
                          styles.dayText,
                          isSelectedDay(day as number) ? styles.selectedDayText : null,
                          isToday(day as number) ? [styles.todayDayText, { color: themedColors.primary, borderColor: themedColors.primary }] : null,
                        ]}>
                          {day}
                        </Text>
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            ) : (
              <ScrollView style={styles.yearPickerContainer}>
                <View style={styles.yearsGrid}>
                  {generateYears().map((year) => (
                    <TouchableOpacity
                      key={year}
                      style={[
                        styles.yearButton,
                        isSelectedYear(year) ? [styles.selectedYear, { backgroundColor: themedColors.primary }] : null,
                      ]}
                      onPress={() => handleYearSelect(year)}
                    >
                      <Text style={[
                        styles.yearText,
                        isSelectedYear(year) ? styles.selectedYearText : null,
                      ]}>
                        {year}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            )}
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton} // Combined footerButton and cancelButton styles
                onPress={handleCancel}
              >
                <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmButton} // Combined footerButton and confirmButton styles
                onPress={handleConfirm}
              >
                <Text style={styles.confirmButtonText}>{t('common.confirm')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string, t: (key: string) => string) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: themedColors.text,
    fontWeight: '500',
    ...(language === 'ur' && { textAlign: 'right' }),
  },
  input: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    borderWidth: 1, 
    borderColor: themedColors.border, // Changed from colors.border
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: themedColors.card,
  },
  inputError: {
    borderColor: themedColors.error,
  },
  leftIcon: {
    marginRight: 10,
  },
  rightIcon: {
    marginLeft: 10,
  },
  inputText: {
    flex: 1,
    fontSize: 16,
    color: themedColors.text,
    ...(language === 'ur' && { textAlign: 'right' }),
  },
  errorText: {
    color: themedColors.error,
    fontSize: 14,
    marginTop: 4,
    ...(language === 'ur' && { textAlign: 'right' }),
  },
  helperText: {
    color: themedColors.textSecondary,
    fontSize: 14,
    marginTop: 4,
    ...(language === 'ur' && { textAlign: 'right' }),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 340,
    backgroundColor: themedColors.card,
    borderRadius: 16,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: themedColors.text,
    ...(language === 'ur' && { textAlign: 'right' }),
  },
  closeButton: {
    padding: 4,
  },
  calendarContainer: {
    padding: 16,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  monthYearButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: themedColors.primaryLight,
  },
  monthYearText: {
    fontSize: 16,
    fontWeight: '600',
    color: themedColors.primary,
    ...(language === 'ur' && { textAlign: 'right' }),
  },
  navigationButtons: {
    flexDirection: 'row',
  },
  navButton: {
    padding: 8,
    borderRadius: 8,
    marginLeft: 8,
    backgroundColor: themedColors.background,
  },
  weekdaysContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  weekdayText: {
    flex: 1,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '500',
    color: themedColors.textSecondary,
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dayButton: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 4,
  },
  emptyDay: {
    // Empty day cell
  },
  dayText: {
    fontSize: 16,
    color: themedColors.text,
  },
  selectedDay: {
    backgroundColor: themedColors.primary,
    borderRadius: 20,
  },
  selectedDayText: {
    color: 'white',
    fontWeight: '600',
  },
  todayDay: {
    borderWidth: 1,
    borderColor: themedColors.primary,
    borderRadius: 20,
  },
  todayDayText: {
    // color: themedColors.primary, // This is already applied in JSX for dynamic color
    fontWeight: '600',
  },
  yearPickerContainer: {
    maxHeight: 300,
  },
  yearsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    justifyContent: 'center',
  },
  yearButton: {
    width: '25%',
    padding: 12,
    alignItems: 'center',
  },
  yearText: {
    fontSize: 16,
    color: themedColors.text,
  },
  selectedYear: {
    backgroundColor: themedColors.primary,
    borderRadius: 20,
  },
  selectedYearText: {
    color: 'white',
    fontWeight: '600',
  },
  modalFooter: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: themedColors.border,
  },
  footerButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  cancelButton: {
    flex: 1, // Added from footerButton
    padding: 16, // Added from footerButton
    alignItems: 'center', // Added from footerButton
    borderRightWidth: 0.5,
    borderRightColor: themedColors.border,
  },
  confirmButton: {
    flex: 1, // Added from footerButton
    padding: 16, // Added from footerButton
    alignItems: 'center', // Added from footerButton
    borderLeftWidth: 0.5,
    borderLeftColor: themedColors.border,
    backgroundColor: themedColors.primaryLight,
  },
  cancelButtonText: {
    fontSize: 16,
    color: themedColors.text,
    fontWeight: '500',
  },
  confirmButtonText: {
    fontSize: 16,
    color: themedColors.primary,
    fontWeight: '600',
  },
});

export default DatePickerInput;