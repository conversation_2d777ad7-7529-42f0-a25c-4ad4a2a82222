import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Image,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAnimalStore } from '@/store/animal-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import HealthCheckCard from '@/components/HealthCheckCard';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { Plus, Activity, ArrowLeft } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function AnimalHealthChecksScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getAnimal } = useAnimalStore();
  const { getHealthChecksByAnimalId, fetchHealthChecks, isLoading } = useHealthCheckStore();

  const [animal, setAnimal] = useState(getAnimal(id));
  const [healthChecks, setHealthChecks] = useState(getHealthChecksByAnimalId(id));
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    setAnimal(getAnimal(id));
    loadHealthChecks();
  }, [id]);

  const loadHealthChecks = async () => {
    await fetchHealthChecks(id);
    setHealthChecks(getHealthChecksByAnimalId(id));
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadHealthChecks();
    setRefreshing(false);
  };

  const handleAddHealthCheck = () => {
    router.push({
      pathname: '/health-checks/add',
      params: { animalId: id }
    });
  };

  if (isLoading && !refreshing) {
    return <LoadingIndicator fullScreen message="Loading health checks..." />;
  }

  if (!animal) {
    return (
      <EmptyState
        title="Animal Not Found"
        message="The animal you're looking for doesn't exist or has been deleted."
        actionLabel="Go Back"
        onAction={() => router.back()}
      />
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: `${animal.name}'s Health Checks`,
        headerTitleStyle: { fontWeight: 'bold' },
        headerStyle: {
          backgroundColor: 'transparent',
        },
        headerTintColor: colors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
        )
      }} />

      <View style={styles.header}>
        <View style={styles.animalInfo}>
          <Text style={styles.animalName}>{animal.name}</Text>
          <Text style={styles.animalSpecies}>
            {animal.species}
            {animal.breed ? ` • ${animal.breed}` : ''}
          </Text>
        </View>
      </View>

      {healthChecks.length === 0 ? (
        <EmptyState
          title="No Health Checks"
          message="Perform regular health checks to monitor this animal's wellbeing"
          actionLabel="Add Health Check"
          onAction={handleAddHealthCheck}
          icon={<Activity size={48} color={colors.primary} />}
          style={styles.emptyState}
        />
      ) : (
        <FlatList
          data={healthChecks.sort((a, b) => b.date - a.date)}
          keyExtractor={item => item.id}
          renderItem={({ item }) => <HealthCheckCard healthCheck={item} />}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        />
      )}

      <TouchableOpacity
        style={styles.floatingButton}
        onPress={handleAddHealthCheck}
      >
        <LinearGradient
          colors={[colors.primary, colors.primaryDark]}
          style={styles.floatingButtonGradient}
        >
          <Plus size={24} color="white" />
        </LinearGradient>
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.card,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  animalSpecies: {
    fontSize: 14,
    color: colors.textSecondary,
  },

  listContent: {
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  floatingButtonGradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});