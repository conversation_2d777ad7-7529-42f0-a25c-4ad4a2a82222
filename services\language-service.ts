import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSettingsStore } from '@/store/settings-store';

// Get the stored language from settings
export async function getStoredLanguage(): Promise<string> {
  try {
    const { language } = useSettingsStore.getState();
    return language || 'en';
  } catch (error) {
    console.error('Error getting stored language:', error);
    return 'en'; // Default to English
  }
}

// Get the speech recognition language code based on app language
export function getSpeechLanguageCode(language: string): string {
  switch (language) {
    case 'ur':
      return 'ur-PK';
    case 'pa':
      return 'pa-IN';
    default:
      return 'en-US';
  }
}

// Get the language code for OpenAI Whisper
export function getWhisperLanguageCode(appLanguage: string): string {
  // Map app language codes to OpenAI Whisper language codes
  const languageMap: Record<string, string> = {
    'en': 'en', // English
    'ur': 'ur', // Urdu
    'pa': 'pa', // Punjabi
    // Add more languages as needed
  };
  
  return languageMap[appLanguage] || 'en'; // Default to English if not found
}



