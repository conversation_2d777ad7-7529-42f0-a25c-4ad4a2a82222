import { firestore } from '@/config/firebase';
import { collection, query, where, getDocs, sum, doc, getDoc } from 'firebase/firestore';

/**
 * Calculates the total expenses for a specific farm.
 * @param farmId The ID of the farm.
 * @returns The total expense amount, or 0 if none found or an error occurs.
 */
export const getTotalExpensesByFarm = async (farmId: string): Promise<number> => {
  try {
    if (!farmId) {
      console.error('No farm ID provided to getTotalExpensesByFarm');
      return 0;
    }

    // Check if the provided ID is a user ID instead of a farm ID
    const userRef = doc(firestore, 'users', farmId);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      console.error(`The provided ID ${farmId} is a user ID, not a farm ID`);
      return 0;
    }

    // Validate that the farmId exists in Firestore
    const farmRef = doc(firestore, 'farms', farmId);
    const farmDoc = await getDoc(farmRef);
    
    if (!farmDoc.exists()) {
      console.error(`Farm with ID ${farmId} does not exist in Firestore`);
      return 0;
    }

    const expensesRef = collection(firestore, 'farms', farmId, 'expenses');
    const querySnapshot = await getDocs(expensesRef);
    
    let total = 0;
    querySnapshot.forEach((doc) => {
      const expense = doc.data();
      total += expense.amount || 0;
    });
    
    return total;
  } catch (error) {
    console.error('Error getting total expenses:', error);
    return 0;
  }
};

/**
 * Calculates the total expenses for a specific animal.
 * @param animalId The ID of the animal.
 * @returns Object with total amount and currency, or { amount: 0, currency: 'PKR' } if none found.
 */
export const getTotalExpensesByAnimal = async (animalId: string): Promise<{ amount: number; currency: string }> => {
  if (!animalId) {
    console.error('getTotalExpensesByAnimal: animalId is required.');
    return { amount: 0, currency: 'PKR' };
  }

  try {
    // First, get all farms to search through their expense subcollections
    const farmsRef = collection(firestore, 'farms');
    const farmsSnapshot = await getDocs(farmsRef);

    let total = 0;
    let currency = 'PKR'; // Default currency
    const currencyCount: Record<string, number> = {};

    // Search through each farm's expenses subcollection
    for (const farmDoc of farmsSnapshot.docs) {
      const expensesRef = collection(firestore, 'farms', farmDoc.id, 'expenses');
      const q = query(expensesRef, where('animalId', '==', animalId));
      const querySnapshot = await getDocs(q);

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        total += data.amount || 0;

        // Track currency usage to find the most common one
        if (data.currency) {
          currencyCount[data.currency] = (currencyCount[data.currency] || 0) + 1;
        }
      });
    }

    // Use the most common currency, or the first non-default currency found
    if (Object.keys(currencyCount).length > 0) {
      currency = Object.keys(currencyCount).reduce((a, b) =>
        currencyCount[a] > currencyCount[b] ? a : b
      );
    }

    console.log(`Animal ${animalId} total expenses:`, { amount: total, currency });
    return { amount: total, currency };
  } catch (error) {
    console.error(`Error fetching total expenses for animal ${animalId}:`, error);
    return { amount: 0, currency: 'PKR' };
  }
};

export const getExpensesByFarmAndCategory = async (farmId: string): Promise<{category: string, amount: number}[]> => {
  try {
    const expensesRef = collection(firestore, 'expenses');
    const q = query(expensesRef, where('farmId', '==', farmId));
    const querySnapshot = await getDocs(q);
    
    // Group expenses by category
    const expensesByCategory: Record<string, number> = {};
    
    querySnapshot.forEach((doc) => {
      const expense = doc.data();
      const category = expense.category || 'Other';
      const amount = expense.amount || 0;
      
      if (!expensesByCategory[category]) {
        expensesByCategory[category] = 0;
      }
      
      expensesByCategory[category] += amount;
    });
    
    // Convert to array format needed for charts
    const result = Object.entries(expensesByCategory).map(([category, amount]) => ({
      category,
      amount
    }));
    
    return result;
  } catch (error) {
    console.error('Error getting expenses by category:', error);
    throw error;
  }
};
