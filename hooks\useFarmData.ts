import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';

export const useFarmData = (farmId: string | undefined) => {
  const [farmName, setFarmName] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFarmData = async () => {
      
      if (!farmId) {
        setLoading(false);
        setError('No farm ID provided');
        return;
      }


      try {
        const farmRef = doc(firestore, 'farms', farmId);
        
        const farmDoc = await getDoc(farmRef);
        
        if (farmDoc.exists()) {
          const farmData = farmDoc.data();
          setFarmName(farmData.name);
        } else {
          console.log(`No farm document found for ID: ${farmId}`);
          setError(`Farm with ID ${farmId} not found`);
        }
      } catch (error) {
        console.error('Error in fetchFarmData:', error);
        setError('Failed to fetch farm data');
      } finally {
        setLoading(false);
      }
    };

    fetchFarmData();
  }, [farmId]);

  return { farmName, loading, error };
};


