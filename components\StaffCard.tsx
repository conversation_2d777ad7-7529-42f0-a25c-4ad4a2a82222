import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Mail, Phone, ChevronRight, User } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
interface StaffCardProps {
  staff: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    role?: string;
    photoURL?: string;
    farmCount?: number;
    updatedAt?: number;
  };
  onPress: () => void;
}

const StaffCard: React.FC<StaffCardProps> = ({ staff, onPress }) => {
  const { t } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors);
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.leftSection}>
        <View style={styles.avatarContainer}>
          {staff.photoURL ? (
            <Image
              source={{ uri: staff.photoURL }}
              style={styles.avatar}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderAvatar}>
              <User size={24} color={themedColors.isDarkMode ? themedColors.textContrast : 'white'} />
            </View>
          )}
        </View>
        
        <View style={styles.infoContainer}>
          <View style={styles.nameContainer}>
            <Text style={styles.name} numberOfLines={1}>
              {staff.name}
            </Text>
 
            {staff.role && (
              <View style={styles.roleBadge}>
                <Text style={styles.roleText}>{t(`farms.staffSection.roles.${staff.role.toLowerCase()}`)}</Text>
              </View>
            )}
          </View>
          
          
          {staff.email && (
            <View style={styles.detailRow}>
              <Mail size={14} color={themedColors.textSecondary} />
              <Text style={styles.detailText} numberOfLines={1}>
                {staff.email}
              </Text>
            </View>
          )}
          
          {staff.phone && (
            <View style={styles.detailRow}>
              <Phone size={14} color={themedColors.textSecondary} />
              <Text style={styles.detailText}>
                {staff.phone}
              </Text>
            </View>
          )}
        </View>
      </View>
      
      <View style={styles.rightSection}>
        <ChevronRight size={20} color={themedColors.textSecondary} />
      </View>
    </TouchableOpacity>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 12,
    shadowColor: themedColors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leftSection: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  rightSection: {
    paddingLeft: 8,
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: themedColors.background, // Or a specific light background from theme
  },
  placeholderAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: themedColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: themedColors.text,
    marginRight: 8,
    flex: 1,
  },
  roleBadge: {
    backgroundColor: themedColors.primaryLight,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  roleText: {
    fontSize: 12,
    color: themedColors.primary,
    fontWeight: '500',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  detailText: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginLeft: 6,
  },
});

export default StaffCard;
