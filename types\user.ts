export interface User {
  id: string;
  email: string;
  name: string;
  role: 'owner' | 'admin' | 'caretaker';
  language: string;
  preferAudio: boolean;
  offlineMode: boolean;
  emailVerified: boolean;
  profilePicture?: string; // URL to profile picture
  createdAt: number;
  updatedAt: number;

  // Multi-tenancy fields
  tenantId?: string; // For admin/caretaker roles, references the owner's ID
  ownedFarms?: string[]; // For owner role, list of farm IDs they own
  assignedFarms?: string[]; // For admin/caretaker roles, list of farm IDs they're assigned to
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Auth methods
  init: () => void;
  checkAuthState: () => Promise<any>;
  register: (email: string, password: string, name: string) => Promise<string>;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  sendEmailVerification: () => Promise<void>;
  verifyEmail: (oobCode?: string) => Promise<boolean>;
}