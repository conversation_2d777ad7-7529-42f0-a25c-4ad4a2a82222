import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
interface LoadingIndicatorProps {
  message?: string;
  fullScreen?: boolean;
  size?: 'small' | 'large';
  color?: string;
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  message = 'Loading...',
  fullScreen = false,
  size = 'large',
  color,
}) => {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);
  const indicatorColor = color || themedColors.primary;
  if (fullScreen) {
    return (
      <View style={styles.fullScreenContainer}>
        <ActivityIndicator size={size} color={indicatorColor} />
        {message && <Text style={styles.message}>{message}</Text>}
      </View>
    );
  }


  return (
    <View style={styles.container}>
      <ActivityIndicator size={size} color={indicatorColor} />
      {message && <Text style={styles.message}>{message}</Text>}
    </View>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  fullScreenContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: themedColors.background,
  },
  message: {
    marginTop: 12,
    fontSize: 16,
    color: themedColors.textSecondary,
    textAlign: 'center',
  },
});

export default LoadingIndicator;