import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MilkingRecord, MilkingStats } from '@/types/milking';
import {
  addMilkingRecord,
  updateMilkingRecord,
  deleteMilkingRecord,
  getMilkingRecordsByFarm,
  getMilkingRecordsByAnimal,
  getMilkingStatsByFarm,
  getRecentMilkingRecords,
} from '@/services/milking-service';

interface MilkingState {
  records: MilkingRecord[];
  stats: MilkingStats | null;
  isLoading: boolean;
  error: string | null;
}

interface MilkingStore extends MilkingState {
  // Records management
  fetchRecordsByFarm: (farmId: string) => Promise<void>;
  fetchRecordsByAnimal: (animalId: string) => Promise<void>;
  fetchRecentRecords: (farmId: string, limit?: number) => Promise<void>;
  addRecord: (record: Omit<MilkingRecord, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  updateRecord: (id: string, updates: Partial<MilkingRecord>) => Promise<void>;
  deleteRecord: (id: string) => Promise<void>;
  
  // Statistics
  fetchStats: (farmId: string, days?: number) => Promise<void>;
  
  // Utility
  getRecordById: (id: string) => MilkingRecord | undefined;
  clearRecords: () => void;
  clearError: () => void;
}

export const useMilkingStore = create<MilkingStore>()(
  persist(
    (set, get) => ({
      records: [],
      stats: null,
      isLoading: false,
      error: null,

      fetchRecordsByFarm: async (farmId: string) => {
        set({ isLoading: true, error: null });
        try {
          const records = await getMilkingRecordsByFarm(farmId);
          set({ records, isLoading: false });
        } catch (error) {
          console.error('Error fetching milking records by farm:', error);
          // Don't set error for indexing issues, just show empty state
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch milking records';
          const isIndexError = errorMessage.includes('index') || errorMessage.includes('composite') || errorMessage.includes('query requires');

          if (isIndexError) {
            // For indexing errors, just set empty records and no error
            set({ records: [], isLoading: false, error: null });
          } else {
            set({
              error: errorMessage,
              isLoading: false
            });
          }
        }
      },

      fetchRecordsByAnimal: async (animalId: string) => {
        set({ isLoading: true, error: null });
        try {
          const records = await getMilkingRecordsByAnimal(animalId);
          set({ records, isLoading: false });
        } catch (error) {
          console.error('Error fetching milking records by animal:', error);
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch animal milking records';
          const isIndexError = errorMessage.includes('index') || errorMessage.includes('composite') || errorMessage.includes('query requires');

          if (isIndexError) {
            // For indexing errors, just set empty records and no error
            set({ records: [], isLoading: false, error: null });
          } else {
            set({
              error: errorMessage,
              isLoading: false
            });
          }
        }
      },

      fetchRecentRecords: async (farmId: string, limit = 10) => {
        set({ isLoading: true, error: null });
        try {
          const records = await getRecentMilkingRecords(farmId, limit);
          set({ records, isLoading: false });
        } catch (error) {
          console.error('Error fetching recent milking records:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch recent milking records',
            isLoading: false 
          });
        }
      },

      addRecord: async (recordData) => {
        set({ isLoading: true, error: null });
        try {
          const recordId = await addMilkingRecord(recordData);
          
          // Add the new record to the local state
          const newRecord: MilkingRecord = {
            ...recordData,
            id: recordId,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          };
          
          set(state => ({
            records: [newRecord, ...state.records],
            isLoading: false
          }));
          
          return recordId;
        } catch (error) {
          console.error('Error adding milking record:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to add milking record',
            isLoading: false 
          });
          throw error;
        }
      },

      updateRecord: async (id: string, updates: Partial<MilkingRecord>) => {
        set({ isLoading: true, error: null });
        try {
          await updateMilkingRecord(id, updates);
          
          // Update the record in local state
          set(state => ({
            records: state.records.map(record =>
              record.id === id 
                ? { ...record, ...updates, updatedAt: Date.now() }
                : record
            ),
            isLoading: false
          }));
        } catch (error) {
          console.error('Error updating milking record:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update milking record',
            isLoading: false 
          });
          throw error;
        }
      },

      deleteRecord: async (id: string) => {
        set({ isLoading: true, error: null });
        try {
          // Get the record to find its farmId
          const record = get().records.find(r => r.id === id);
          if (!record || !record.farmId) {
            throw new Error('Record not found or missing farmId');
          }

          await deleteMilkingRecord(id, record.farmId);

          // Remove the record from local state
          set(state => ({
            records: state.records.filter(record => record.id !== id),
            isLoading: false
          }));
        } catch (error) {
          console.error('Error deleting milking record:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to delete milking record',
            isLoading: false
          });
          throw error;
        }
      },

      fetchStats: async (farmId: string, days = 30) => {
        set({ isLoading: true, error: null });
        try {
          const stats = await getMilkingStatsByFarm(farmId, days);
          set({ stats, isLoading: false });
        } catch (error) {
          console.error('Error fetching milking stats:', error);
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch milking statistics';
          const isIndexError = errorMessage.includes('index') || errorMessage.includes('composite') || errorMessage.includes('query requires');

          if (isIndexError) {
            // For indexing errors, set default empty stats
            const emptyStats = {
              totalQuantity: 0,
              averageDaily: 0,
              averagePerAnimal: 0,
              qualityDistribution: {
                excellent: 0,
                good: 0,
                fair: 0,
                poor: 0,
              },
              topProducers: [],
              monthlyTrend: [],
            };
            set({ stats: emptyStats, isLoading: false, error: null });
          } else {
            set({
              error: errorMessage,
              isLoading: false
            });
          }
        }
      },

      getRecordById: (id: string) => {
        return get().records.find(record => record.id === id);
      },

      clearRecords: () => {
        set({ records: [], stats: null, error: null });
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'milking-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        records: state.records,
        stats: state.stats,
      }),
    }
  )
);
