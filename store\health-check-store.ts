import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { HealthCheck } from '@/types/healthCheck';
import { collection, addDoc, updateDoc, deleteDoc, getDocs, doc, query, where } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { useAnimalStore } from './animal-store';

interface HealthCheckState {
  healthChecks: HealthCheck[];
  isLoading: boolean;
  error: string | null;
  fetchHealthChecks: (animalId?: string) => Promise<HealthCheck[]>;
  getHealthCheck: (id: string) => HealthCheck | undefined;
  getHealthChecksByAnimalId: (animalId: string) => HealthCheck[];
  addHealthCheck: (healthCheck: Partial<HealthCheck>) => Promise<HealthCheck>;
  updateHealthCheck: (id: string, updates: Partial<HealthCheck>) => Promise<HealthCheck>;
  deleteHealthCheck: (id: string) => Promise<void>;
  clearHealthChecks: () => void;
}

export const useHealthCheckStore = create<HealthCheckState>()(
  persist(
    (set, get) => ({
      healthChecks: [],
      isLoading: false,
      error: null,

      fetchHealthChecks: async (animalId?: string) => {
        set({ isLoading: true, error: null });
        try {
          const fetchedHealthChecks: HealthCheck[] = [];
          
          if (animalId) {
            // If animalId is provided, fetch only for that animal
            const { getAnimal } = useAnimalStore.getState();
            const animal = getAnimal(animalId);
            
            if (!animal || !animal.farmId) {
              throw new Error('Animal not found or missing farmId');
            }
            
            // Get health checks from the animal's subcollection
            const farmRef = doc(firestore, 'farms', animal.farmId);
            const animalRef = doc(farmRef, 'animals', animalId);
            const healthChecksCollectionRef = collection(animalRef, 'healthChecks');
            
            const querySnapshot = await getDocs(healthChecksCollectionRef);
            
            querySnapshot.forEach((doc) => {
              fetchedHealthChecks.push({ 
                id: doc.id, 
                ...doc.data(),
                animalId: animalId // Ensure animalId is set
              } as HealthCheck);
            });
          } else {
            // If no animalId provided, fetch all health checks for all animals the user has access to
            const { animals } = useAnimalStore.getState();

            // For each animal, fetch its health checks
            for (const animal of animals) {
              if (!animal.farmId) continue;

              const farmRef = doc(firestore, 'farms', animal.farmId);
              const animalRef = doc(farmRef, 'animals', animal.id);
              const healthChecksCollectionRef = collection(animalRef, 'healthChecks');

              const querySnapshot = await getDocs(healthChecksCollectionRef);
              console.log(`Health Check Store: Found ${querySnapshot.size} health checks for animal ${animal.name} (${animal.id})`);

              querySnapshot.forEach((doc) => {
                fetchedHealthChecks.push({
                  id: doc.id,
                  ...doc.data(),
                  animalId: animal.id // Ensure animalId is set
                } as HealthCheck);
              });
            }
          }
      
          console.log('Health Check Store: Total health checks fetched:', fetchedHealthChecks.length);
          set({
            healthChecks: animalId
              ? [...get().healthChecks.filter(check => check.animalId !== animalId), ...fetchedHealthChecks]
              : fetchedHealthChecks,
            isLoading: false
          });

          return fetchedHealthChecks;
        } catch (error) {
          console.error('Error fetching health checks:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch health checks',
            isLoading: false
          });
          return [];
        }
      },

      getHealthCheck: (id: string) => {
        return get().healthChecks.find(check => check.id === id);
      },

      getHealthChecksByAnimalId: (animalId: string) => {
        return get().healthChecks.filter(check => check.animalId === animalId);
      },

      addHealthCheck: async (healthCheck: Partial<HealthCheck>) => {
        set({ isLoading: true, error: null });
        try {
          // Create a clean data object without undefined values
          const cleanData: Record<string, any> = {};
          
          // Only add defined properties to the clean data object
          Object.entries(healthCheck).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              cleanData[key] = value;
            }
          });
      
          // Ensure required fields are present
          if (!cleanData.animalId) {
            throw new Error('Missing required field: animalId');
          }
      
          // Handle image upload if there's an imageUri
          if (cleanData.imageUri && cleanData.imageUri.startsWith('file:')) {
            // This is a local file that needs to be uploaded
            const storage = getStorage();
            const timestamp = Date.now();
            const imageRef = ref(storage, `healthChecks/${cleanData.animalId}/${timestamp}`);
      
            // Convert URI to blob
            const response = await fetch(cleanData.imageUri);
            const blob = await response.blob();
      
            // Upload to Firebase Storage
            await uploadBytes(imageRef, blob);
      
            // Get download URL
            cleanData.imageUri = await getDownloadURL(imageRef);
          }
      
          // If imageUri is still undefined or null after processing, remove it
          if (!cleanData.imageUri) {
            delete cleanData.imageUri;
          }
      
          const timestamp = Date.now();
          const newHealthCheckData = {
            ...cleanData,
            createdAt: timestamp,
            updatedAt: timestamp,
          };
      
          // Get the farm ID for this animal
          const { getAnimal } = useAnimalStore.getState();
          const animal = getAnimal(cleanData.animalId);
          
          if (!animal || !animal.farmId) {
            throw new Error('Animal not found or missing farmId');
          }
      
          // Add to Firestore as subcollection under animal
          const farmRef = doc(firestore, 'farms', animal.farmId);
          const animalRef = doc(farmRef, 'animals', cleanData.animalId);
          const healthChecksCollectionRef = collection(animalRef, 'healthChecks');
          
          const docRef = await addDoc(healthChecksCollectionRef, newHealthCheckData);
      
          // Create the complete health check object
          const newHealthCheck: HealthCheck = {
            id: docRef.id,
            ...newHealthCheckData,
          } as HealthCheck;
      
          // Update local state
          set(state => ({
            healthChecks: [...state.healthChecks, newHealthCheck],
            isLoading: false,
          }));
      
          return newHealthCheck;
        } catch (error) {
          console.error('Error adding health check:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to add health check',
            isLoading: false
          });
          throw error;
        }
      },

      updateHealthCheck: async (id: string, updates: Partial<HealthCheck>) => {
        set({ isLoading: true, error: null });
        try {
          // Get the current health check to find its animalId
          const healthCheck = get().healthChecks.find(check => check.id === id);
          if (!healthCheck || !healthCheck.animalId) {
            throw new Error('Health check not found or missing animalId');
          }
      
          // Get the animal to find its farmId
          const { getAnimal } = useAnimalStore.getState();
          const animal = getAnimal(healthCheck.animalId);
          
          if (!animal || !animal.farmId) {
            throw new Error('Animal not found or missing farmId');
          }
      
          // Create a clean updates object
          const cleanUpdates: Record<string, any> = {};
          Object.entries(updates).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              cleanUpdates[key] = value;
            }
          });
      
          // Handle image upload if there's a new imageUri
          if (cleanUpdates.imageUri && cleanUpdates.imageUri.startsWith('file:')) {
            const storage = getStorage();
            const timestamp = Date.now();
            const imageRef = ref(storage, `healthChecks/${healthCheck.animalId}/${timestamp}`);
      
            // Convert URI to blob
            const response = await fetch(cleanUpdates.imageUri);
            const blob = await response.blob();
      
            // Upload to Firebase Storage
            await uploadBytes(imageRef, blob);
      
            // Get download URL
            cleanUpdates.imageUri = await getDownloadURL(imageRef);
          }
      
          const timestamp = Date.now();
          const updatedData = {
            ...cleanUpdates,
            updatedAt: timestamp
          };
      
          // Update in Firestore
          const farmRef = doc(firestore, 'farms', animal.farmId);
          const animalRef = doc(farmRef, 'animals', healthCheck.animalId);
          const healthCheckRef = doc(animalRef, 'healthChecks', id);
          
          await updateDoc(healthCheckRef, updatedData);
      
          // Update local state
          const updatedHealthCheck = { ...healthCheck, ...updatedData };
          set(state => ({
            healthChecks: state.healthChecks.map(check =>
              check.id === id ? updatedHealthCheck : check
            ),
            isLoading: false
          }));
      
          return updatedHealthCheck;
        } catch (error) {
          console.error('Error updating health check:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to update health check',
            isLoading: false
          });
          throw error;
        }
      },

      deleteHealthCheck: async (id) => {
        set({ isLoading: true, error: null });
        try {
          // Get the current health check to find its animalId
          const healthCheck = get().healthChecks.find(check => check.id === id);
          if (!healthCheck || !healthCheck.animalId) {
            throw new Error('Health check not found or missing animalId');
          }
      
          // Get the animal to find its farmId
          const { getAnimal } = useAnimalStore.getState();
          const animal = getAnimal(healthCheck.animalId);
          
          if (!animal || !animal.farmId) {
            throw new Error('Animal not found or missing farmId');
          }
      
          // Delete from Firestore
          const farmRef = doc(firestore, 'farms', animal.farmId);
          const animalRef = doc(farmRef, 'animals', healthCheck.animalId);
          const healthCheckRef = doc(animalRef, 'healthChecks', id);
          
          await deleteDoc(healthCheckRef);
      
          // Update the local state
          set({
            healthChecks: get().healthChecks.filter(check => check.id !== id),
            isLoading: false
          });
        } catch (error) {
          console.error('Error deleting health check:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to delete health check',
            isLoading: false
          });
          throw error;
        }
      },

      clearHealthChecks: () => {
        set({ healthChecks: [], error: null });
      }
    }),
    {
      name: 'health-check-store',
      storage: createJSONStorage(() => AsyncStorage)
    }
  )
);