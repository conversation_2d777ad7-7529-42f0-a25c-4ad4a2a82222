import React, { createContext, useState, useContext, ReactNode } from 'react';
import Toast, { ToastType } from '@/components/Toast';

interface ToastMessage {
  type?: ToastType;
  title?: string;
  message: string;
}

interface ToastContextProps {
  showToast: (messageData: string | ToastMessage, type?: ToastType, duration?: number) => void;
  hideToast: () => void;
}

const ToastContext = createContext<ToastContextProps | undefined>(undefined);

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [type, setType] = useState<ToastType>('success');
  const [duration, setDuration] = useState(3000);

  const showToast = (
    messageData: string | ToastMessage,
    toastType: ToastType = 'success',
    toastDuration: number = 3000
  ) => {
    if (typeof messageData === 'string') {
      setMessage(messageData);
      setType(toastType);
    } else {
      setMessage(messageData.message);
      setType(messageData.type || toastType);
    }
    setDuration(toastDuration);
    setVisible(true);
  };

  const hideToast = () => {
    setVisible(false);
  };

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      <Toast
        visible={visible}
        message={message}
        type={type}
        duration={duration}
        onDismiss={hideToast}
      />
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextProps => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};
