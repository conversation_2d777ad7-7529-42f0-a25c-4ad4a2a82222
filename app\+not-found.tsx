import { Link, Stack } from "expo-router";
import { StyleSheet, Text, View } from "react-native";
import { useTranslation } from "@/hooks/useTranslation";
import { useThemeColors } from "@/hooks/useThemeColors";

export default function NotFoundScreen() {
  const { t } = useTranslation();
  const themedColors = useThemeColors();

  return (
    <>
      <Stack.Screen options={{ title: t('common.error') }} />
      <View style={[styles.container, { backgroundColor: themedColors.background }]}>
        <Text style={[styles.title, { color: themedColors.text }]}>{t('common.pageNotFound')}</Text>

        <Link href="/" style={styles.link}>
          <Text style={[styles.linkText, { color: themedColors.primary }]}>{t('common.goToHome')}</Text>
        </Link>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
  linkText: {
    fontSize: 14,
  },
});
