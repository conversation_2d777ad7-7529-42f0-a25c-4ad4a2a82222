import { Disease } from '@/types/animal';

export const diseases: Disease[] = [
  {
    id: 'brd1',
    name: 'Bovine Respiratory Disease',
    description: 'Common respiratory infection in cattle',
    symptoms: ['fever', 'cough', 'nasal_discharge', 'lethargy'],
    treatments: ['antibiotics', 'anti-inflammatories', 'rest'],
    severity: 'medium',
    zoonotic: false
  },
  {
    id: 'fmd1',
    name: 'Foot and Mouth Disease',
    description: 'Highly contagious viral disease affecting cloven-hoofed animals',
    symptoms: ['fever', 'blisters', 'lameness', 'excessive_salivation'],
    treatments: ['supportive care', 'rest', 'soft food'],
    severity: 'high',
    zoonotic: false
  },
  {
    id: 'mastitis1',
    name: 'Mastitis',
    description: 'Inflammation of the mammary gland in dairy animals',
    symptoms: ['swollen_udder', 'pain', 'abnormal_milk', 'fever'],
    treatments: ['antibiotics', 'frequent milking', 'anti-inflammatories'],
    severity: 'medium',
    zoonotic: false
  },
  {
    id: 'parvo1',
    name: 'Canine Parvovirus',
    description: 'Highly contagious viral illness affecting dogs',
    symptoms: ['vomiting', 'diarrhea', 'lethargy', 'loss_of_appetite', 'fever'],
    treatments: ['supportive care', 'fluid therapy', 'antibiotics'],
    severity: 'high',
    zoonotic: false
  },
  {
    id: 'cae1',
    name: 'Caprine Arthritis Encephalitis',
    description: 'Viral disease affecting goats causing arthritis and other issues',
    symptoms: ['joint_swelling', 'limping', 'weight_loss', 'hard_udder'],
    treatments: ['supportive care', 'anti-inflammatories', 'management'],
    severity: 'medium',
    zoonotic: false
  },
  {
    id: 'nd1',
    name: 'Newcastle Disease',
    description: 'Viral disease affecting birds, particularly poultry',
    symptoms: ['respiratory_distress', 'neurological_signs', 'decreased_egg_production', 'diarrhea'],
    treatments: ['vaccination', 'biosecurity', 'supportive care'],
    severity: 'high',
    zoonotic: true
  },
  {
    id: 'rabies1',
    name: 'Rabies',
    description: 'Fatal viral disease affecting mammals',
    symptoms: ['behavioral_changes', 'aggression', 'paralysis', 'excessive_salivation'],
    treatments: ['none once symptomatic', 'vaccination for prevention'],
    severity: 'high',
    zoonotic: true
  },
  {
    id: 'tb1',
    name: 'Bovine Tuberculosis',
    description: 'Chronic bacterial disease affecting cattle and other species',
    symptoms: ['weight_loss', 'cough', 'enlarged_lymph_nodes', 'lethargy'],
    treatments: ['culling', 'testing and surveillance'],
    severity: 'high',
    zoonotic: true
  },
  {
    id: 'bruc1',
    name: 'Brucellosis',
    description: 'Bacterial disease affecting multiple species',
    symptoms: ['abortion', 'retained_placenta', 'infertility', 'joint_pain'],
    treatments: ['antibiotics', 'testing and culling'],
    severity: 'high',
    zoonotic: true
  },
  {
    id: 'bse1',
    name: 'Bovine Spongiform Encephalopathy',
    description: 'Fatal neurodegenerative disease in cattle',
    symptoms: ['neurological_signs', 'aggression', 'incoordination', 'weight_loss'],
    treatments: ['none', 'culling'],
    severity: 'high',
    zoonotic: true
  }
];