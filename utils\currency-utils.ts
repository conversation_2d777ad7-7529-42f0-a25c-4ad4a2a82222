

/**
 * Currency information interface
 */
export interface CurrencyInfo {
  code: string;
  symbol: string;
  name: string;
  country: string;
  flag: string;
  locale?: string;
}

/**
 * Comprehensive currency data for major world currencies
 */
export const CURRENCIES: Record<string, CurrencyInfo> = {
  // Major World Currencies
  USD: { code: 'USD', symbol: '$', name: 'US Dollar', country: 'United States', flag: '🇺🇸', locale: 'en-US' },
  EUR: { code: 'EUR', symbol: '€', name: 'Euro', country: 'European Union', flag: '🇪🇺', locale: 'en-EU' },
  GBP: { code: 'GBP', symbol: '£', name: 'British Pound', country: 'United Kingdom', flag: '🇬🇧', locale: 'en-GB' },
  JPY: { code: 'JPY', symbol: '¥', name: 'Japanese Yen', country: 'Japan', flag: '🇯🇵', locale: 'ja-JP' },
  CHF: { code: 'CHF', symbol: 'CHF', name: 'Swiss Franc', country: 'Switzerland', flag: '🇨🇭', locale: 'de-CH' },
  CAD: { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar', country: 'Canada', flag: '🇨🇦', locale: 'en-CA' },
  AUD: { code: 'AUD', symbol: 'A$', name: 'Australian Dollar', country: 'Australia', flag: '🇦🇺', locale: 'en-AU' },

  // Asian Currencies
  PKR: { code: 'PKR', symbol: '₨', name: 'Pakistani Rupee', country: 'Pakistan', flag: '🇵🇰', locale: 'ur-PK' },
  INR: { code: 'INR', symbol: '₹', name: 'Indian Rupee', country: 'India', flag: '🇮🇳', locale: 'hi-IN' },
  CNY: { code: 'CNY', symbol: '¥', name: 'Chinese Yuan', country: 'China', flag: '🇨🇳', locale: 'zh-CN' },
  KRW: { code: 'KRW', symbol: '₩', name: 'South Korean Won', country: 'South Korea', flag: '🇰🇷', locale: 'ko-KR' },
  SGD: { code: 'SGD', symbol: 'S$', name: 'Singapore Dollar', country: 'Singapore', flag: '🇸🇬', locale: 'en-SG' },
  HKD: { code: 'HKD', symbol: 'HK$', name: 'Hong Kong Dollar', country: 'Hong Kong', flag: '🇭🇰', locale: 'zh-HK' },
  THB: { code: 'THB', symbol: '฿', name: 'Thai Baht', country: 'Thailand', flag: '🇹🇭', locale: 'th-TH' },
  MYR: { code: 'MYR', symbol: 'RM', name: 'Malaysian Ringgit', country: 'Malaysia', flag: '🇲🇾', locale: 'ms-MY' },
  IDR: { code: 'IDR', symbol: 'Rp', name: 'Indonesian Rupiah', country: 'Indonesia', flag: '🇮🇩', locale: 'id-ID' },
  PHP: { code: 'PHP', symbol: '₱', name: 'Philippine Peso', country: 'Philippines', flag: '🇵🇭', locale: 'en-PH' },
  VND: { code: 'VND', symbol: '₫', name: 'Vietnamese Dong', country: 'Vietnam', flag: '🇻🇳', locale: 'vi-VN' },

  // Middle Eastern Currencies
  AED: { code: 'AED', symbol: 'د.إ', name: 'UAE Dirham', country: 'United Arab Emirates', flag: '🇦🇪', locale: 'ar-AE' },
  SAR: { code: 'SAR', symbol: '﷼', name: 'Saudi Riyal', country: 'Saudi Arabia', flag: '🇸🇦', locale: 'ar-SA' },
  QAR: { code: 'QAR', symbol: '﷼', name: 'Qatari Riyal', country: 'Qatar', flag: '🇶🇦', locale: 'ar-QA' },
  KWD: { code: 'KWD', symbol: 'د.ك', name: 'Kuwaiti Dinar', country: 'Kuwait', flag: '🇰🇼', locale: 'ar-KW' },
  BHD: { code: 'BHD', symbol: '.د.ب', name: 'Bahraini Dinar', country: 'Bahrain', flag: '🇧🇭', locale: 'ar-BH' },
  OMR: { code: 'OMR', symbol: '﷼', name: 'Omani Rial', country: 'Oman', flag: '🇴🇲', locale: 'ar-OM' },
  JOD: { code: 'JOD', symbol: 'د.ا', name: 'Jordanian Dinar', country: 'Jordan', flag: '🇯🇴', locale: 'ar-JO' },
  LBP: { code: 'LBP', symbol: '£', name: 'Lebanese Pound', country: 'Lebanon', flag: '🇱🇧', locale: 'ar-LB' },

  // African Currencies
  ZAR: { code: 'ZAR', symbol: 'R', name: 'South African Rand', country: 'South Africa', flag: '🇿🇦', locale: 'en-ZA' },
  EGP: { code: 'EGP', symbol: '£', name: 'Egyptian Pound', country: 'Egypt', flag: '🇪🇬', locale: 'ar-EG' },
  NGN: { code: 'NGN', symbol: '₦', name: 'Nigerian Naira', country: 'Nigeria', flag: '🇳🇬', locale: 'en-NG' },
  KES: { code: 'KES', symbol: 'KSh', name: 'Kenyan Shilling', country: 'Kenya', flag: '🇰🇪', locale: 'en-KE' },
  GHS: { code: 'GHS', symbol: '₵', name: 'Ghanaian Cedi', country: 'Ghana', flag: '🇬🇭', locale: 'en-GH' },

  // European Currencies (Non-Euro)
  NOK: { code: 'NOK', symbol: 'kr', name: 'Norwegian Krone', country: 'Norway', flag: '🇳🇴', locale: 'no-NO' },
  SEK: { code: 'SEK', symbol: 'kr', name: 'Swedish Krona', country: 'Sweden', flag: '🇸🇪', locale: 'sv-SE' },
  DKK: { code: 'DKK', symbol: 'kr', name: 'Danish Krone', country: 'Denmark', flag: '🇩🇰', locale: 'da-DK' },
  PLN: { code: 'PLN', symbol: 'zł', name: 'Polish Zloty', country: 'Poland', flag: '🇵🇱', locale: 'pl-PL' },
  CZK: { code: 'CZK', symbol: 'Kč', name: 'Czech Koruna', country: 'Czech Republic', flag: '🇨🇿', locale: 'cs-CZ' },
  HUF: { code: 'HUF', symbol: 'Ft', name: 'Hungarian Forint', country: 'Hungary', flag: '🇭🇺', locale: 'hu-HU' },
  RON: { code: 'RON', symbol: 'lei', name: 'Romanian Leu', country: 'Romania', flag: '🇷🇴', locale: 'ro-RO' },
  BGN: { code: 'BGN', symbol: 'лв', name: 'Bulgarian Lev', country: 'Bulgaria', flag: '🇧🇬', locale: 'bg-BG' },
  HRK: { code: 'HRK', symbol: 'kn', name: 'Croatian Kuna', country: 'Croatia', flag: '🇭🇷', locale: 'hr-HR' },
  RUB: { code: 'RUB', symbol: '₽', name: 'Russian Ruble', country: 'Russia', flag: '🇷🇺', locale: 'ru-RU' },

  // Latin American Currencies
  BRL: { code: 'BRL', symbol: 'R$', name: 'Brazilian Real', country: 'Brazil', flag: '🇧🇷', locale: 'pt-BR' },
  MXN: { code: 'MXN', symbol: '$', name: 'Mexican Peso', country: 'Mexico', flag: '🇲🇽', locale: 'es-MX' },
  ARS: { code: 'ARS', symbol: '$', name: 'Argentine Peso', country: 'Argentina', flag: '🇦🇷', locale: 'es-AR' },
  CLP: { code: 'CLP', symbol: '$', name: 'Chilean Peso', country: 'Chile', flag: '🇨🇱', locale: 'es-CL' },
  COP: { code: 'COP', symbol: '$', name: 'Colombian Peso', country: 'Colombia', flag: '🇨🇴', locale: 'es-CO' },
  PEN: { code: 'PEN', symbol: 'S/', name: 'Peruvian Sol', country: 'Peru', flag: '🇵🇪', locale: 'es-PE' },
  UYU: { code: 'UYU', symbol: '$U', name: 'Uruguayan Peso', country: 'Uruguay', flag: '🇺🇾', locale: 'es-UY' },

  // Other Notable Currencies
  TRY: { code: 'TRY', symbol: '₺', name: 'Turkish Lira', country: 'Turkey', flag: '🇹🇷', locale: 'tr-TR' },
  ILS: { code: 'ILS', symbol: '₪', name: 'Israeli Shekel', country: 'Israel', flag: '🇮🇱', locale: 'he-IL' },
  NZD: { code: 'NZD', symbol: 'NZ$', name: 'New Zealand Dollar', country: 'New Zealand', flag: '🇳🇿', locale: 'en-NZ' },
  IRR: { code: 'IRR', symbol: '﷼', name: 'Iranian Rial', country: 'Iran', flag: '🇮🇷', locale: 'fa-IR' },
  AFN: { code: 'AFN', symbol: '؋', name: 'Afghan Afghani', country: 'Afghanistan', flag: '🇦🇫', locale: 'fa-AF' },
  BDT: { code: 'BDT', symbol: '৳', name: 'Bangladeshi Taka', country: 'Bangladesh', flag: '🇧🇩', locale: 'bn-BD' },
  LKR: { code: 'LKR', symbol: '₨', name: 'Sri Lankan Rupee', country: 'Sri Lanka', flag: '🇱🇰', locale: 'si-LK' },
  NPR: { code: 'NPR', symbol: '₨', name: 'Nepalese Rupee', country: 'Nepal', flag: '🇳🇵', locale: 'ne-NP' },
  MVR: { code: 'MVR', symbol: '.ރ', name: 'Maldivian Rufiyaa', country: 'Maldives', flag: '🇲🇻', locale: 'dv-MV' },
  BTN: { code: 'BTN', symbol: 'Nu.', name: 'Bhutanese Ngultrum', country: 'Bhutan', flag: '🇧🇹', locale: 'dz-BT' },
};

/**
 * Get popular currencies (most commonly used)
 */
export const POPULAR_CURRENCIES = [
  'USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD',
  'PKR', 'INR', 'CNY', 'AED', 'SAR', 'BRL', 'MXN'
];

/**
 * Formats a number as currency
 * @param amount The amount to format
 * @param currencyCode Optional currency code (defaults to USD)
 * @param locale Optional locale string (defaults to 'en-US')
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currencyCode: string = 'USD',
  locale?: string
): string {
  if (amount === undefined || amount === null) return '';

  const currency = CURRENCIES[currencyCode];
  const formatLocale = locale || currency?.locale || 'en-US';

  try {
    return new Intl.NumberFormat(formatLocale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    const symbol = currency?.symbol || currencyCode;
    return `${symbol} ${amount.toFixed(2)}`;
  }
}

/**
 * Formats a number as currency with the user's preferred settings
 * @param amount The amount to format
 * @returns Formatted currency string
 */
export function formatCurrencyWithUserPreferences(amount: number): string {
  // This is a placeholder - in a real app, you would get the user's
  // preferred currency and locale from settings or context
  return formatCurrency(amount);
}

/**
 * Parses a currency string into a number
 * @param currencyString The currency string to parse
 * @returns Parsed number or null if invalid
 */
export function parseCurrency(currencyString: string): number | null {
  if (!currencyString) return null;

  // Remove currency symbols, commas, and other non-numeric characters
  // except for the decimal point
  const numericString = currencyString.replace(/[^0-9.-]/g, '');

  const parsedNumber = parseFloat(numericString);

  return isNaN(parsedNumber) ? null : parsedNumber;
}

/**
 * Get currency information by code
 * @param currencyCode The currency code
 * @returns Currency information or null if not found
 */
export function getCurrencyInfo(currencyCode: string): CurrencyInfo | null {
  return CURRENCIES[currencyCode] || null;
}

/**
 * Get all available currencies
 * @returns Array of all currency information
 */
export function getAllCurrencies(): CurrencyInfo[] {
  return Object.values(CURRENCIES);
}

/**
 * Get popular currencies
 * @returns Array of popular currency information
 */
export function getPopularCurrencies(): CurrencyInfo[] {
  return POPULAR_CURRENCIES.map(code => CURRENCIES[code]).filter(Boolean);
}

/**
 * Search currencies by name, code, or country
 * @param query Search query
 * @returns Array of matching currencies
 */
export function searchCurrencies(query: string): CurrencyInfo[] {
  if (!query) return getAllCurrencies();

  const searchTerm = query.toLowerCase();
  return getAllCurrencies().filter(currency =>
    currency.code.toLowerCase().includes(searchTerm) ||
    currency.name.toLowerCase().includes(searchTerm) ||
    currency.country.toLowerCase().includes(searchTerm)
  );
}

/**
 * Get currencies by region
 * @param region Region name
 * @returns Array of currencies in the region
 */
export function getCurrenciesByRegion(region: string): CurrencyInfo[] {
  const regionMappings: Record<string, string[]> = {
    'asia': ['PKR', 'INR', 'CNY', 'JPY', 'KRW', 'SGD', 'HKD', 'THB', 'MYR', 'IDR', 'PHP', 'VND', 'BDT', 'LKR', 'NPR', 'MVR', 'BTN'],
    'europe': ['EUR', 'GBP', 'CHF', 'NOK', 'SEK', 'DKK', 'PLN', 'CZK', 'HUF', 'RON', 'BGN', 'HRK', 'RUB'],
    'middle_east': ['AED', 'SAR', 'QAR', 'KWD', 'BHD', 'OMR', 'JOD', 'LBP', 'ILS', 'TRY', 'IRR'],
    'africa': ['ZAR', 'EGP', 'NGN', 'KES', 'GHS'],
    'americas': ['USD', 'CAD', 'BRL', 'MXN', 'ARS', 'CLP', 'COP', 'PEN', 'UYU'],
    'oceania': ['AUD', 'NZD']
  };

  const codes = regionMappings[region.toLowerCase()] || [];
  return codes.map(code => CURRENCIES[code]).filter(Boolean);
}

/**
 * Legacy currency flags mapping for backward compatibility
 */
export const currencyFlags: Record<string, string> = Object.fromEntries(
  Object.entries(CURRENCIES).map(([code, info]) => [code, info.flag])
);

/**
 * Gets the flag representation for a currency code
 * @param currencyCode The currency code
 * @returns Flag emoji or country code if emoji not supported
 */
export function getCurrencyFlag(currencyCode: string): string {
  // Country code mapping for reliable display
  const countryCodeMap: Record<string, string> = {
    'PKR': 'PK',
    'USD': 'US',
    'EUR': 'EU',
    'GBP': 'GB',
    'INR': 'IN',
    'AED': 'AE',
    'SAR': 'SA',
    'JPY': 'JP',
    'CHF': 'CH',
    'CAD': 'CA',
    'AUD': 'AU',
    'CNY': 'CN',
    'KRW': 'KR',
    'SGD': 'SG',
    'HKD': 'HK',
    'THB': 'TH',
    'MYR': 'MY',
    'IDR': 'ID',
    'PHP': 'PH',
    'VND': 'VN',
    'ZAR': 'ZA',
    'EGP': 'EG',
    'NGN': 'NG',
    'KES': 'KE',
    'GHS': 'GH',
    'NOK': 'NO',
    'SEK': 'SE',
    'DKK': 'DK',
    'PLN': 'PL',
    'CZK': 'CZ',
    'HUF': 'HU',
    'RON': 'RO',
    'BGN': 'BG',
    'HRK': 'HR',
    'RUB': 'RU',
    'BRL': 'BR',
    'MXN': 'MX',
    'ARS': 'AR',
    'CLP': 'CL',
    'COP': 'CO',
    'PEN': 'PE',
    'UYU': 'UY',
    'TRY': 'TR',
    'ILS': 'IL',
    'NZD': 'NZ',
    'IRR': 'IR',
    'AFN': 'AF',
    'BDT': 'BD',
    'LKR': 'LK',
    'NPR': 'NP',
    'MVR': 'MV',
    'BTN': 'BT',
    'QAR': 'QA',
    'KWD': 'KW',
    'BHD': 'BH',
    'OMR': 'OM',
    'JOD': 'JO',
    'LBP': 'LB'
  };

  // Try emoji first, then fall back to country code
  const flag = CURRENCIES[currencyCode]?.flag;
  const countryCode = countryCodeMap[currencyCode];

  // Return emoji if available, otherwise country code
  return flag || countryCode || currencyCode.substring(0, 2) || '??';
}

/**
 * Gets the symbol for a currency code
 * @param currencyCode The currency code
 * @returns Currency symbol or code if not found
 */
export function getCurrencySymbol(currencyCode: string): string {
  return CURRENCIES[currencyCode]?.symbol || currencyCode;
}

/**
 * Gets emoji flag for a currency (for testing)
 * @param currencyCode The currency code
 * @returns Emoji flag or empty string
 */
export function getCurrencyEmojiFlag(currencyCode: string): string {
  return CURRENCIES[currencyCode]?.flag || '';
}

/**
 * Gets country code for a currency
 * @param currencyCode The currency code
 * @returns Country code
 */
export function getCurrencyCountryCode(currencyCode: string): string {
  const countryCodeMap: Record<string, string> = {
    'PKR': 'PK', 'USD': 'US', 'EUR': 'EU', 'GBP': 'GB', 'INR': 'IN',
    'AED': 'AE', 'SAR': 'SA', 'JPY': 'JP', 'CHF': 'CH', 'CAD': 'CA',
    'AUD': 'AU', 'CNY': 'CN', 'KRW': 'KR', 'SGD': 'SG', 'HKD': 'HK'
  };

  return countryCodeMap[currencyCode] || currencyCode.substring(0, 2) || '??';
}

/**
 * Groups expenses by currency and calculates totals
 * @param expenses Array of expenses
 * @returns Object with currency totals and breakdown
 */
export function groupExpensesByCurrency(expenses: any[]) {
  const currencyTotals: Record<string, number> = {};
  const currencyBreakdown: Record<string, any[]> = {};

  expenses.forEach(expense => {
    const currency = expense.currency || 'PKR'; // Default to Pakistani Rupee

    if (!currencyTotals[currency]) {
      currencyTotals[currency] = 0;
      currencyBreakdown[currency] = [];
    }

    currencyTotals[currency] += expense.amount;
    currencyBreakdown[currency].push(expense);
  });

  return {
    totals: currencyTotals,
    breakdown: currencyBreakdown,
    currencies: Object.keys(currencyTotals).sort()
  };
}

/**
 * Gets the primary currency total and other currencies
 * @param expenses Array of expenses
 * @param preferredCurrency User's preferred currency (default: PKR)
 * @returns Object with primary total and other currency totals
 */
export function getExpenseTotals(expenses: any[], preferredCurrency: string = 'PKR') {
  const grouped = groupExpensesByCurrency(expenses);

  // If no expenses, return zero
  if (grouped.currencies.length === 0) {
    return {
      primaryTotal: 0,
      primaryCurrency: preferredCurrency,
      otherCurrencies: [],
      allTotals: {},
      hasMultipleCurrencies: false
    };
  }

  // Check if preferred currency (PKR) exists in expenses
  let actualPrimaryCurrency = preferredCurrency;
  let primaryTotal = grouped.totals[preferredCurrency] || 0;

  // If no PKR expenses found, use the currency with the highest total
  if (primaryTotal === 0 && grouped.currencies.length > 0) {
    const currencyWithHighestTotal = Object.entries(grouped.totals)
      .sort(([,a], [,b]) => b - a)[0];

    if (currencyWithHighestTotal) {
      actualPrimaryCurrency = currencyWithHighestTotal[0];
      primaryTotal = currencyWithHighestTotal[1];
    }
  }

  const otherCurrencies = Object.entries(grouped.totals)
    .filter(([currency]) => currency !== actualPrimaryCurrency)
    .map(([currency, amount]) => ({ currency, amount }));

  return {
    primaryTotal,
    primaryCurrency: actualPrimaryCurrency,
    otherCurrencies,
    allTotals: grouped.totals,
    hasMultipleCurrencies: grouped.currencies.length > 1
  };
}
