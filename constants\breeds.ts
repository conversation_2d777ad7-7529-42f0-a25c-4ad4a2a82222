import { Animal } from '@/types/animal';

export type BreedOption = {
  value: string;
  label: string;
  description?: string;
};

export const breedsBySpecies: Record<string, BreedOption[]> = {
  Cow: [
    { value: 'Sahiwal', label: '<PERSON><PERSON><PERSON>', description: 'High milk yield, common in Pakistan & India' },
    { value: 'Holstein Friesian', label: 'Holstein Friesian', description: 'Famous dairy breed, black & white pattern' },
    { value: 'Jersey', label: 'Jersey', description: 'Small-sized, high butterfat milk' },
    { value: 'Red Sindhi', label: 'Red Sindhi', description: 'Heat-tolerant, dual-purpose breed' },
    { value: 'Ayrshire', label: 'Ayrshire', description: 'Known for vigor and efficiency in milk production' },
    { value: 'Brown Swiss', label: 'Brown Swiss', description: 'Large and docile, high protein milk' },
    { value: 'Guernsey', label: 'Guernsey', description: 'Golden-yellow milk, rich in beta-carotene' },
    { value: 'Tharparkar', label: 'Tharparkar', description: 'Dual-purpose, drought-resistant' },
    { value: 'Gir', label: 'Gir', description: 'Indigenous Indian breed with high milk yield' },
    { value: '<PERSON><PERSON><PERSON><PERSON>', label: 'Ka<PERSON><PERSON>j', description: 'Strong and good milk producer' },
    { value: '<PERSON><PERSON>', label: 'Hariana', description: 'Popular in northern India, dual-purpose' },
    { value: 'Rathi', label: 'Rathi', description: 'Efficient in hot climates, good milker' },
    { value: 'Nagori', label: 'Nagori', description: 'Draft breed, also milk-producing' },
    { value: 'Mewati', label: 'Mewati', description: 'Good milk and draft breed' },
    { value: 'Khillari', label: 'Khillari', description: 'Hardy and used for draft and milk' },
    { value: 'Deoni', label: 'Deoni', description: 'Triple-purpose breed: milk, meat, draft' },
    { value: 'Ongole', label: 'Ongole', description: 'Strong and aggressive, good milk yield' },
    { value: 'Amrit Mahal', label: 'Amrit Mahal', description: 'Used in heavy draft work' },
    { value: 'Kasaragod Dwarf', label: 'Kasaragod Dwarf', description: 'Small breed from Kerala' },
    { value: 'Vechur', label: 'Vechur', description: 'One of the smallest cattle breeds in the world' },
  ],
  Goat: [
    { value: 'Beetal', label: 'Beetal', description: 'Large-sized, good for milk and meat' },
    { value: 'Boer', label: 'Boer', description: 'South African breed, meat-focused' },
    { value: 'Jamunapari', label: 'Jamunapari', description: 'Long ears, good milking breed' },
    { value: 'Black Bengal', label: 'Black Bengal', description: 'Popular meat breed, small size' },
    { value: 'Barbari', label: 'Barbari', description: 'Small breed, ideal for stall feeding' },
    { value: 'Osmanabadi', label: 'Osmanabadi', description: 'Dual-purpose breed' },
    { value: 'Sirohi', label: 'Sirohi', description: 'Hardy and adaptable breed' },
    { value: 'Malabari', label: 'Malabari', description: 'Native to Kerala, good milker' },
    { value: 'Mehsana', label: 'Mehsana', description: 'Found in Gujarat, strong and productive' },
    { value: 'Kutchi', label: 'Kutchi', description: 'Known for long-distance walking' },
    { value: 'Pashmina', label: 'Pashmina', description: 'Produces luxurious wool' },
    { value: 'Changthangi', label: 'Changthangi', description: 'High altitude goat from Ladakh' },
    { value: 'Kanni Adu', label: 'Kanni Adu', description: 'Black-colored, good milker' },
    { value: 'Zalawadi', label: 'Zalawadi', description: 'Adapted to dry regions' },
    { value: 'Surti', label: 'Surti', description: 'High-quality milk, short-eared' },
    { value: 'Tellicherry', label: 'Tellicherry', description: 'Found in Kerala and Tamil Nadu' },
    { value: 'Gohilwadi', label: 'Gohilwadi', description: 'Good for meat and milk' },
    { value: 'Kota', label: 'Kota', description: 'Native to Rajasthan, black coat' },
    { value: 'Marwari', label: 'Marwari', description: 'Resilient breed, long-distance walker' },
    { value: 'Jhakrana', label: 'Jhakrana', description: 'Superior dairy type breed' },
  ],
  
  Poultry: [
    { value: 'Rhode Island Red', label: 'Rhode Island Red', description: 'Dual-purpose (eggs and meat)' },
    { value: 'Leghorn', label: 'Leghorn', description: 'Excellent egg layer' },
    { value: 'Broiler', label: 'Broiler', description: 'Bred specifically for meat' },
    { value: 'Australorp', label: 'Australorp', description: 'Hardy and suitable for backyard farming' },
    { value: 'Orpington', label: 'Orpington', description: 'Calm nature, good egg layers' },
    { value: 'Plymouth Rock', label: 'Plymouth Rock', description: 'Dual-purpose, good temperament' },
    { value: 'Sussex', label: 'Sussex', description: 'Heavy breed, excellent layers' },
    { value: 'Brahma', label: 'Brahma', description: 'Large, winter-hardy breed' },
    { value: 'Silkie', label: 'Silkie', description: 'Fluffy feathers, ornamental' },
    { value: 'Kadaknath', label: 'Kadaknath', description: 'Black meat, native to India' },
    { value: 'Aseel', label: 'Aseel', description: 'Aggressive, used in cockfighting' },
    { value: 'Cochin', label: 'Cochin', description: 'Fluffy and friendly, ornamental' },
    { value: 'Naked Neck', label: 'Naked Neck', description: 'Heat-tolerant breed' },
    { value: 'Fayoumi', label: 'Fayoumi', description: 'Egyptian breed, disease resistant' },
    { value: 'Ancona', label: 'Ancona', description: 'Good layer, active forager' },
    { value: 'Barnevelder', label: 'Barnevelder', description: 'Beautiful patterned feathers' },
    { value: 'Hamburg', label: 'Hamburg', description: 'Small and energetic' },
    { value: 'Campine', label: 'Campine', description: 'Belgian, good for eggs' },
    { value: 'Langshan', label: 'Langshan', description: 'Tall breed, calm temperament' },
    { value: 'Java', label: 'Java', description: 'Dual-purpose, early American breed' },
  ],  
  Fish: [
    { value: 'Rohu', label: 'Rohu (Labeo rohita)', description: 'Popular freshwater fish in South Asia' },
    { value: 'Tilapia', label: 'Tilapia', description: 'Fast-growing and hardy, ideal for warm waters' },
    { value: 'Catla', label: 'Catla', description: 'Surface feeder, commonly farmed with Rohu' },
    { value: 'Pangasius', label: 'Pangasius (Basa fish)', description: 'Fast-growing, used in commercial aquaculture' },
    { value: 'Common Carp', label: 'Common Carp', description: 'Adaptable freshwater fish' },
    { value: 'Grass Carp', label: 'Grass Carp', description: 'Eats aquatic plants, fast grower' },
    { value: 'Silver Carp', label: 'Silver Carp', description: 'Filter feeder, popular in polyculture' },
    { value: 'Mrigal', label: 'Mrigal', description: 'Bottom feeder, pairs well with Rohu and Catla' },
    { value: 'Snakehead', label: 'Snakehead', description: 'Carnivorous and aggressive fish' },
    { value: 'Koi', label: 'Koi', description: 'Ornamental and colorful' },
    { value: 'Guppy', label: 'Guppy', description: 'Small and ornamental' },
    { value: 'Goldfish', label: 'Goldfish', description: 'Popular aquarium fish' },
    { value: 'Molly', label: 'Molly', description: 'Livebearer, hardy aquarium fish' },
    { value: 'Oscar', label: 'Oscar', description: 'Aggressive and intelligent' },
    { value: 'Betta', label: 'Betta', description: 'Colorful and territorial' },
    { value: 'Tinfoil Barb', label: 'Tinfoil Barb', description: 'Fast swimming, shiny scales' },
    { value: 'Zebra Danio', label: 'Zebra Danio', description: 'Small and active schooling fish' },
    { value: 'Rainbow Shark', label: 'Rainbow Shark', description: 'Semi-aggressive freshwater fish' },
    { value: 'Blue Gourami', label: 'Blue Gourami', description: 'Labyrinth fish, peaceful' },
    { value: 'Platy', label: 'Platy', description: 'Colorful and easy to breed' },
  ],
  
};

export const getSpeciesGenderLabels = (species: string): { male: string; female: string } => {
  switch (species) {
    case 'Cow':
      return { male: 'Bull', female: 'Cow' };
    case 'Goat':
      return { male: 'Buck', female: 'Doe' };
    case 'Poultry':
      return { male: 'Rooster', female: 'Hen' };
    case 'Fish':
      return { male: 'Male', female: 'Female' };
    default:
      return { male: 'Male', female: 'Female' };
  }
};

export const generateTagId = (): string => {
  // Generate a random 5-digit integer
  return Math.floor(10000 + Math.random() * 90000).toString();
};
// Gestation periods by species (in days)
export const gestationPeriods: Record<string, number> = {
  'cattle': 283, // 9.5 months
  'cow': 283,    // alias for cattle
  'sheep': 152,  // 5 months
  'goat': 150,   // 5 months
  'pig': 114,    // 3 months, 3 weeks, 3 days
  'horse': 340,  // 11 months
  'rabbit': 31,  // 1 month
  'chicken': 21, // 21 days (not mammals but included for completeness)
  'duck': 28,    // 28 days
  'turkey': 28,  // 28 days
  'default': 150 // Default value
};
