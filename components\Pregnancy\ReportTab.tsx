import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { Pregnancy } from '@/store/pregnancy-store';
import { LinearGradient } from 'expo-linear-gradient';
import { format, addDays } from 'date-fns';
import { Calendar, Clock, Baby, Milestone, Heart, User, Tag, CheckCircle } from 'lucide-react-native'; // Import icons
import { formatDate as utilFormatDate } from '@/utils/date-utils';

interface ReportTabProps {
  pregnancy: Pregnancy;
  colors: any;
}

const ReportTab = ({ pregnancy, colors }: ReportTabProps) => {
  const { t, language } = useTranslation();
  
  // Format date to a more readable format with proper localization
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return t('common.notAvailable', { defaultValue: 'Not Available' });

    try {
      const date = new Date(dateString);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return t('common.notAvailable', { defaultValue: 'Not Available' });
      }

      // Use proper locale for date formatting
      const locale = language === 'ur' ? 'ur-PK' : 'en-US';
      return date.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return t('common.notAvailable', { defaultValue: 'Not Available' });
    }
  };
  
  // Calculate expected date if not provided
  const getExpectedDate = () => {
    if (pregnancy.expectedDate) {
      return formatDate(pregnancy.expectedDate);
    }
    
    if (pregnancy.conceptionDate) {
      try {
        const conceptionDate = new Date(pregnancy.conceptionDate);
        
        // Calculate expected date based on species
        let gestationDays = 280; // Default for cows (approximately 9 months)
        
        if (pregnancy.species) {
          const species = pregnancy.species.toLowerCase();
          if (species === 'cow' || species === 'cattle') {
            gestationDays = 280; // ~9 months
          } else if (species === 'goat') {
            gestationDays = 150; // ~5 months
          } else if (species === 'sheep') {
            gestationDays = 152; // ~5 months
          } else if (species === 'pig') {
            gestationDays = 114; // ~3.8 months
          }
        }
        
        const expectedDate = addDays(conceptionDate, gestationDays);
        return format(expectedDate, 'MMM dd, yyyy');
      } catch (error) {
        console.error('Error calculating expected date:', error);
        return t('common.notAvailable', { defaultValue: 'Not Available' });
      }
    }

    return t('common.notAvailable', { defaultValue: 'Not Available' });
  };
  
  // Calculate pregnancy progress and days remaining
  const calculatePregnancyDetails = () => {
    if (!pregnancy.conceptionDate) {
      return {
        progress: 0,
        daysRemaining: t('common.notAvailable', { defaultValue: 'Not Available' }),
        stage: 'unknown'
      };
    }
    
    try {
      const conceptionDate = new Date(pregnancy.conceptionDate);
      
      // Calculate expected date if not provided
      let expectedDate;
      if (pregnancy.expectedDate) {
        expectedDate = new Date(pregnancy.expectedDate);
      } else {
        // Calculate based on species
        let gestationDays = 280; // Default for cows
        
        if (pregnancy.species) {
          const species = pregnancy.species.toLowerCase();
          if (species === 'cow' || species === 'cattle') {
            gestationDays = 280;
          } else if (species === 'goat') {
            gestationDays = 150;
          } else if (species === 'sheep') {
            gestationDays = 152;
          } else if (species === 'pig') {
            gestationDays = 114;
          }
        }
        
        expectedDate = addDays(conceptionDate, gestationDays);
      }
      
      const today = new Date();
      
      // Calculate total duration in days
      const totalDuration = Math.floor((expectedDate.getTime() - conceptionDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Calculate days passed
      const daysPassed = Math.floor((today.getTime() - conceptionDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Calculate days remaining
      const daysRemaining = Math.max(0, Math.floor((expectedDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));
      
      // Calculate progress percentage
      const progress = Math.min(100, Math.max(0, Math.round((daysPassed / totalDuration) * 100)));
      
      // Determine stage
      let stage = 'early';
      if (progress > 66) {
        stage = 'late';
      } else if (progress > 33) {
        stage = 'mid';
      }
      
      return {
        progress,
        daysRemaining: daysRemaining.toString(),
        stage
      };
    } catch (error) {
      console.error('Error calculating pregnancy details:', error);
      return {
        progress: 0,
        daysRemaining: t('common.notAvailable', { defaultValue: 'Not Available' }),
        stage: 'unknown'
      };
    }
  };
  
  const expectedDate = getExpectedDate();
  const { progress, daysRemaining, stage } = calculatePregnancyDetails();
  
  // Get stage display text
  const getStageText = (stageKey: string) => {
    switch(stageKey) {
      case 'early':
        return t('pregnancy.stages.early', { defaultValue: 'Early Stage' });
      case 'mid':
        return t('pregnancy.stages.mid', { defaultValue: 'Middle Stage' });
      case 'late':
        return t('pregnancy.stages.late', { defaultValue: 'Late Stage' });
      default:
        return t('common.unknown', { defaultValue: 'Unknown' });
    }
  };
  
  const styles = StyleSheet.create({
    container: {
      padding: 16,
    },
    section: {
      marginBottom: 20,
      backgroundColor: colors.card,
      borderRadius: 8,
      padding: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 10,
      color: colors.text,
      textAlign: language === 'ur' ? 'right' : 'left',
    },
    row: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    label: {
      fontSize: 14,
      color: colors.textSecondary,
      textAlign: language === 'ur' ? 'right' : 'left',
    },
    value: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
      textAlign: language === 'ur' ? 'right' : 'left',
    },
    progressContainer: {
      alignSelf: language === 'ur' ? 'flex-end' : 'flex-start',
      height: 20,
      backgroundColor: colors.border,
      borderRadius: 10,
      marginTop: 10,
      overflow: 'hidden',
    },
    progressBar: {
      height: '100%',
    },
    progressText: {
      position: 'absolute', // Keep text centered over the bar
      width: '100%',
      textAlign: 'center',
      color: 'white',
      fontWeight: '600',
      fontSize: 12,
      lineHeight: 20,
      textShadowColor: 'rgba(0, 0, 0, 0.5)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 2,
    },
    urduText: {
      fontFamily: 'UrduFont',
      textAlign: 'right',
    },
    labelContainer: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      alignItems: 'center',
    },
    icon: {
      marginRight: language === 'ur' ? 0 : 8,
      marginLeft: language === 'ur' ? 8 : 0,
    },
  });

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>
          {t('pregnancy.details')}
        </Text>
        
        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <Heart size={16} color={colors.primary} style={styles.icon} />
            <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('pregnancy.animal')}</Text>
          </View>
          <Text style={[styles.value, language === 'ur' ? styles.urduText : null]}>{pregnancy.animalName || t('common.notAvailable', { defaultValue: 'Not Available' })}</Text>
        </View>

        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <User size={16} color={colors.primary} style={styles.icon} />
            <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('pregnancy.sire')}</Text>
          </View>
          <Text style={[styles.value, language === 'ur' ? styles.urduText : null]}>{pregnancy.sireName || t('common.notAvailable', { defaultValue: 'Not Available' })}</Text>
        </View>

        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <Tag size={16} color={colors.primary} style={styles.icon} />
            <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('pregnancy.species')}</Text>
          </View>
          <Text style={[styles.value, language === 'ur' ? styles.urduText : null]}>{pregnancy.species || t('common.notAvailable', { defaultValue: 'Not Available' })}</Text>
        </View>

        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <CheckCircle size={16} color={colors.primary} style={styles.icon} />
            <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('pregnancy.statusT')}</Text>
          </View>
          <Text style={[styles.value, language === 'ur' ? styles.urduText : null]}>
            {pregnancy.status ? t(`pregnancy.status.${pregnancy.status.toLowerCase()}`, { defaultValue: pregnancy.status }) : t('pregnancy.status.active', { defaultValue: 'Active' })}
          </Text>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null, {textAlign: language === 'ur' ? 'right' : 'left'}]}>
          {t('pregnancy.timeline')}
        </Text>
        
        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <Calendar size={16} color={colors.primary} style={styles.icon} />
            <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
              {t('pregnancy.conceptionDate')}
            </Text>
          </View>
          <Text style={[styles.value, language === 'ur' ? styles.urduText : null]}>{formatDate(pregnancy.conceptionDate)}</Text>
        </View>
        
        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <Baby size={16} color={colors.primary} style={styles.icon} />
            <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
              {t('pregnancy.expectedDate')}
            </Text>
          </View>
          <Text style={[styles.value, language === 'ur' ? styles.urduText : null]}>{expectedDate}</Text>
        </View>

        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <Clock size={16} color={colors.primary} style={styles.icon} />
            <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
              {t('pregnancy.daysRemaining')}
            </Text>
          </View>
          <Text style={[styles.value, language === 'ur' ? styles.urduText : null]}>{daysRemaining}</Text>
        </View>

        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <Milestone size={16} color={colors.primary} style={styles.icon} />
            <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
              {t('pregnancy.stage')}
            </Text>
          </View>
          <Text style={[styles.value, language === 'ur' ? styles.urduText : null]}>{getStageText(stage)}</Text>
        </View>
        
        <View>
          <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('pregnancy.progress')}</Text>
          <View style={styles.progressContainer}>
            <LinearGradient
              colors={['#4CAF50', '#8BC34A', '#CDDC39']}
              style={[styles.progressBar, { width: `${progress}%` }]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            />
            <Text style={styles.progressText}>{`${progress}%`}</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default ReportTab;
