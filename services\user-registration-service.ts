import {
  createUserWithEmailAndPassword,
  signOut,
  signInWithEmailAndPassword,
  sendPasswordResetEmail
} from 'firebase/auth';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  query, 
  where, 
  getDocs 
} from 'firebase/firestore';
import { auth, firestore } from '@/config/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';


export interface UserRegistrationData {
  email: string;
  name: string;
  role: 'admin' | 'caretaker';
  farmIds: string[];
  farmNames?: string[];
  phone_number?: string;
  cnic?: string;
  age?: number;
  gender?: string;
  photo?: string;
}

export interface RegistrationResult {
  success: boolean;
  userId?: string;
  tempPassword?: string;
  message: string;
}

/**
 * Register a new user using "forgot password" approach
 * Owner remains logged in during the process
 */
export async function registerUserWithEmailVerification(
  userData: UserRegistrationData,
  ownerId: string
): Promise<RegistrationResult> {
  try {
    // Validate inputs
    if (!userData.email || !userData.name || !userData.role || !userData.farmIds.length || !ownerId) {
      throw new Error('Missing required fields');
    }

    // Check if email already exists
    const existingUserQuery = query(
      collection(firestore, 'users'),
      where('email', '==', userData.email)
    );
    const existingUserSnapshot = await getDocs(existingUserQuery);

    if (!existingUserSnapshot.empty) {
      throw new Error(`The email ${userData.email} is already registered. Please use a different email address.`);
    }

    // Store current user credentials to restore session later
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('Owner must be logged in to register users');
    }

    const ownerEmail = currentUser.email;
    const ownerPassword = await AsyncStorage.getItem('ownerPassword');

    if (!ownerEmail || !ownerPassword) {
      throw new Error('Unable to restore owner session. Please log in again.');
    }

    // Generate a simple temporary password
    const tempPassword = generateTemporaryPassword(userData.name, userData.role);

    // Create user account with temporary password
    const userCredential = await createUserWithEmailAndPassword(auth, userData.email, tempPassword);
    const newUser = userCredential.user;

    // Create user document in Firestore
    const timestamp = Date.now();
    const userDoc = {
      id: newUser.uid,
      email: userData.email,
      name: userData.name,
      role: userData.role,
      language: 'en',
      preferAudio: false,
      offlineMode: false,
      emailVerified: false,
      createdAt: timestamp,
      updatedAt: timestamp,
      assignedFarms: userData.farmIds,
      farmIds: userData.farmIds,
      ownerId: ownerId,
      isEmployee: true,
      status: 'pending_password_reset', // User needs to reset password
      tempPassword: tempPassword, // Store for reference
      // Additional employee fields
      phone_number: userData.phone_number || '',
      cnic: userData.cnic || '',
      age: userData.age || 0,
      gender: userData.gender || '',
      joining_date: new Date(),
      photo: userData.photo || '',
      // Set password reset expiry (24 hours from now)
      passwordResetExpiry: timestamp + (24 * 60 * 60 * 1000)
    };

    await setDoc(doc(firestore, 'users', newUser.uid), userDoc);

    // Update farm staff arrays
    for (const farmId of userData.farmIds) {
      try {
        const farmRef = doc(firestore, 'farms', farmId);
        const farmDoc = await getDoc(farmRef);

        if (farmDoc.exists()) {
          const farmData = farmDoc.data();
          const currentStaff = farmData.staff || [];
          const currentStaffCount = farmData.staffCount || 0;

          if (!currentStaff.includes(newUser.uid)) {
            await updateDoc(farmRef, {
              staff: [...currentStaff, newUser.uid],
              staffCount: currentStaffCount + 1,
              updatedAt: timestamp
            });
          }
        }
      } catch (farmError) {
        console.error(`Error updating farm ${farmId}:`, farmError);
      }
    }

    // Send password reset email (this opens in browser like your screenshot)
    try {
      await sendPasswordResetEmail(auth, userData.email);
      console.log('Password reset email sent successfully');
    } catch (emailError) {
      console.error('Error sending password reset email:', emailError);
      // Continue execution - we'll show the credentials in alert
    }

    // Restore owner session
    try {
      await signOut(auth);
      await signInWithEmailAndPassword(auth, ownerEmail, ownerPassword);
    } catch (sessionError) {
      console.error('Error restoring owner session:', sessionError);
    }

    return {
      success: true,
      userId: newUser.uid,
      tempPassword: tempPassword,
      message: 'User registered successfully. Password reset email sent.'
    };

  } catch (error: any) {
    console.error('Error registering user:', error);

    // Handle specific Firebase errors
    if (error.code === 'auth/email-already-in-use') {
      return {
        success: false,
        message: `The email ${userData.email} is already registered. Please use a different email address.`
      };
    }

    if (error.code === 'auth/weak-password') {
      return {
        success: false,
        message: 'The password is too weak. Please try again.'
      };
    }

    return {
      success: false,
      message: error.message || 'Failed to register user. Please try again.'
    };
  }
}

/**
 * Generate temporary password based on user data
 */
function generateTemporaryPassword(name: string, role: string): string {
  if (role === 'admin') {
    // For admins, generate a random secure password
    return Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-4).toUpperCase() + '123!';
  } else {
    // For caretakers, create a simple password using name
    let namePrefix = name.substring(0, 4).toLowerCase().replace(/[^a-z]/g, '');
    while (namePrefix.length < 4) {
      namePrefix += 'x';
    }
    const randomNumbers = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return namePrefix + randomNumbers + '!';
  }
}

/**
 * Complete user setup after password reset
 * This function is called after user resets their password via email
 */
export async function completeUserSetup(email: string): Promise<{ success: boolean; message: string }> {
  try {
    // Find user by email
    const userQuery = query(
      collection(firestore, 'users'),
      where('email', '==', email)
    );
    const userSnapshot = await getDocs(userQuery);

    if (userSnapshot.empty) {
      throw new Error('User not found');
    }

    const userDoc = userSnapshot.docs[0];
    const userData = userDoc.data();

    // Update user status to active after password reset
    await updateDoc(doc(firestore, 'users', userData.id), {
      emailVerified: true,
      status: 'active',
      tempPassword: null, // Remove temporary password
      passwordResetExpiry: null, // Remove expiry
      updatedAt: Date.now()
    });

    return {
      success: true,
      message: 'Account setup completed successfully. You can now log in with your new password.'
    };

  } catch (error: any) {
    console.error('Error completing user setup:', error);
    return {
      success: false,
      message: error.message || 'Failed to complete user setup. Please try again.'
    };
  }
}
