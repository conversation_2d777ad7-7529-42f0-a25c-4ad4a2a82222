import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useAnimalStore } from '@/store/animal-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { useAuthStore } from '@/store/auth-store';
import StatisticsCard from './StatisticsCard';
import { useTranslation } from '@/hooks/useTranslation';
import { Animal } from '@/types/animal';
import { HealthCheck } from '@/types/healthCheck';
import { AnimalRecord } from '@/types/record';

import { MaterialCommunityIcons } from '@expo/vector-icons';

interface DashboardStatsProps {
  filteredAnimals: Animal[];
  filteredHealthChecks: HealthCheck[];
  filteredRecords: AnimalRecord[];
  onRefresh?: () => Promise<void>;
}

const DashboardStats: React.FC<DashboardStatsProps> = ({
  filteredAnimals,
  filteredHealthChecks,
  filteredRecords,
  onRefresh
}) => {
  const router = useRouter();
  const { user } = useAuthStore();
  const { fetchAnimals } = useAnimalStore();
  const { fetchHealthChecks } = useHealthCheckStore();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { t } = useTranslation();

  // Calculate statistics using filtered data with null checks
  const totalAnimals = filteredAnimals?.length || 0;
  const totalHealthChecks = filteredHealthChecks?.length || 0;
  const totalRecords = filteredRecords?.length || 0;

  // Calculate sick animals with null check
  const sickAnimals = filteredAnimals?.filter(animal => animal.hasHealthIssues)?.length || 0;

  // Calculate healthy animals
  const healthyAnimals = totalAnimals - sickAnimals;

  // Handle refresh
  const handleRefresh = async () => {
    if (isRefreshing || !user) return;

    setIsRefreshing(true);
    try {
      if (onRefresh) {
        await onRefresh();
      } else {
        await Promise.all([
          fetchAnimals(user.id),
          fetchHealthChecks()
        ]);
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Navigation handlers
  const handleAddAnimal = () => {
    // router.push('/animals/add');
    router.push('/(tabs)/animals');
  };

  const handleViewAnimals = () => {
    router.push('/(tabs)/animals');
  };

  const handleViewHealthChecks = () => {
    router.push('/health-checks');
  };

  const handleViewAlerts = () => {
    router.push('/alerts');
  };

  return (
    <View style={styles.container}>
      <View style={styles.statsGrid}>
        <View style={styles.statsRow}>
          <StatisticsCard
            title={t('dashboard.sickAnimals')}
            value={sickAnimals}
            backgroundColor="#F87171"
            icon={<MaterialCommunityIcons name="paw-off" size={22} color="white" />}
            onPress={handleViewAlerts}
          />
          <StatisticsCard
            title={t('dashboard.healthyAnimals')}
            value={healthyAnimals}
            backgroundColor="#10B981"
            icon={<MaterialCommunityIcons name="dog" size={22} color="white" />}
            onPress={handleViewAnimals}
          />
        </View>

        <View style={styles.statsRow}>
          <StatisticsCard
            title={t('dashboard.stats.totalAnimals')}
            value={totalAnimals}
            backgroundColor="#8B5CF6"
            icon={<MaterialCommunityIcons name="paw" size={22} color="white" />}
            onPress={handleAddAnimal}
          />
          <StatisticsCard
            title={t('dashboard.healthChecks')}
            value={totalHealthChecks}
            backgroundColor="#3B82F6"
            icon={<MaterialCommunityIcons name="medical-bag" size={22} color="white" />}
            onPress={handleViewHealthChecks}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 14,
    width: '100%',
  },
  statsGrid: {
    marginBottom: 20,
    paddingHorizontal: 2,
    width: '100%',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    gap: 10,
    width: '100%',
  }
});

export default DashboardStats;







