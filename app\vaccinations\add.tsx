import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
} from 'react-native';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAnimalStore } from '@/store/animal-store';
import { useRecordStore } from '@/store/record-store';
import { updateAnimalVaccinationStatus } from '@/utils/healthStatus';
import { RecordType } from '@/types/record';
import Button from '@/components/Button';
import DatePickerInput from '@/components/DatePickerInput';
import AnimalDropdown from '@/components/AnimalDropdown';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Calendar,
  Save,
  Syringe,
  FileText,
  Pill,
  Clipboard,
} from 'lucide-react-native';

export default function AddVaccinationScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const animalId = params.animalId as string;

  const { animals, fetchAnimals, updateAnimal } = useAnimalStore();
  const { addRecord, isLoading } = useRecordStore();

  const [selectedAnimalId, setSelectedAnimalId] = useState(animalId || '');
  const [vaccineName, setVaccineName] = useState('');
  const [batchNumber, setBatchNumber] = useState('');
  const [manufacturer, setManufacturer] = useState('');
  const [notes, setNotes] = useState('');
  const [vaccinationDate, setVaccinationDate] = useState(new Date());
  const [expiryDate, setExpiryDate] = useState(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)); // 30 days from now
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch animals if needed
  useEffect(() => {
    if (animals.length === 0) {
      fetchAnimals();
    }
  }, [animals.length, fetchAnimals]);

  const resetForm = () => {
    setSelectedAnimalId('');
    setVaccineName('');
    setBatchNumber('');
    setManufacturer('');
    setNotes('');
    setVaccinationDate(new Date());
    setExpiryDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000));
  };

  const validateForm = () => {
    if (!selectedAnimalId) {
      Alert.alert('Error', 'Please select an animal');
      return false;
    }

    if (!vaccineName.trim()) {
      Alert.alert('Error', 'Please enter a vaccine name');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Create the vaccination record
      const recordData = {
        animalId: selectedAnimalId,
        date: vaccinationDate.getTime(),
        type: RecordType.VACCINATION,
        title: `Vaccination: ${vaccineName}`,
        description: notes,
        symptoms: [],
        vaccinations: [
          {
            id: Date.now().toString(),
            name: vaccineName,
            date: vaccinationDate.getTime(),
            expiryDate: expiryDate.getTime(),
            batchNumber: batchNumber || undefined,
            manufacturer: manufacturer || undefined,
            notes: notes || undefined,
          },
        ],
      };

      // Add the record
      await addRecord(recordData);

      // Get the animal
      const animal = animals.find(a => a.id === selectedAnimalId);

      if (animal) {
        // Update the animal's vaccination status
        const vaccinationUpdates = updateAnimalVaccinationStatus(animal, vaccinationDate.getTime());
        await updateAnimal(selectedAnimalId, vaccinationUpdates);
      }

      Alert.alert(
        'Success',
        'Vaccination record added successfully',
        [
          {
            text: 'Add Another',
            onPress: () => {
              resetForm();
            },
          },
          {
            text: 'Done',
            onPress: () => router.back(),
            style: 'default',
          },
        ]
      );
    } catch (error) {
      console.error('Error adding vaccination record:', error);
      Alert.alert('Error', 'Failed to add vaccination record');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Add Vaccination',
          headerTitleStyle: { fontWeight: 'bold' },
        }}
      />

      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          {/* Animal Selection */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={colors.primary} />
              <Text style={styles.label}>Animal</Text>
            </View>
            <AnimalDropdown
              animals={animals}
              selectedAnimalId={selectedAnimalId}
              onSelectAnimal={setSelectedAnimalId}
            />
          </View>

          {/* Vaccination Date */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Calendar size={24} color={colors.primary} />
              <Text style={styles.label}>Vaccination Date</Text>
            </View>
            <DatePickerInput
              label=""
              value={vaccinationDate}
              onChange={setVaccinationDate}
            />
          </View>

          {/* Vaccine Name */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Syringe size={24} color={colors.primary} />
              <Text style={styles.label}>Vaccine Name</Text>
            </View>
            <TextInput
              style={styles.input}
              value={vaccineName}
              onChangeText={setVaccineName}
              placeholder="Enter vaccine name"
              placeholderTextColor={colors.textSecondary}
            />
          </View>

          {/* Batch Number */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Clipboard size={24} color={colors.primary} />
              <Text style={styles.label}>Batch Number (Optional)</Text>
            </View>
            <TextInput
              style={styles.input}
              value={batchNumber}
              onChangeText={setBatchNumber}
              placeholder="Enter batch number"
              placeholderTextColor={colors.textSecondary}
            />
          </View>

          {/* Manufacturer */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Pill size={24} color={colors.primary} />
              <Text style={styles.label}>Manufacturer (Optional)</Text>
            </View>
            <TextInput
              style={styles.input}
              value={manufacturer}
              onChangeText={setManufacturer}
              placeholder="Enter manufacturer"
              placeholderTextColor={colors.textSecondary}
            />
          </View>

          {/* Expiry Date */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Calendar size={24} color={colors.primary} />
              <Text style={styles.label}>Expiry Date</Text>
            </View>
            <DatePickerInput
              label=""
              value={expiryDate}
              onChange={setExpiryDate}
            />
          </View>

          {/* Notes */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={colors.primary} />
              <Text style={styles.label}>Notes (Optional)</Text>
            </View>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Enter any additional notes"
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <LinearGradient
              colors={[colors.primary, colors.primaryDark]}
              style={styles.saveButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Save size={24} color="white" />
              <Text style={styles.saveButtonText}>Save Vaccination</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginLeft: 8,
  },
  input: {
    backgroundColor: colors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    padding: 12,
    fontSize: 16,
    color: colors.text,
  },
  textArea: {
    minHeight: 100,
  },
  saveButton: {
    marginTop: 24,
    marginBottom: 40,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
