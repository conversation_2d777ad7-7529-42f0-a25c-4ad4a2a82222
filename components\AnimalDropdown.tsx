import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  Image,
} from 'react-native';
import { colors } from '@/constants/colors';
import { ChevronDown, X } from 'lucide-react-native';
import { Animal } from '@/types/animal';

interface AnimalDropdownProps {
  animals: Animal[];
  selectedAnimalId: string;
  onSelectAnimal: (animalId: string) => void;
}

const AnimalDropdown: React.FC<AnimalDropdownProps> = ({
  animals,
  selectedAnimalId,
  onSelectAnimal,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  
  const selectedAnimal = animals.find(animal => animal.id === selectedAnimalId);
  
  // Get placeholder image for animals without an image
  const getPlaceholderImage = (species: string) => {
    switch (species.toLowerCase()) {
      case 'cow':
        return 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D';
      case 'goat':
        return 'https://images.unsplash.com/photo-1524024973431-2ad916746881?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8Z29hdCUyMGZhY2V8ZW58MHx8MHx8fDA%3D';
      case 'poultry':
        return 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8Y2hpY2tlbiUyMGZhY2V8ZW58MHx8MHx8fDA%3D';
      case 'fish':
        return 'https://images.unsplash.com/photo-1524704796725-9fc3044a58b2?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGZpc2glMjBmYWNlfGVufDB8fDB8fHww';
      default:
        return 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D';
    }
  };
  
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.dropdownButton}
        onPress={() => setModalVisible(true)}
      >
        {selectedAnimal ? (
          <View style={styles.selectedAnimalContainer}>
            <Image 
              source={{ uri: selectedAnimal.imageUri || getPlaceholderImage(selectedAnimal.species) }} 
              style={styles.selectedAnimalImage} 
              resizeMode="cover"
            />
            <Text style={styles.selectedAnimalText}>
              {selectedAnimal.name} ({selectedAnimal.species})
            </Text>
          </View>
        ) : (
          <Text style={styles.placeholderText}>Select an animal</Text>
        )}
        <ChevronDown size={20} color={colors.textSecondary} />
      </TouchableOpacity>
      
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Animal</Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <X size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={animals}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.animalItem,
                    selectedAnimalId === item.id && styles.selectedAnimalItem
                  ]}
                  onPress={() => {
                    onSelectAnimal(item.id);
                    setModalVisible(false);
                  }}
                >
                  <Image 
                    source={{ uri: item.imageUri || getPlaceholderImage(item.species) }} 
                    style={styles.animalImage} 
                    resizeMode="cover"
                  />
                  <View style={styles.animalInfo}>
                    <Text style={[
                      styles.animalName,
                      selectedAnimalId === item.id && styles.selectedAnimalName
                    ]}>
                      {item.name}
                    </Text>
                    <Text style={styles.animalSpecies}>
                      {item.species}{item.breed ? ` • ${item.breed}` : ''}
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    No animals found. Please add an animal first.
                  </Text>
                </View>
              }
              style={styles.animalsList}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    padding: 12,
  },
  selectedAnimalContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedAnimalImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedAnimalText: {
    fontSize: 16,
    color: colors.text,
  },
  placeholderText: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.background,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  animalsList: {
    maxHeight: 400,
  },
  animalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  selectedAnimalItem: {
    backgroundColor: colors.primaryLight,
  },
  animalImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 4,
  },
  selectedAnimalName: {
    color: colors.primary,
  },
  animalSpecies: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default AnimalDropdown;
