import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Lock, Eye, EyeOff, CheckCircle, XCircle } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { verifyEmailAndSetupPassword } from '@/services/user-registration-service';

export default function SetupPasswordScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const themedColors = useThemeColors();
  const params = useLocalSearchParams<{ email?: string; code?: string }>();

  const [email, setEmail] = useState(params.email || '');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Password validation states
  const [passwordValidation, setPasswordValidation] = useState({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false
  });

  useEffect(() => {
    validatePassword(password);
  }, [password]);

  const validatePassword = (pwd: string) => {
    setPasswordValidation({
      minLength: pwd.length >= 8,
      hasUppercase: /[A-Z]/.test(pwd),
      hasLowercase: /[a-z]/.test(pwd),
      hasNumber: /\d/.test(pwd),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(pwd)
    });
  };

  const isPasswordValid = () => {
    return Object.values(passwordValidation).every(Boolean);
  };

  const handleSetupPassword = async () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    if (!isPasswordValid()) {
      Alert.alert('Error', 'Please ensure your password meets all requirements');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    setIsLoading(true);

    try {
      const result = await verifyEmailAndSetupPassword(email, password);
      
      if (result.success) {
        Alert.alert(
          'Success',
          result.message,
          [
            {
              text: 'OK',
              onPress: () => router.replace('/(auth)/login')
            }
          ]
        );
      } else {
        Alert.alert('Error', result.message);
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to set up password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const ValidationItem = ({ isValid, text }: { isValid: boolean; text: string }) => (
    <View style={styles.validationItem}>
      {isValid ? (
        <CheckCircle size={16} color={themedColors.success} />
      ) : (
        <XCircle size={16} color={themedColors.error} />
      )}
      <Text style={[
        styles.validationText,
        { color: isValid ? themedColors.success : themedColors.error }
      ]}>
        {text}
      </Text>
    </View>
  );

  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: 'Set Up Your Password',
          headerShown: true,
        }}
      />

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Welcome!</Text>
          <Text style={styles.subtitle}>
            Please set up your secure password to complete your account setup.
          </Text>
        </View>

        <View style={styles.form}>
          {/* Email Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email Address</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              editable={!params.email} // Disable if email is provided via params
            />
          </View>

          {/* Password Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>New Password</Text>
            <View style={styles.passwordContainer}>
              <Lock size={20} color={themedColors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.passwordInput}
                placeholder="Enter your new password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                style={styles.eyeIcon}
              >
                {showPassword ? (
                  <EyeOff size={20} color={themedColors.textSecondary} />
                ) : (
                  <Eye size={20} color={themedColors.textSecondary} />
                )}
              </TouchableOpacity>
            </View>
          </View>

          {/* Password Validation */}
          {password.length > 0 && (
            <View style={styles.validationContainer}>
              <Text style={styles.validationTitle}>Password Requirements:</Text>
              <ValidationItem isValid={passwordValidation.minLength} text="At least 8 characters" />
              <ValidationItem isValid={passwordValidation.hasUppercase} text="One uppercase letter" />
              <ValidationItem isValid={passwordValidation.hasLowercase} text="One lowercase letter" />
              <ValidationItem isValid={passwordValidation.hasNumber} text="One number" />
              <ValidationItem isValid={passwordValidation.hasSpecialChar} text="One special character" />
            </View>
          )}

          {/* Confirm Password Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Confirm Password</Text>
            <View style={styles.passwordContainer}>
              <Lock size={20} color={themedColors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.passwordInput}
                placeholder="Confirm your new password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                style={styles.eyeIcon}
              >
                {showConfirmPassword ? (
                  <EyeOff size={20} color={themedColors.textSecondary} />
                ) : (
                  <Eye size={20} color={themedColors.textSecondary} />
                )}
              </TouchableOpacity>
            </View>
          </View>

          {/* Password Match Indicator */}
          {confirmPassword.length > 0 && (
            <View style={styles.matchIndicator}>
              {password === confirmPassword ? (
                <View style={styles.matchSuccess}>
                  <CheckCircle size={16} color={themedColors.success} />
                  <Text style={[styles.matchText, { color: themedColors.success }]}>
                    Passwords match
                  </Text>
                </View>
              ) : (
                <View style={styles.matchError}>
                  <XCircle size={16} color={themedColors.error} />
                  <Text style={[styles.matchText, { color: themedColors.error }]}>
                    Passwords do not match
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* Setup Button */}
          <TouchableOpacity
            style={[
              styles.setupButton,
              (!isPasswordValid() || password !== confirmPassword || isLoading) && styles.setupButtonDisabled
            ]}
            onPress={handleSetupPassword}
            disabled={!isPasswordValid() || password !== confirmPassword || isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.setupButtonText}>Set Up Password</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: themedColors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: themedColors.text,
    backgroundColor: themedColors.card,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    backgroundColor: themedColors.card,
  },
  inputIcon: {
    marginLeft: 12,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: themedColors.text,
  },
  eyeIcon: {
    padding: 12,
  },
  validationContainer: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  validationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 12,
  },
  validationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  validationText: {
    fontSize: 14,
    marginLeft: 8,
  },
  matchIndicator: {
    marginBottom: 20,
  },
  matchSuccess: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  matchError: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  matchText: {
    fontSize: 14,
    marginLeft: 8,
  },
  setupButton: {
    backgroundColor: themedColors.primary,
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  setupButtonDisabled: {
    backgroundColor: themedColors.border,
    opacity: 0.7,
  },
  setupButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
