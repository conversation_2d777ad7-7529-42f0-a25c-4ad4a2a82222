import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { Farm, FarmStatus } from '@/types/farm';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import { Save, MapPin, Building2 } from 'lucide-react-native';
import Input from '@/components/Input';
import Button from '@/components/Button';
import { useToast } from '@/contexts/ToastContext';
import LoadingIndicator from '@/components/LoadingIndicator';

export default function EditFarmScreen() {
  const { t, language } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { user } = useAuthStore();
  const { getFarm, updateFarm, isLoading } = useFarmStore();
  const { showToast } = useToast();
  const themedColors = useThemeColors();

  const [farm, setFarm] = useState<Farm | undefined>(getFarm(id));
  const [name, setName] = useState('');
  const [location, setLocation] = useState('');
  const [status, setStatus] = useState<FarmStatus>(FarmStatus.ACTIVE);
  const [errors, setErrors] = useState({
    name: '',
    location: '',
  });

  useEffect(() => {
    if (farm) {
      setName(farm.name);
      setLocation(farm.location);
      setStatus(farm.status);
    }
  }, [farm]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: '',
      location: '',
    };

    if (!name.trim()) {
      newErrors.name = t('farms.nameRequired');
      isValid = false;
    }

    if (!location.trim()) {
      newErrors.location = t('farms.locationRequired');
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    if (!user || !farm) return;

    try {
      await updateFarm(id, {
        name,
        location,
        status,
      });

      showToast({
        type: 'success',
        title: t('common.success'),
        message: t('farms.updateSuccess'),
      });

      // Navigate back to the farm detail screen
      router.back();
    } catch (error) {
      console.error('Error updating farm:', error);
      showToast({
        type: 'error',
        title: t('common.error'),
        message: t('common.errorOccurred'),
      });
    }
  };

  if (!farm) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: t('farms.editFarm'),
          headerBackTitle: t('common.back'),
        }}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.name')}*</Text>
            </View>
            <Input
              value={name}
              onChangeText={setName}
              placeholder={t('farms.namePlaceholder')}
              error={errors.name}
              leftIcon={<Building2 size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.location')}*</Text>
            </View>
            <Input
              value={location}
              onChangeText={setLocation}
              placeholder={t('farms.locationPlaceholder')}
              error={errors.location}
              leftIcon={<MapPin size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.status')}</Text>
            </View>
            <View style={styles.statusButtons}>
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.ACTIVE && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.ACTIVE)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.ACTIVE && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusActive')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.INACTIVE && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.INACTIVE)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.INACTIVE && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusInactive')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.PENDING && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.PENDING)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.PENDING && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusPending')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.COMPLETED && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.COMPLETED)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.COMPLETED && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusCompleted')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t('common.save')}
              onPress={handleSave}
              isLoading={isLoading}
              leftIcon={<Save size={20} color="white" />}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  labelContainer: {
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
  },
  statusButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  statusButton: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  statusButtonSelected: {
    backgroundColor: themedColors.primary,
    borderColor: themedColors.primary,
  },
  statusButtonText: {
    color: themedColors.text,
    fontSize: 14,
    fontWeight: '500',
  },
  statusButtonTextSelected: {
    color: 'white',
  },
  buttonContainer: {
    marginTop: 24,
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
});
