import React,{useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, ScrollView } from 'react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';

interface SpeciesOption {
  id: string;
  label: string;
  imageUrl: string;
}

interface SpeciesFilterRowProps {
  selectedSpecies: string | null;
  onSelectSpecies: (species: string | null) => void;
}

const SpeciesFilterRow: React.FC<SpeciesFilterRowProps> = ({
  selectedSpecies,
  onSelectSpecies,
}) => {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);

  const speciesOptions: SpeciesOption[] = [
    { id: 'Cow', label: 'Cow', imageUrl: 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D' },
    { id: 'Goat', label: 'Goat', imageUrl: 'https://images.unsplash.com/photo-1524024973431-2ad916746881?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8Z29hdCUyMGZhY2V8ZW58MHx8MHx8fDA%3D' },
    { id: 'Poultry', label: 'Poultry', imageUrl: 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8Y2hpY2tlbiUyMGZhY2V8ZW58MHx8MHx8fDA%3D' },
    { id: 'Fish', label: 'Fish', imageUrl: 'https://images.unsplash.com/photo-1524704796725-9fc3044a58b2?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGZpc2glMjBmYWNlfGVufDB8fDB8fHww' },
  ];

  const renderSpeciesLabel = useCallback((species: SpeciesOption) => {
    if (!species || !species.id) return species?.label || '';
    try {
      const translationKey = `animals.${species.id.toLowerCase()}`;
      const translatedText = t(translationKey);
      return translatedText || species.label;
    } catch (error) {
      console.error('Error in species translation:', error);
      return species.label;
    }
  }, [t]);

  return (
    <View style={styles.container}>
      <View style={styles.scrollContent}>
        {speciesOptions.map((species) => (
          <TouchableOpacity
            key={species.id}
            style={[
              styles.speciesItem,
              selectedSpecies === species.id && styles.selectedSpeciesItem
            ]}
            onPress={() => onSelectSpecies(
              selectedSpecies === species.id ? null : species.id
            )}
          >
            <View style={[
              styles.imageContainer,
              selectedSpecies === species.id && styles.selectedImageContainer
            ]}>
              <Image
                source={{ uri: species.imageUrl }}
                style={styles.speciesImage}
                resizeMode="cover"
              />
            </View>
            <Text style={[
              styles.speciesText,
              selectedSpecies === species.id && styles.selectedSpeciesText,
              styles.languageSpecificText
            ]}>
              {renderSpeciesLabel(species)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  languageSpecificText: {
    ...(language === 'ur' && { fontFamily: 'System', textAlign: 'right' as 'right' }),
  },
  container: {
    marginBottom: 16,
    backgroundColor: themedColors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
    width: '100%',
  },
  scrollContent: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    padding: 8,
    width: '100%',
  },
  speciesItem: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: 2,
  },
  selectedSpeciesItem: {
    opacity: 1,
  },
  imageContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: themedColors.border,
    marginBottom: 4,
  },
  selectedImageContainer: {
    borderColor: themedColors.primary,
    borderWidth: 3,
  },
  speciesImage: {
    width: '100%',
    height: '100%',
  },
  speciesText: {
    fontSize: 12,
    color: themedColors.text,
    textAlign: 'center',
  },
  selectedSpeciesText: {
    fontWeight: 'bold',
    color: themedColors.primary,
  },
});

export default SpeciesFilterRow;
