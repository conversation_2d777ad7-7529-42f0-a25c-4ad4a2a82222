import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, useColorScheme, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { HealthCheck } from '@/types/healthCheck';
import {
  Calendar,
  AlertTriangle,
  Thermometer,
  Scale,
  CheckCircle,
  Stethoscope
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSettingsStore } from '@/store/settings-store';
import { useAnimalStore } from '@/store/animal-store';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';

interface HealthCheckCardProps {
  healthCheck: HealthCheck;
  showAnimalName?: boolean;
  animalName?: string;
}

const HealthCheckCard: React.FC<HealthCheckCardProps> = ({
  healthCheck,
  showAnimalName = false,
  animalName
}) => {
  const router = useRouter();
  const systemColorScheme = useColorScheme();
  const { darkMode } = useSettingsStore();
  const { animals } = useAnimalStore();
  const themedColors = useThemeColors();
  const { t, language } = useTranslation();

  // Find the animal associated with this health check
  const animal = animals.find(a => a.id === healthCheck.animalId);

  // Use the app's dark mode setting, falling back to system preference
  const isDarkMode = themedColors.isDarkMode; // Use themedColors.isDarkMode
  const styles = getStyles(themedColors, language);

  const handlePress = () => {
    router.push(`/health-checks/${healthCheck.id}`);
  };

  // Get placeholder image for animals without an image - focused on faces
  const getPlaceholderImage = (species: string) => {
    switch (species?.toLowerCase()) {
      case 'cow':
        return 'https://images.unsplash.com/photo-**********-29f4545e9d53?q=80&w=1000&auto=format&fit=crop';
      case 'goat':
        return 'https://images.unsplash.com/photo-1524024973431-2ad916746881?q=80&w=1000&auto=format&fit=crop';
      case 'poultry':
        return 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1000&auto=format&fit=crop';
      case 'fish':
        return 'https://images.unsplash.com/photo-1524704796725-9fc3044a58b2?q=80&w=1000&auto=format&fit=crop';
      default:
        return 'https://images.unsplash.com/photo-**********-29f4545e9d53?q=80&w=1000&auto=format&fit=crop';
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    // Use the appropriate locale based on the selected language
    const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';

    // Format the date according to the locale
    try {
      return date.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      // Fallback to a simple format if locale-specific formatting fails
      return `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`;
    }
  };



  // Get abnormal parameters for visual indicators
  const getAbnormalParameters = () => {
    const abnormalParams = [];

    if (healthCheck.appetite !== 'normal') abnormalParams.push('appetite');
    if (healthCheck.hydration !== 'normal') abnormalParams.push('hydration');
    if (healthCheck.respiration !== 'normal') abnormalParams.push('respiration');
    if (healthCheck.gait !== 'normal') abnormalParams.push('gait');
    if (healthCheck.fecal !== 'normal') abnormalParams.push('fecal');
    if (healthCheck.coat !== 'normal') abnormalParams.push('coat');
    if (healthCheck.eyes !== 'normal') abnormalParams.push('eyes');
    if (healthCheck.ears !== 'normal') abnormalParams.push('ears');

    return abnormalParams;
  };

  const abnormalParams = getAbnormalParameters();

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <LinearGradient
        colors={isDarkMode ? 
          [themedColors.cardOpaque || 'rgba(55,65,81,0.8)', themedColors.cardTransparent || 'rgba(55,65,81,0.4)'] : 
          [themedColors.cardOpaque || 'rgba(255,255,255,0.8)', themedColors.cardTransparent || 'rgba(255,255,255,0.4)']}
        style={styles.cardGradient}
      >
        <View style={styles.cardContent}>
          <View style={styles.cardLayout}>
            {/* Left side - Animal image */}
            <View style={styles.imageSection}>
              {animal && (
                animal.imageUri ? (
                  <Image
                    source={{ uri: animal.imageUri }}
                    style={styles.animalImage}
                    resizeMode="cover"
                  />
                ) : (
                  <Image
                    source={{ uri: getPlaceholderImage(animal.species) }}
                    style={styles.animalImage}
                    resizeMode="cover"
                  />
                )
              )}
            </View>

            {/* Right side - Content */}
            <View style={styles.contentSection}>
              <View style={styles.header}>
                <View style={styles.recordTypeIconContainer}>
                  <Stethoscope size={18} color={themedColors.primary} />
                </View>
                <View style={styles.headerContent}>
                  <View style={styles.typeContainer}>
                    <Text style={[styles.type, { color: themedColors.primary, fontSize: 15 }]}>{t('healthChecks.title')}</Text>
                  </View>
                  {healthCheck.abnormalities ? (
                    <View style={styles.abnormalBadge}>
                      <AlertTriangle size={12} color="white" />
                      <Text style={styles.abnormalText}>{t('healthChecks.abnormal').toUpperCase()}</Text>
                    </View>
                  ) : (
                    <View style={styles.normalBadge}>
                      <CheckCircle size={12} color="white" />
                      <Text style={styles.normalText}>{t('healthChecks.normal').toUpperCase()}</Text>
                    </View>
                  )}
                </View>
              </View>

              {showAnimalName && (
                <Text style={[styles.animalName, language === 'ur' ? styles.urduText : null]}>
                  {animalName || t('animals.unknownAnimal')}
                </Text>
              )}

              <View style={styles.metricsContainer}>
                {healthCheck.temperature && (
                  <View style={styles.metricItem}>
                    <Thermometer size={16} color={
                      healthCheck.temperature < 38 || healthCheck.temperature > 39.5
                        ? themedColors.error
                        : themedColors.textSecondary
                    } />
                    <Text style={styles.metricText}>
                      <Text style={[
                        styles.metricValue,
                        (healthCheck.temperature < 38 || healthCheck.temperature > 39.5) && styles.abnormalValue
                      ]}>
                        {healthCheck.temperature}°C
                      </Text>
                    </Text>
                  </View>
                )}

                {healthCheck.weight && (
                  <View style={styles.metricItem}>
                    <Scale size={16} color={themedColors.textSecondary} />
                    <Text style={styles.metricText}>
                      <Text style={styles.metricValue}>{healthCheck.weight} kg</Text>
                    </Text>
                  </View>
                )}
              </View>

              {abnormalParams.length > 0 && (
                <View style={styles.symptomsContainer}>
                  <Text style={styles.symptomsLabel}>{t('healthChecks.abnormalities')}:</Text>
                  <Text style={styles.symptoms}>
                    {abnormalParams.map(param => {
                      switch(param) {
                        case 'appetite': return healthCheck.appetite;
                        case 'hydration': return healthCheck.hydration;
                        case 'respiration': return healthCheck.respiration;
                        case 'gait': return healthCheck.gait;
                        case 'fecal': return healthCheck.fecal;
                        case 'coat': return healthCheck.coat;
                        case 'eyes': return healthCheck.eyes;
                        case 'ears': return healthCheck.ears;
                        default: return param;
                      }
                    }).join(', ')}
                  </Text>
                </View>
              )}

              {healthCheck.notes && (
                <Text style={[styles.description, language === 'ur' ? styles.urduText : null]} numberOfLines={2}>
                  {healthCheck.notes}
                </Text>
              )}

              <View style={styles.dateContainer}>
                <Calendar size={12} color={themedColors.textSecondary} />
                <Text style={[styles.date, { color: themedColors.textSecondary }]}>{formatDate(healthCheck.date)}</Text>
              </View>
            </View>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: themedColors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: themedColors.isDarkMode ? 0.2 : 0.1,
    shadowRadius: 3,
    elevation: themedColors.isDarkMode ? 1 : 2,
    overflow: 'hidden',
    // maxHeight:180
  },
  cardGradient: {
    width: '100%',
  },
  cardContent: {
    padding: 12,
  },
  cardLayout: {
    flexDirection: 'row',
  },
  imageSection: {
    width: 70,
    height: 70,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 12,
  },
  animalImage: {
    width: '100%',
    height: '100%',
  },
  contentSection: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  recordTypeIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: themedColors.isDarkMode ? 'rgba(167, 139, 113, 0.2)' : 'rgba(79, 70, 229, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    alignSelf: 'flex-end',
    marginTop: 6,
  },
  date: {
    fontSize: 12,
    // color set dynamically
  },
  abnormalBadge: {
    backgroundColor: themedColors.error,
    paddingHorizontal: 4,
    paddingVertical: 4,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
    marginRight:-10
  },
  abnormalText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  normalBadge: {
    backgroundColor: themedColors.success,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  normalText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  animalName: {
    fontSize: 15,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 6,
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  type: {
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  metricsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 8,
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  metricText: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  metricValue: {
    color: themedColors.text,
    fontWeight: '500',
  },
  abnormalValue: {
    color: themedColors.error,
  },
  symptomsContainer: {
    marginTop: 2,
    marginBottom: 6,
    backgroundColor: themedColors.isDarkMode ? 'rgba(239, 68, 68, 0.15)' : 'rgba(254, 226, 226, 0.3)',
    padding: 8,
    borderRadius: 6,
  },
  symptomsLabel: {
    fontSize: 13,
    color: themedColors.textSecondary,
    marginBottom: 2,
    fontWeight: '500',
  },
  symptoms: {
    fontSize: 13,
    color: themedColors.text,
  },
  description: {
    fontSize: 13,
    color: themedColors.text,
    marginBottom: 8,
    lineHeight: 18,
  },
});

export default HealthCheckCard;