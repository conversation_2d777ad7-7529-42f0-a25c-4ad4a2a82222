import { Symptom, Disease } from '@/types/symptom';

export const symptoms: Symptom[] = [
  {
    id: "s1",
    name: "Fever",
    description: "Elevated body temperature above normal range",
    bodyPart: "Whole body",
    severity: "medium"
  },
  {
    id: "s2",
    name: "Coughing",
    description: "Forceful expulsion of air from the lungs",
    bodyPart: "Respiratory",
    severity: "medium"
  },
  {
    id: "s3",
    name: "Lethargy",
    description: "Lack of energy, unusual tiredness",
    bodyPart: "Whole body",
    severity: "medium"
  },
  {
    id: "s4",
    name: "Diarrhea",
    description: "Loose, watery stools occurring more frequently than normal",
    bodyPart: "Digestive",
    severity: "high"
  },
  {
    id: "s5",
    name: "Vomiting",
    description: "Forceful expulsion of stomach contents through the mouth",
    bodyPart: "Digestive",
    severity: "high"
  },
  {
    id: "s6",
    name: "Nasal discharge",
    description: "Fluid coming from the nose",
    bodyPart: "Respiratory",
    severity: "low"
  },
  {
    id: "s7",
    name: "<PERSON><PERSON><PERSON>",
    description: "Difficulty walking or standing, limping",
    bodyPart: "Musculoskeletal",
    severity: "high"
  },
  {
    id: "s8",
    name: "Weight loss",
    description: "Unintended decrease in body weight",
    bodyPart: "Whole body",
    severity: "high"
  }
];

export const diseases: Disease[] = [
  {
    id: "d1",
    name: "Foot and Mouth Disease",
    description: "Highly contagious viral disease affecting cloven-hoofed animals",
    symptoms: ["s1", "s4", "s7"],
    treatment: "Supportive care, anti-inflammatory medication",
    prevention: "Vaccination, biosecurity measures",
    zoonotic: false,
    severity: "high",
    species: ["Cattle", "Sheep", "Goats", "Pigs"]
  },
  {
    id: "d2",
    name: "Bovine Respiratory Disease",
    description: "Complex of respiratory diseases affecting cattle",
    symptoms: ["s1", "s2", "s3", "s6"],
    treatment: "Antibiotics, anti-inflammatory drugs",
    prevention: "Vaccination, proper ventilation, stress reduction",
    zoonotic: false,
    severity: "medium",
    species: ["Cattle"]
  },
  {
    id: "d3",
    name: "Mastitis",
    description: "Inflammation of the mammary gland in dairy animals",
    symptoms: ["s1", "s3"],
    treatment: "Antibiotics, frequent milking, supportive care",
    prevention: "Proper milking hygiene, regular udder checks",
    zoonotic: false,
    severity: "medium",
    species: ["Cattle", "Sheep", "Goats"]
  },
  {
    id: "d4",
    name: "Brucellosis",
    description: "Bacterial infection affecting multiple species",
    symptoms: ["s1", "s3", "s8"],
    treatment: "Antibiotics, supportive care",
    prevention: "Vaccination, testing and culling infected animals",
    zoonotic: true,
    severity: "high",
    species: ["Cattle", "Sheep", "Goats", "Pigs"]
  }
];