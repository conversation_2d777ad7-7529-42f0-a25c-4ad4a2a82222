import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
  Image,
} from 'react-native';
import { ChevronDown, Search, X } from 'lucide-react-native';
import { Animal } from '@/types/animal';
import { useAnimalStore } from '@/store/animal-store';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useAuthStore } from '@/store/auth-store';

interface AnimalSearchDropdownProps {
  placeholder: string;
  animals?: Animal[];  // Make animals optional
  value: string;
  onSelect: (animalId: string) => void;
  onChangeText?: (text: string) => void;
  farmId?: string;     // Add farmId prop for filtering
  onAnimalSelect?: (id: string, name: string) => void; // Add callback for name
}

const AnimalSearchDropdown: React.FC<AnimalSearchDropdownProps> = ({
  placeholder,
  animals,  // No default value - let it be undefined
  value,
  onSelect,
  onChangeText,
  farmId,
  onAnimalSelect,
}) => {
  const { t, language } = useTranslation();
  const { animals: allAnimals, fetchAnimals, fetchAnimalsByFarm } = useAnimalStore();
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAnimal, setSelectedAnimal] = useState<Animal | null>(null);
  const themeHookColors = useThemeColors();
  const { user } = useAuthStore();

  // Fetch animals if not provided
  useEffect(() => {
    if (!animals && user) {
      fetchAnimals(user.id);
    }
  }, [user, animals]);

  // Fetch animals for specific farm when farmId changes
  useEffect(() => {
    if (farmId && !animals) {
      fetchAnimalsByFarm(farmId);
    }
  }, [farmId, fetchAnimalsByFarm, animals]);

  useEffect(() => {
    // Find the selected animal when value changes
    const animalsList = animals || allAnimals;
    if (value) {
      const animal = animalsList.find(a => a.id === value);
      setSelectedAnimal(animal || null);
    } else {
      setSelectedAnimal(null);
    }
  }, [value, animals, allAnimals]);

  const colors = useMemo(() => ({ // Define colors based on theme, memoized
    background: themeHookColors.background,
    card: themeHookColors.card,
    text: themeHookColors.text,
    textSecondary: themeHookColors.textSecondary,
    border: themeHookColors.border,
    primary: themeHookColors.primary,
    primaryLight: themeHookColors.primaryLight || (themeHookColors.isDarkMode ? '#A5B4FC' : '#E0E7FF'), // Fallback for primaryLight
  }), [themeHookColors]);

  // Determine which animals to use - provided animals or all animals filtered by farmId
  const animalsToUse = useMemo(() => {
    // If animals are explicitly provided (even if empty), use those
    if (animals !== undefined) {
      return animals;
    }

    // If farmId is provided, filter by farm
    if (farmId) {
      return allAnimals.filter(animal => animal.farmId === farmId);
    }

    return allAnimals;
  }, [animals, allAnimals, farmId]);

  // Reset selected animal when animals list changes
  useEffect(() => {
    if (selectedAnimal && !animalsToUse.some(animal => animal.id === selectedAnimal.id)) {
      setSelectedAnimal(null);
      onSelect('');
    }
  }, [animalsToUse]);

  // Filter animals based on search query
  const filteredAnimals = animalsToUse.filter(animal =>
    animal.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    animal.species.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (animal.breed && animal.breed.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Handle animal selection
  const handleSelect = (animal: Animal) => {
    onSelect(animal.id);
    setSelectedAnimal(animal);
    setModalVisible(false);
    setSearchQuery('');
    
    // Call the additional callback if provided
    if (onAnimalSelect) {
      onAnimalSelect(animal.id, animal.name);
    }
  };

  // Handle search input change
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    if (onChangeText) {
      onChangeText(text);
    }
  };

  // Close modal
  const handleClose = () => {
    setModalVisible(false);
    setSearchQuery('');
  };

  // Clear selection
  const handleClear = () => {
    onSelect('');
    setSelectedAnimal(null);
    setSearchQuery('');
  };

  // Get placeholder image for animals without an image - focused on faces
  const getPlaceholderImage = (species: string) => {
    switch (species.toLowerCase()) {
      case 'cow':
        return 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D';
      case 'goat':
        return 'https://images.unsplash.com/photo-1524024973431-2ad916746881?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8Z29hdCUyMGZhY2V8ZW58MHx8MHx8fDA%3D';
      case 'poultry':
        return 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8Y2hpY2tlbiUyMGZhY2V8ZW58MHx8MHx8fDA%3D';
      case 'fish':
        return 'https://images.unsplash.com/photo-1524704796725-9fc3044a58b2?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGZpc2glMjBmYWNlfGVufDB8fDB8fHww';
      default:
        return 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D';
    }
  };

  const styles = useMemo(() => getStyles(colors, themeHookColors.isDarkMode), [colors, themeHookColors.isDarkMode]);

  return (
    <View style={styles.container}>
      {/* Dropdown Button */}
      <TouchableOpacity
        style={[styles.dropdownButton, { backgroundColor: colors.card, borderColor: colors.border }]}
        onPress={() => setModalVisible(true)}
        activeOpacity={0.7}
      >
        <View style={styles.searchContainer}>
          <View style={styles.iconContainer}>
            <Search size={20} color={colors.textSecondary} />
          </View>
 
          {selectedAnimal ? (
            <View style={styles.selectedAnimalContainer}>
              <Image
                source={{ uri: selectedAnimal.imageUri || getPlaceholderImage(selectedAnimal.species) }}
                style={[styles.selectedAnimalImage, { borderColor: colors.border }]}
                resizeMode="cover"
              />
              <Text style={styles.selectedAnimalText}>
                {selectedAnimal.name} ({selectedAnimal.species})
              </Text>
              <TouchableOpacity onPress={handleClear} style={styles.clearButton}>
                <X size={16} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.placeholderContainer}>
              <Text style={[styles.placeholderText, language === 'ur' ? styles.urduText : null]}>{placeholder}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>

      {/* Modal for dropdown - Fixed for Android */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={handleClose}
        statusBarTranslucent={true}
        hardwareAccelerated={true}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
              <Text style={[styles.modalTitle, language === 'ur' ? styles.urduText : null]}>{t('animals.selectAnimal')}</Text>
              <TouchableOpacity
                onPress={handleClose}
                style={styles.closeButtonContainer}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
 
            <View style={[styles.searchModalContainer, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
              <Search size={20} color={colors.textSecondary} style={styles.searchIcon} />
              <TextInput
                placeholder={t('animals.searchAnimals')}
                placeholderTextColor={colors.textSecondary}
                style={[styles.searchInput, { color: colors.text }, language === 'ur' ? styles.urduInput : null]}
                value={searchQuery}
                onChangeText={handleSearchChange}
                autoCapitalize="none"
                autoCorrect={false}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <X size={20} color={colors.textSecondary} />
                </TouchableOpacity>
              )}
            </View> 

            <FlatList
              data={filteredAnimals}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.animalItem,
                    { borderBottomColor: colors.border },
                    value === item.id && [styles.selectedAnimalItem, { backgroundColor: colors.primaryLight }]
                  ]}
                  onPress={() => handleSelect(item)}
                >
                  <Image
                    source={{ uri: item.imageUri || getPlaceholderImage(item.species) }}
                    style={[styles.animalImage, { borderColor: colors.border }]}
                    resizeMode="cover"
                  />
                  <View style={styles.animalInfo}>
                    <Text style={[
                      styles.animalName,
                      { color: colors.text },
                      value === item.id && [styles.selectedAnimalName, { color: colors.primary }]
                    ]}>
                      {item.name}
                    </Text>
                    <Text style={[styles.animalSpecies, { color: colors.textSecondary }]}>
                      {(t(`animals.${item.species.toLowerCase()}`) || item.species).charAt(0).toUpperCase() + (t(`animals.${item.species.toLowerCase()}`) || item.species).slice(1)}{item.breed ? ` • ${language === 'ur' ? (t(`animals.${item.breed}`) || item.breed) : item.breed}` : ''}
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
              ListEmptyComponent={
                <View style={[styles.emptyContainer, { backgroundColor: colors.background }]}>
                  <Text style={styles.emptyText}>
                    {searchQuery ?
                      `${t('animals.noAnimalsFound')} "${searchQuery}"` :
                      farmId ?
                        `No animals found for this farm. Please add animals to this farm first.` :
                        'No animals available'
                    }
                  </Text>
                </View>
              }
              style={styles.animalsList}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const getStyles = (colors: any, isDarkMode: boolean) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  urduInput: {
    textAlign: 'right',
  },
  container: {
    flex: 1,
  },
  dropdownButton: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    elevation: 2, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    // backgroundColor will be set dynamically
    // borderColor will be set dynamically
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 0,
    height: 48,
  },
  selectedAnimalContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
    height: 36,
  },
  iconContainer: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedAnimalImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 8,
    borderWidth: 1,
    // borderColor will be set dynamically
  },
  selectedAnimalText: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
    textAlignVertical: 'center',
    lineHeight: 20,
  },
  clearButton: {
    padding: 4,
  },
  placeholderContainer: {
    flex: 1,
    marginLeft: 8,
    height: 32,
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    elevation: 1000, // High elevation for Android
    zIndex: 1000, // High z-index for iOS
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
    minHeight: 300,
    elevation: 1001, // Higher than overlay
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    // backgroundColor will be set by theme
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    // borderBottomColor will be set by theme
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text, // Use themed text color
  },
  closeButtonContainer: {
    padding: 4,
    borderRadius: 4,
  },
  searchModalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    // backgroundColor will be set by theme
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16, 
    // color will be set by theme
  },
  animalsList: {
    maxHeight: 400,
  },
  animalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    paddingVertical: 16,
    borderBottomWidth: 1,
    // borderBottomColor will be set by theme
  },
  selectedAnimalItem: {
    // backgroundColor will be set by theme
  },
  animalImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
    marginRight: 12,
    borderWidth: 1,
    // borderColor will be set dynamically
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 18,
    fontWeight: '600', 
    // color will be set by theme
    marginBottom: 2,
  },
  selectedAnimalName: {
    // color will be set by theme
  },
  animalSpecies: {
    fontSize: 15,
    // color will be set by theme
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    // backgroundColor will be set by theme
  },
  emptyText: {
    fontSize: 16,
    color: colors.textSecondary, // Use themed color
    textAlign: 'center',
  },
});

export default AnimalSearchDropdown;
