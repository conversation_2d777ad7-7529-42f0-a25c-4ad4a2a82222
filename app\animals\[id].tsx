import React, { useEffect, useState } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack, useFocusEffect } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAnimalStore } from '@/store/animal-store';
import { useRecordStore } from '@/store/record-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { useMilkingStore } from '@/store/milking-store';
import Button from '@/components/Button';
import { RecordCard } from '@/components/RecordCard';
import HealthCheckCard from '@/components/HealthCheckCard';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { Edit, Trash2, Plus, FileText, Activity, Calendar, ArrowLeft, Users, Milk } from 'lucide-react-native';
import { HealthCheck } from '@/types/healthCheck';
import { AnimalRecord } from '@/types/record';
import { MilkingRecord } from '@/types/milking';
import { useAuthStore } from '@/store/auth-store';
import { getTotalExpensesByAnimal } from '@/services/expense-service';
import { useThemeColors } from '@/hooks/useThemeColors';
import { DollarSign } from 'lucide-react-native';
import { Animal } from '@/types/animal'; // Ensure Animal type is imported and includes fatherId, motherId
import PregnancyStatus from '@/components/AnimalDetails/PregnancyStatus';
import { getCurrencySymbol } from '@/utils/currency-utils';

export default function AnimalDetailScreen() {
  const { t, language } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getAnimal, deleteAnimal, isLoading: animalLoading, fetchAnimals } = useAnimalStore();
  const { records, isLoading: recordsLoading, fetchRecords } = useRecordStore();
  const { healthChecks, isLoading: healthChecksLoading, fetchHealthChecks } = useHealthCheckStore(); // Ensure useAnimalStore().animals contains all animals
  const { records: milkingRecords, isLoading: milkingLoading, fetchRecordsByAnimal } = useMilkingStore();
  const { user } = useAuthStore();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);

  const [animal, setAnimal] = useState<Animal | null>(null);
  const [animalRecords, setAnimalRecords] = useState<AnimalRecord[]>([]);
  const [animalHealthChecks, setAnimalHealthChecks] = useState<HealthCheck[]>([]);
  const [animalMilkingRecords, setAnimalMilkingRecords] = useState<MilkingRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalExpenses, setTotalExpenses] = useState<{ amount: number; currency: string } | null>(null);
  const [isLoadingExpenses, setIsLoadingExpenses] = useState(false);

  // Family tree states
  const [father, setFather] = useState<Animal | null | 'external' | 'loading'>( 'loading');
  const [mother, setMother] = useState<Animal | null | 'external' | 'loading'>('loading');
  const [children, setChildren] = useState<Animal[]>([]);

  // Add this effect to refresh data when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      const loadAnimal = async () => {
        if (user?.id) {
          await fetchAnimals(user.id);
          const updatedAnimal = getAnimal(id);
          setAnimal(updatedAnimal as Animal | null); // Cast to Animal
          
          // Load expenses data here, inside the existing callback
          if (updatedAnimal?.id) {
            setIsLoadingExpenses(true);
            try {
              console.log('Fetching expenses for animal:', updatedAnimal.id);
              const expenses = await getTotalExpensesByAnimal(updatedAnimal.id);
              console.log('Animal expenses result:', expenses);
              setTotalExpenses(expenses);
            } catch (error) {
              console.error('Error fetching animal expenses:', error);
            } finally {
              setIsLoadingExpenses(false);
            }
          }
        }
      };

      loadAnimal();
    }, [id, user?.id, getAnimal, fetchAnimals]) // Added getAnimal, fetchAnimals
  );

  useEffect(() => {
    if (id) {
      setAnimal(getAnimal(id));

      // Load records, health checks, and milking records for this animal
      const loadData = async () => {
        await fetchRecords(id);
        await fetchHealthChecks(id);
        await fetchRecordsByAnimal(id);
      };

      loadData();
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      // Update records when the records state changes
      setAnimalRecords(records.filter(record => record.animalId === id));

      // Filter health checks for this animal
      setAnimalHealthChecks(healthChecks.filter(check => check.animalId === id));

      // Filter milking records for this animal
      setAnimalMilkingRecords(milkingRecords.filter(record => record.animalId === id));
    }

    // Load family members when animal and allAnimals are available
    if (animal && useAnimalStore.getState().animals.length > 0) {
      const allFetchedAnimals = useAnimalStore.getState().animals;
      // Load Father
      if (animal.fatherId === 'BOUGHT_EXTERNAL') {
        setFather('external');
      } else if (animal.fatherId) {
        const f = allFetchedAnimals.find(a => a.id === animal.fatherId);
        setFather(f || null); // null if ID exists but animal not found
      } else {
        setFather(null); // No father specified
      }

      // Load Mother
      if (animal.motherId === 'BOUGHT_EXTERNAL') {
        setMother('external');
      } else if (animal.motherId) {
        const m = allFetchedAnimals.find(a => a.id === animal.motherId);
        setMother(m || null); // null if ID exists but animal not found
      } else {
        setMother(null); // No mother specified
      }

      // Load Children
      const animalChildren = allFetchedAnimals.filter(
        a => a.fatherId === animal.id || a.motherId === animal.id
      );
      setChildren(animalChildren);
    }

  }, [id, records, healthChecks, milkingRecords]);

  // Refresh milking data when screen becomes focused (after adding records)
  useFocusEffect(
    React.useCallback(() => {
      if (id) {
        fetchRecordsByAnimal(id);
      }
    }, [id])
  );

  const handleEdit = () => {
    router.push(`/animals/${id}/edit`);
  };

  const handleDelete = () => {
    if (!animal) return;

    Alert.alert(
      t('animals.deleteTitle'),
      t('animals.deleteConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          onPress: async () => {
            try {
              // Show loading state
              setLoading(true);
              
              // Delete the animal
              await deleteAnimal(id);
              
              // Show success message
              Alert.alert(
                t('common.success'),
                t('animals.deleteSuccess'),
                [
                  {
                    text: t('common.ok'),
                    onPress: () => router.back(),
                  },
                ]
              );
            } catch (error) {
              console.error('Error deleting animal:', error);
              Alert.alert(t('common.error'), t('animals.deleteError'));
            } finally {
              setLoading(false);
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  const handleAddRecord = () => {
    router.push(`/records/add?animalId=${id}`);
  };

  const handleAddHealthCheck = () => {
    router.push(`/health-checks/add?animalId=${id}`);
  };

  const handleViewAllRecords = () => {
    router.push(`/animals/${id}/records`);
  };

  const handleViewAllHealthChecks = () => {
    router.push(`/animals/${id}/health-checks`);
  };

  const handleAddMilking = () => {
    if (animal?.farmId) {
      router.push(`/milking/add?farmId=${animal.farmId}&animalId=${id}`);
    }
  };

  const handleViewAllMilking = () => {
    router.push(`/milking/list?animalId=${id}`);
  };

  const formatAge = (age?: number) => {
    if (!age) return t('common.unknown');
    return age === 1 ? t('animals.oneYear', { age: 1 }) : t('animals.multipleYears', { age });
  };

  const formatDate = (timestamp?: number | null) => {
    if (!timestamp) return t('animals.notScheduled');
    
    try {
      const date = new Date(timestamp);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date detected:', timestamp);
        return t('common.invalidDate');
      }
      
      return date.toLocaleDateString(language === 'ur' ? 'ur-PK' : 'en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Error formatting date:', error, timestamp);
      return t('common.error');
    }
  };

  const getDaysUntilNextCheck = (nextCheckDate?: number) => {
    if (!nextCheckDate) return null;

    const now = Date.now();
    const diffTime = nextCheckDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  const daysUntilNextCheck = getDaysUntilNextCheck(animal?.nextHealthCheck);

  const getMilkQualityColor = (quality: string): string => {
    switch (quality) {
      case 'excellent':
        return '#10B981';
      case 'good':
        return '#3B82F6';
      case 'fair':
        return '#F59E0B';
      case 'poor':
        return '#EF4444';
      default:
        return themedColors.textSecondary;
    }
  };

  if (animalLoading || recordsLoading || healthChecksLoading) {
    return <LoadingIndicator fullScreen message={t('animals.loadingDetails')} />;
  }

  if (!animal) {
    return (
      <EmptyState
        title={t('animals.notFound')}
        message={t('animals.notFoundMessage')}
        actionLabel={t('common.goBack')}
        onAction={() => router.back()}
      />
    );
  }

  // Removed separate useEffect for loading expenses

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: animal.name,
        headerTitleAlign: language === 'ur' ? 'center' : 'left',
        headerTitleStyle: { fontWeight: 'bold' },
        headerStyle: { backgroundColor: themedColors.background },
        headerTintColor: themedColors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: Platform.OS === 'ios' ? 8 : 0 }}>
            <ArrowLeft size={24} color={themedColors.text} />
          </TouchableOpacity>
        )
      }} />
      <ScrollView style={styles.scrollView}>
        <View style={styles.imageContainer}>
          {animal.imageUri ? (
            <Image
              source={{ uri: animal.imageUri }}
              style={styles.image}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderImage}>
              <Text style={styles.placeholderText}>{animal.name.charAt(0)}</Text>
            </View>
          )}
          <View style={styles.imageActions}>
            <TouchableOpacity
              style={styles.editButton}
              onPress={handleEdit}
            >
              <Edit size={20} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.deleteButton, { zIndex: 999 }]}
              onPress={handleDelete} // Corrected: was handleEdit
              activeOpacity={0.7}
            >
              <Trash2 size={20} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.detailsContainer}>
          <Text style={[styles.name, language === 'ur' ? styles.urduText : null]}>{animal.name}</Text>
          <View style={[styles.speciesContainer, language === 'ur' && {justifyContent:'flex-end'}]}>
            <Text style={[styles.species]}>{(t(`animals.${animal.species.toLowerCase()}`) || animal.species).charAt(0).toUpperCase() + (t(`animals.${animal.species.toLowerCase()}`) || animal.species).slice(1)} </Text>
            {animal.breed && <Text style={[styles.breed, {marginHorizontal:5}]}> {language === 'ur' ? (t(`animals.${animal.breed}`) || animal.breed) : animal.breed}</Text>}
          </View>

          <View style={styles.infoGrid}>
            <View style={[styles.infoItem, language === 'ur' && {justifyContent:'flex-end'}]}>
              <Text style={styles.infoLabel}>{t('animals.age')}</Text>
              <Text style={styles.infoValue}>{formatAge(animal.age)}</Text>
            </View>
            <View style={[styles.infoItem, language === 'ur' && {justifyContent:'flex-end'}]}>
              <Text style={styles.infoLabel}>{t('animals.gender')}</Text>
              <Text style={styles.infoValue}>
                {t(`animals.${animal.gender}`)}
              </Text>
            </View>
            {animal.weight && (
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, language === 'ur' ? styles.urduText : null]}>{t('animals.animalWeight')}</Text>
                <Text style={[styles.infoValue, language === 'ur' ? styles.urduText : null]}>
                  {language === 'ur' ? `${animal.weight} ${t('common.kg')}` : `${animal.weight} kg`}
                </Text>
              </View>
            )}
            {animal.tagId && (
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, language === 'ur' ? styles.urduText : null]}>{t('animals.animalTagId')}</Text>
                <Text style={[styles.infoValue, language === 'ur' ? styles.urduText : null]}>{animal.tagId}</Text>
              </View>
            )}
            <View style={styles.infoItem}>
              <Text style={[styles.infoLabel, language === 'ur' ? styles.urduText : null]}>
                {t('expenses.totalExpenses')}
              </Text>
              <View style={[styles.expenseValueContainer, language === 'ur' ? styles.urduExpenseValueContainer : null]}>
                {/* <DollarSign size={16} color={themedColors.textSecondary} /> */}
                <Text style={[styles.infoValue, language === 'ur' ? styles.urduText : null]}>
                  {isLoadingExpenses ? '...' : totalExpenses !== null ? `${getCurrencySymbol(totalExpenses.currency)} ${totalExpenses.amount.toFixed(2)}` : t('common.unknown')}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.healthCheckInfo}>
            <View style={styles.nextCheckContainer}>
              <Text style={[styles.nextCheckLabel, language === 'ur' ? styles.urduText : null]}>{t('animals.nextCheck')}:</Text>
              <Text style={[styles.nextCheckDate, language === 'ur' ? styles.urduText : null]}>{formatDate(animal.nextHealthCheck)}</Text>
              {daysUntilNextCheck !== null && (
                <Text style={[
                  styles.daysRemaining,
                  daysUntilNextCheck <= 0 ? styles.overdue :
                  daysUntilNextCheck <= 1 ? styles.soon : null,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {daysUntilNextCheck <= 0
                    ? t('animals.overdue')
                    : daysUntilNextCheck === 1
                      ? t('animals.tomorrow')
                      : t('animals.inDays', { days: daysUntilNextCheck })}
                </Text>
              )}
            </View>
            <TouchableOpacity
              style={styles.scheduleButton}
              onPress={handleAddHealthCheck}
            >
              <Calendar size={16} color="white" />
              <Text style={[styles.scheduleButtonText, language === 'ur' ? styles.urduText : null]}>
                {daysUntilNextCheck !== null && daysUntilNextCheck <= 0
                  ? t('animals.performCheckNow')
                  : t('animals.scheduleCheck')}
              </Text>
            </TouchableOpacity>
          </View>

        </View>
        {animal && animal.age && animal.age > 1.5 && animal.gender === 'female' && (
          <PregnancyStatus animalId={animal.id} farmId={animal.farmId} />
        )}

        <View style={styles.healthChecksSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.title')}</Text>
            <View style={styles.sectionActions}>
              {animalHealthChecks.length > 0 && (
                <TouchableOpacity onPress={handleViewAllHealthChecks}>
                  <Text style={[styles.viewAllText, language === 'ur' ? styles.urduText : null]}>{t('common.viewAll')}</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.addButton}
                onPress={handleAddHealthCheck}
              >
                <Plus size={16} color="white" />
              </TouchableOpacity>
            </View>
          </View>

          {animalHealthChecks.length === 0 ? (
            <EmptyState
              title={t('healthChecks.noChecks')}
              message={t('healthChecks.performRegularChecks')}
              actionLabel={t('healthChecks.addCheck')}
              onAction={handleAddHealthCheck}
              icon={<Activity size={48} color={themedColors.primary} />}
            />
          ) : (
            <View style={styles.healthChecksList}>
              {animalHealthChecks
                .sort((a, b) => b.date - a.date)
                .slice(0, 2)
                .map((check) => (
                  <HealthCheckCard key={check.id} healthCheck={check} />
                ))
              }
            </View>
          )}
        </View>

        <View style={styles.recordsSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('records.title')}</Text>
            <View style={styles.sectionActions}>
              {animalRecords.length > 0 && (
                <TouchableOpacity onPress={handleViewAllRecords}>
                  <Text style={[styles.viewAllText, language === 'ur' ? styles.urduText : null]}>{t('common.viewAll')}</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.addButton}
                onPress={handleAddRecord}
              >
                <Plus size={16} color="white" />
              </TouchableOpacity>
            </View>
          </View>

          {animalRecords.length === 0 ? (
            <EmptyState
              title={t('records.noRecords')}
              message={t('records.addRecordsMessage')}
              actionLabel={t('records.addRecord')}
              onAction={handleAddRecord}
              icon={<FileText size={48} color={themedColors.primary} />}
            />
          ) : (
            <View style={styles.recordsList}>
              {animalRecords
                .sort((a, b) => b.date - a.date)
                .slice(0, 2)
                .map(record => (
                  <RecordCard key={record.id} record={record} />
                ))
              }
            </View>
          )}
        </View>

        {/* Milking Section - Only show for dairy animals */}
        {animal && (animal.species.toLowerCase() === 'cow' || animal.species.toLowerCase() === 'buffalo' || animal.species.toLowerCase() === 'goat') && (
          <View style={styles.milkingSection}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('milking.title')}</Text>
              <View style={styles.sectionActions}>
                {animalMilkingRecords.length > 0 && (
                  <TouchableOpacity onPress={handleViewAllMilking}>
                    <Text style={[styles.viewAllText, language === 'ur' ? styles.urduText : null]}>{t('common.viewAll')}</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={handleAddMilking}
                >
                  <Plus size={16} color="white" />
                </TouchableOpacity>
              </View>
            </View>

            {animalMilkingRecords.length === 0 ? (
              <EmptyState
                title={t('milking.noRecords')}
                message={t('milking.addYourFirstRecord')}
                actionLabel={t('milking.addRecord')}
                onAction={handleAddMilking}
                icon={<Milk size={48} color={themedColors.primary} />}
              />
            ) : (
              <View style={styles.milkingList}>
                {animalMilkingRecords
                  .sort((a, b) => b.date - a.date)
                  .slice(0, 3)
                  .map((record) => (
                    <TouchableOpacity
                      key={record.id}
                      style={[styles.milkingCard, language === 'ur' && styles.urduCard]}
                      onPress={() => router.push(`/milking/${record.id}`)}
                    >
                      <View style={[styles.milkingCardHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
                        <View style={[styles.milkingInfo, language === 'ur' && { alignItems: 'flex-end' }]}>
                          <Text style={[styles.milkingDate, language === 'ur' && styles.urduText]}>
                            {formatDate(record.date)} • {t(`milking.session.${record.session}`)}
                          </Text>
                          <View style={[styles.milkingQuantityRow, language === 'ur' && { flexDirection: 'row-reverse' }]}>
                            <Milk size={16} color={themedColors.primary} />
                            <Text style={[styles.milkingQuantity, language === 'ur' && styles.urduText]}>
                              {record.quantity.toFixed(1)} {t('milking.liters')}
                            </Text>
                          </View>
                        </View>
                        <View style={[
                          styles.milkingQualityBadge,
                          { backgroundColor: getMilkQualityColor(record.quality) + '20' }
                        ]}>
                          <Text style={[
                            styles.milkingQualityText,
                            { color: getMilkQualityColor(record.quality) },
                            language === 'ur' && styles.urduText
                          ]}>
                            {t(`milking.quality.${record.quality}`)}
                          </Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))
                }
              </View>
            )}
          </View>
        )}

        {/* Family Tree Section */}
        <View style={styles.familyTreeSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('animals.familyTree')}</Text>
            <Users size={24} color={themedColors.primary} />
          </View>

          {(father === 'loading' || mother === 'loading') && <ActivityIndicator color={themedColors.primary} style={{ marginVertical: 20 }}/>}

          <View style={styles.parentsContainer}>
            {father !== 'loading' && (
              <View style={styles.familyMemberCard}>
                <Text style={styles.familyMemberRole}>{t('animals.father')}</Text>
                {father === 'external' ? (
                  <Text style={styles.familyMemberText}>{t('animals.boughtFromExternal')}</Text>
                ) : father && father.id ? (
                  <>
                    <Image source={{ uri: father.imageUri || undefined }} style={styles.familyMemberImageMissing} />
                    <Text style={styles.familyMemberName}>{father.name}</Text>
                  </>
                ) : (
                  <Text style={styles.familyMemberText}>{t('common.unknown')}</Text>
                )}
              </View>
            )}

            {mother !== 'loading' && (
              <View style={styles.familyMemberCard}>
                <Text style={styles.familyMemberRole}>{t('animals.mother')}</Text>
                {mother === 'external' ? (
                  <Text style={styles.familyMemberText}>{t('animals.boughtFromExternal')}</Text>
                ) : mother && mother.id ? (
                  <>
                    <Image source={{ uri: mother.imageUri || undefined }} style={styles.familyMemberImageMissing} />
                    <Text style={styles.familyMemberName}>{mother.name}</Text>
                  </>
                ) : (
                  <Text style={styles.familyMemberText}>{t('common.unknown')}</Text>
                )}
              </View>
            )}
          </View>

          {children.length > 0 && (
            <>
              <Text style={styles.childrenTitle}>{t('animals.children')}</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.childrenContainer}>
                {children.map(child => (
                  <TouchableOpacity key={child.id} style={styles.familyMemberCard} onPress={() => router.push(`/animals/${child.id}`)}>
                    <Image source={{ uri: child.imageUri || undefined }} style={styles.familyMemberImageMissing} />
                    <Text style={styles.familyMemberName}>{child.name}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </>
          )}

          {father !== 'loading' && mother !== 'loading' && father === null && mother === null && children.length === 0 && (
             <Text style={styles.noFamilyText}>{t('animals.noFamilyInfo')}</Text>
          )}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title={t('healthChecks.performCheck')}
          onPress={handleAddHealthCheck}
          variant="primary" // Keep primary for distinction
          size="small"      // Change size to small
          leftIcon={<Activity size={16} color="white" />} // Adjust icon size
          style={styles.footerButton}
        />
        <Button
          title={t('records.addRecord')}
          onPress={handleAddRecord}
          variant="outline" // Keep outline
          size="small"      // Change size to small
          leftIcon={<FileText size={16} color={themedColors.primary} />} // Adjust icon size
          style={styles.footerButton}
        />
      
      </View>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    height: 250,
    width: '100%',
    backgroundColor: themedColors.primaryLight,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: themedColors.primaryLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 72,
    fontWeight: 'bold',
    color: themedColors.primary,
  },
  imageActions: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
    gap: 8,
  },
  editButton: {
    backgroundColor: 'rgba(167, 139, 113, 0.7)',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButton: {
    backgroundColor: 'rgba(239, 68, 68, 0.7)',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  detailsContainer: {
    padding: 16,
    backgroundColor: themedColors.card,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 4,
  },
  speciesContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  species: {
    fontSize: 16,
    color: themedColors.textSecondary,
  },
  breed: {
    fontSize: 16,
    color: themedColors.textSecondary,
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
    marginBottom: 16,
  },
  infoItem: {
    width: '50%',
    paddingHorizontal: 8,
    marginBottom: 16,
  },
  infoLabel: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
  },
  healthCheckInfo: {
    backgroundColor: themedColors.primaryLight,
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
  },
  nextCheckContainer: {
    marginBottom: 12,
  },
  nextCheckLabel: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 4,
  },
  nextCheckDate: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 4,
  },
  daysRemaining: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  overdue: {
    color: themedColors.error,
    fontWeight: '500',
  },
  soon: {
    color: themedColors.warning,
    fontWeight: '500',
  },
  scheduleButton: {
    backgroundColor: themedColors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
  },
  scheduleButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  healthChecksSection: {
    marginTop: 16,
    // backgroundColor: themedColors.card,
    paddingVertical: 16,
  },
  recordsSection: {
    marginTop: 16,
    // backgroundColor: themedColors.card,
    paddingVertical: 16,
    marginBottom: 16,
  },
  milkingSection: {
    marginTop: 16,
    paddingVertical: 16,
    marginBottom: 16,
  },
  milkingList: {
    paddingHorizontal: 16,
  },
  milkingCard: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  urduCard: {
    alignItems: 'flex-end',
  },
  milkingCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  milkingInfo: {
    flex: 1,
  },
  milkingDate: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 8,
  },
  milkingQuantityRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  milkingQuantity: {
    fontSize: 16,
    fontWeight: '600',
    color: themedColors.text,
  },
  milkingQualityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  milkingQualityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  sectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  viewAllText: {
    fontSize: 14,
    color: themedColors.primary,
    fontWeight: '500',
  },
  addButton: {
    backgroundColor: themedColors.primary,
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  healthChecksList: {
    paddingHorizontal: 16,
  },
  recordsList: {
    paddingHorizontal: 16,
  },
  footer: {
    padding: 16,
    backgroundColor: themedColors.card,
    borderTopWidth: 1,
    borderTopColor: themedColors.border,
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'space-around',
  },
  footerButton: {
    // flex: 1, // Removed to make buttons take minimal width
  },
  expenseValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  urduExpenseValueContainer: {
    flexDirection: 'row-reverse',
  },
  familyTreeSection: {
    marginTop: 16,
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: themedColors.card,
  },
  parentsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  familyMemberCard: {
    alignItems: 'center',
    padding: 8,
    backgroundColor: themedColors.background,
    borderRadius: 8,
    minWidth: 120,
    marginHorizontal: 4, // For children scrollview
  },
  familyMemberImageMissing: { // Placeholder style for missing image
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: themedColors.border, // Placeholder color
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  familyMemberName: {
    fontSize: 14,
    fontWeight: '500',
    color: themedColors.text,
    marginTop: 4,
  },
  familyMemberRole: {
    fontSize: 12,
    color: themedColors.textSecondary,
    marginBottom: 8,
  },
  familyMemberText: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  childrenTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 8,
    marginTop: 16,
  },
  childrenContainer: {
    paddingVertical: 8,
  },
  noFamilyText: {
    textAlign: 'center',
    color: themedColors.textSecondary,
    marginTop: 16,
  }
});
