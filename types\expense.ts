export enum ExpenseCategory {
  ANIMAL_PURCHASE = 'ANIMAL_PURCHASE',
  FEED = 'FEED',
  MEDICATION = 'MEDICATION',
  VACCINATION = 'VACCINATION',
  VETERINARY = 'VETERINARY',
  EQUIPMENT = 'EQUIPMENT',
  UTILITIES = 'UTILITIES',
  LABOR = 'LABOR',
  MAINTENANCE = 'MAINTENANCE',
  OTHER = 'OTHER',
}

export enum PaymentMethod {
  CASH = 'CASH',
  CARD = 'CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  OTHER = 'OTHER',
}

// Import currency types from currency utils
import { CURRENCIES } from '@/utils/currency-utils';

// Define supported currency types - now supports all currencies
export type SupportedCurrency = keyof typeof CURRENCIES;

export interface Expense {
  id: string;
  amount: number;
  currency: SupportedCurrency;
  date: number;
  category: ExpenseCategory;
  description?: string;
  farmId: string;
  farmName?: string;
  animalId?: string;
  animalName?: string;
  staffId?: string;
  staffName?: string;
  receiptImage?: string;
  paymentMethod: PaymentMethod;
  createdBy: string;
  tenantId: string;
  createdAt: number;
  updatedAt: number;
}

export interface ExpenseSummary {
  totalAmount: number;
  categoryBreakdown: Record<ExpenseCategory, number>;
  periodStart: number;
  periodEnd: number;
}



