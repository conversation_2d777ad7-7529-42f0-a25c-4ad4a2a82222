import { Symptom } from '@/types/animal';

export const symptoms: Symptom[] = [
  // General symptoms
  { id: 'fever', name: 'Fever', category: 'general' },
  { id: 'lethargy', name: 'Lethargy', category: 'general' },
  { id: 'loss_of_appetite', name: 'Loss of Appetite', category: 'general' },
  { id: 'weight_loss', name: 'Weight Loss', category: 'general' },
  { id: 'dehydration', name: 'Dehydration', category: 'general' },
  
  // Respiratory symptoms
  { id: 'cough', name: 'Cough', category: 'respiratory' },
  { id: 'nasal_discharge', name: 'Nasal Discharge', category: 'respiratory' },
  { id: 'difficulty_breathing', name: 'Difficulty Breathing', category: 'respiratory' },
  { id: 'sneezing', name: 'Sneezing', category: 'respiratory' },
  
  // Digestive symptoms
  { id: 'diarrhea', name: 'Diarrhea', category: 'digestive' },
  { id: 'vomiting', name: 'Vomiting', category: 'digestive' },
  { id: 'bloating', name: 'Bloating', category: 'digestive' },
  { id: 'constipation', name: 'Constipation', category: 'digestive' },
  { id: 'abnormal_feces', name: 'Abnormal Feces', category: 'digestive' },
  
  // Skin symptoms
  { id: 'hair_loss', name: 'Hair Loss', category: 'skin' },
  { id: 'itching', name: 'Itching', category: 'skin' },
  { id: 'rash', name: 'Rash', category: 'skin' },
  { id: 'wounds', name: 'Wounds', category: 'skin' },
  { id: 'swelling', name: 'Swelling', category: 'skin' },
  
  // Neurological symptoms
  { id: 'seizures', name: 'Seizures', category: 'neurological' },
  { id: 'disorientation', name: 'Disorientation', category: 'neurological' },
  { id: 'head_tilt', name: 'Head Tilt', category: 'neurological' },
  { id: 'paralysis', name: 'Paralysis', category: 'neurological' },
  { id: 'abnormal_behavior', name: 'Abnormal Behavior', category: 'neurological' },
  
  // Reproductive symptoms
  { id: 'vaginal_discharge', name: 'Vaginal Discharge', category: 'reproductive' },
  { id: 'abortion', name: 'Abortion', category: 'reproductive' },
  { id: 'infertility', name: 'Infertility', category: 'reproductive' },
  { id: 'difficult_birth', name: 'Difficult Birth', category: 'reproductive' },
  
  // Musculoskeletal symptoms
  { id: 'limping', name: 'Limping', category: 'musculoskeletal' },
  { id: 'lameness', name: 'Lameness', category: 'musculoskeletal' },
  { id: 'joint_swelling', name: 'Joint Swelling', category: 'musculoskeletal' },
  { id: 'stiffness', name: 'Stiffness', category: 'musculoskeletal' },
  
  // Other symptoms
  { id: 'increased_thirst', name: 'Increased Thirst', category: 'other' },
  { id: 'increased_urination', name: 'Increased Urination', category: 'other' },
  { id: 'eye_discharge', name: 'Eye Discharge', category: 'other' },
  { id: 'ear_discharge', name: 'Ear Discharge', category: 'other' },
  { id: 'bleeding', name: 'Bleeding', category: 'other' },
];

export const symptomCategories = [
  { id: 'general', name: 'General' },
  { id: 'respiratory', name: 'Respiratory' },
  { id: 'digestive', name: 'Digestive' },
  { id: 'skin', name: 'Skin' },
  { id: 'neurological', name: 'Neurological' },
  { id: 'reproductive', name: 'Reproductive' },
  { id: 'musculoskeletal', name: 'Musculoskeletal' },
  { id: 'other', name: 'Other' },
];