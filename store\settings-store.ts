import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SettingsState {
  language: string;
  textSize: 'small' | 'medium' | 'large';
  soundEnabled: boolean;
  darkMode: boolean;
  openaiApiKey: string;
  aiVisionEnabled: boolean;
  preferredCurrency: string;

  setLanguage: (language: string) => void;
  setTextSize: (size: 'small' | 'medium' | 'large') => void;
  setSoundEnabled: (enabled: boolean) => void;
  setDarkMode: (enabled: boolean) => void;
  setOpenAIApiKey: (key: string) => void;
  setAIVisionEnabled: (enabled: boolean) => void;
  setPreferredCurrency: (currency: string) => void;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      language: 'en',
      textSize: 'medium',
      soundEnabled: true,
      darkMode: false,
      // Updated API key format to include 'proj-' prefix
      openaiApiKey: '********************************************************************************************************************************************************************',
      // Enable AI Vision by default
      aiVisionEnabled: true,
      
      setLanguage: (language) => set({ language }),
      setTextSize: (textSize) => set({ textSize }),
      setSoundEnabled: (soundEnabled) => set({ soundEnabled }),
      setDarkMode: (darkMode) => set({ darkMode }),
      setOpenAIApiKey: (openaiApiKey) => set({ openaiApiKey }),
      setAIVisionEnabled: (aiVisionEnabled) => set({ aiVisionEnabled }),
    }),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

