import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useAnimalStore } from '@/store/animal-store';
import { useRecordStore } from '@/store/record-store';
import { useTranslation } from '@/hooks/useTranslation';
import { AnimalRecord, RecordType } from '@/types/record';
import { Animal } from '@/types/animal';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import {formatDate} from '@/utils/date-utils';
import {
  ArrowLeft,
  Calendar,
  FileText,
  Edit,
  Trash2,
  <PERSON>yringe,
  Pill,
  Sciss<PERSON>,
  Baby,
  Stethoscope,
  Skull,
  HelpCircle,
  AlertTriangle,
} from 'lucide-react-native';

export default function RecordDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const themedColors = useThemeColors();
  const { getAnimal } = useAnimalStore();
  const { getRecordById, deleteRecord, isLoading, fetchRecords } = useRecordStore();
  const { t, language } = useTranslation();

  const [record, setRecord] = useState<AnimalRecord | undefined>();
  const [animal, setAnimal] = useState<Animal | undefined>();
  const [loading, setLoading] = useState(true);
  const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // First fetch all records to ensure we have the latest data
        await fetchRecords();

        // Then get the specific record
        const recordData = getRecordById(id);
        setRecord(recordData);

        if (recordData) {
          const animalData = getAnimal(recordData.animalId);
          setAnimal(animalData);
        }
      } catch (error) {
        console.error('Error loading record:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, getRecordById, getAnimal, fetchRecords]);

  const handleEdit = () => {
    if (record) {
      router.push({
        pathname: `/records/${id}/edit`,
        params: { animalId: record.animalId }
      });
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Record',
      'Are you sure you want to delete this health record? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            try {
              await deleteRecord(id);
              router.back();
            } catch (error) {
              console.error('Error deleting record:', error);
              Alert.alert('Error', 'Failed to delete record. Please try again.');
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  const getRecordTypeIcon = (type: RecordType) => {
    switch (type) {
      case RecordType.VACCINATION:
        return <Syringe size={24} color={themedColors.primary} />;
      case RecordType.MEDICATION:
        return <Pill size={24} color={themedColors.primary} />;
      case RecordType.SURGERY:
        return <Scissors size={24} color={themedColors.primary} />;
      case RecordType.GENERAL:
        return <Stethoscope size={24} color={themedColors.primary} />;
      case RecordType.BIRTH:
        return <Baby size={24} color={themedColors.primary} />;
      case RecordType.DEATH:
        return <Skull size={24} color={themedColors.primary} />;
      default:
        return <HelpCircle size={24} color={themedColors.primary} />;
    }
  };

  const getRecordTypeColor = (type: RecordType) => {
    switch (type) {
      case RecordType.VACCINATION:
        return themedColors.primary;
      case RecordType.MEDICATION:
        return themedColors.secondary;
      case RecordType.SURGERY:
        return themedColors.error;
      case RecordType.GENERAL:
        return themedColors.success;
      case RecordType.BIRTH:
        return '#60A5FA'; // Light blue
      case RecordType.DEATH:
        return '#6B7280'; // Gray
      default:
        return themedColors.textSecondary;
    }
  };

  // Get placeholder image for animals without an image - focused on faces
  const getPlaceholderImage = (species: string) => {
    switch (species?.toLowerCase()) {
      case 'cow':
        return 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop';
      case 'goat':
        return 'https://images.unsplash.com/photo-1524024973431-2ad916746881?q=80&w=1000&auto=format&fit=crop';
      case 'poultry':
        return 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1000&auto=format&fit=crop';
      case 'fish':
        return 'https://images.unsplash.com/photo-1524704796725-9fc3044a58b2?q=80&w=1000&auto=format&fit=crop';
      default:
        return 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop';
    }
  };

  if (isLoading || loading) {
    return <LoadingIndicator fullScreen message={t('records.loadingDetails')} />;
  }

  if (!record || !animal) {
    return (
      <EmptyState
        title={t('records.notFound')}
        message={t('records.notFoundMessage')}
        actionLabel={t('common.goBack')}
        onAction={() => router.back()}
      />
    );
  }

  const recordTypeColor = getRecordTypeColor(record.type as RecordType);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themedColors.background }]} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('records.healthRecord'),
        headerTitleAlign: language === 'ur' ? 'center' : 'left',
        headerTitleStyle: { fontWeight: 'bold', color: themedColors.text },
        headerStyle: {
          backgroundColor: themedColors.background,
        },
        headerTintColor: themedColors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={themedColors.text} />
          </TouchableOpacity>
        )
      }} />

      <ScrollView style={styles.scrollView}>
        {/* Animal Info Section */}
        <View style={[styles.animalSection, { backgroundColor: themedColors.card, borderBottomColor: themedColors.border }]}>
          <View style={styles.animalImageContainer}>
            {animal.imageUri ? (
              <Image
                source={{ uri: animal.imageUri }}
                style={styles.animalImage}
                resizeMode="cover"
              />
            ) : (
              <Image
                source={{ uri: getPlaceholderImage(animal.species) }}
                style={styles.animalImage}
                resizeMode="cover"
              />
            )}
          </View>
          <View style={styles.animalInfo}>
            <Text style={[styles.animalName, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>{animal.name}</Text>
            <Text style={[styles.animalSpecies, { color: themedColors.textSecondary }, language === 'ur' ? styles.urduText : null]}>
              {animal.species.charAt(0).toUpperCase() + animal.species.slice(1)}
              {animal.breed ? ` • ${animal.breed}` : ''}
            </Text>
          </View>
        </View>

        {/* Record Type Badge */}
        <View style={[
          styles.recordTypeBadge,
          { backgroundColor: recordTypeColor + '20' },
          language === 'ur' && styles.urduRecordTypeBadge
        ]}>
          <View style={[
            styles.recordTypeIconContainer,
            language === 'ur' && styles.urduRecordTypeIconContainer
          ]}>
            {getRecordTypeIcon(record.type as RecordType)}
          </View>
          <Text style={[styles.recordTypeText, { color: recordTypeColor }, language === 'ur' ? styles.urduText : null]}>
            {record.type === RecordType.VACCINATION ? t('records.vaccination') :
             record.type === RecordType.MEDICATION ? t('records.medicationLabel') :
             record.type === RecordType.SURGERY ? t('records.surgery') :
             record.type === RecordType.GENERAL ? t('records.checkup') :
             record.type.charAt(0).toUpperCase() + record.type.slice(1)}
          </Text>
        </View>

        {/* Record Details */}
        <View style={[styles.recordDetailsCard, { backgroundColor: themedColors.card }]}>
          <View style={styles.recordHeader}>
            <Text style={[styles.recordTitle, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>
                 {language === 'ur' ? t(`records.${record.title.toLowerCase().replace(/^.*?:/, "").replaceAll(' ', '')}`): record.title.startsWith(record.type + ':')
                    ? record.title.substring(record.type.length + 1).trim()
                    : record.title}
            </Text>
            <View style={[styles.dateContainer, language === 'ur' && styles.urduDateContainer]}>
              <Calendar size={16} color={themedColors.textSecondary} />
              <Text style={[styles.dateText, { color: themedColors.textSecondary }, language === 'ur' ? styles.urduText : null]}>{formatDate(record.date, locale)}</Text>
            </View>
          </View>

          {record.confirmedDisease && (
            <View style={styles.diseaseSection}>
              <View style={styles.sectionHeader}>
                <AlertTriangle size={18} color={themedColors.error} />
                <Text style={[styles.sectionTitle, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>{t('records.confirmedDisease')}</Text>
              </View>
              <View style={styles.diseaseCard}>
                <Text style={[styles.diseaseName, { color: themedColors.text }]}>{record.confirmedDisease.name}</Text>
                <Text style={[styles.diseaseDescription, { color: themedColors.textSecondary }]}>{record.confirmedDisease.description}</Text>
                <View style={styles.severityContainer}>
                  <Text style={[styles.severityLabel, { color: themedColors.text }]}>Severity:</Text>
                  <View style={[
                    styles.severityBadge,
                    {
                      backgroundColor:
                        record.confirmedDisease.severity === 'high' ? themedColors.error :
                        record.confirmedDisease.severity === 'medium' ? themedColors.warning :
                        themedColors.secondary
                    }
                  ]}>
                    <Text style={styles.severityText}>
                      {record.confirmedDisease.severity.toUpperCase()}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          )}

          {record.description && (
            <View style={styles.descriptionSection}>
              <View style={[styles.sectionHeader, language === 'ur' && styles.urduSectionHeader]}>
                <FileText size={18} color={themedColors.textSecondary} />
                <Text style={[styles.sectionTitle, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>{t('records.details')}</Text>
              </View>
              <Text style={[styles.descriptionText, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>{record.description}</Text>
            </View>
          )}

          {record.symptoms && record.symptoms.length > 0 && (
            <View style={styles.symptomsSection}>
              <View style={styles.sectionHeader}>
                <AlertTriangle size={18} color={themedColors.warning} />
                <Text style={[styles.sectionTitle, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>{t('records.symptoms')}</Text>
              </View>
              <View style={styles.symptomsList}>
                {record.symptoms.map((symptom, index) => (
                  <View key={index} style={styles.symptomItem}>
                    <Text style={[styles.symptomText, { color: themedColors.text }]}>
                      • {typeof symptom === 'string' ? symptom : symptom.name}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {record.medications && record.medications.length > 0 && (
            <View style={styles.medicationsSection}>
              <View style={styles.sectionHeader}>
                <Pill size={18} color={themedColors.secondary} />
                <Text style={[styles.sectionTitle, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>{t('records.medications')}</Text>
              </View>
              {record.medications.map((medication, index) => (
                <View key={index} style={styles.medicationItem}>
                  <Text style={[styles.medicationName, { color: themedColors.text }]}>{medication.name}</Text>
                  <Text style={[styles.medicationDetails, { color: themedColors.textSecondary }]}>
                    Dosage: {medication.dosage} • Frequency: {medication.frequency}
                  </Text>
                  {medication.notes && (
                    <Text style={[styles.medicationNotes, { color: themedColors.textSecondary }]}>{medication.notes}</Text>
                  )}
                </View>
              ))}
            </View>
          )}

          {record.vaccinations && record.vaccinations.length > 0 && (
            <View style={styles.vaccinationsSection}>
              <View style={styles.sectionHeader}>
                <Syringe size={18} color={themedColors.primary} />
                <Text style={[styles.sectionTitle, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>{t('records.vaccinations')}</Text>
              </View>
              {record.vaccinations.map((vaccination, index) => (
                <View key={index} style={styles.vaccinationItem}>
                  <Text style={[styles.vaccinationName, { color: themedColors.text }]}>{vaccination.name}</Text>
                  {vaccination.expiryDate && (
                    <Text style={[styles.vaccinationDetails, { color: themedColors.textSecondary }]}>
                      Expires: {formatDate(vaccination.expiryDate)}
                    </Text>
                  )}
                  {vaccination.notes && (
                    <Text style={[styles.vaccinationNotes, { color: themedColors.textSecondary }]}>{vaccination.notes}</Text>
                  )}
                </View>
              ))}
            </View>
          )}

          {record.treatments && record.treatments.length > 0 && (
            <View style={styles.treatmentsSection}>
              <View style={styles.sectionHeader}>
                <Stethoscope size={18} color={themedColors.success} />
                <Text style={[styles.sectionTitle, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>{t('records.treatments')}</Text>
              </View>
              {record.treatments.map((treatment, index) => (
                <View key={index} style={styles.treatmentItem}>
                  <Text style={[styles.treatmentName, { color: themedColors.text }]}>{treatment.name}</Text>
                  <Text style={[styles.treatmentDetails, { color: themedColors.textSecondary }]}>
                    Date: {formatDate(treatment.date)}
                    {treatment.provider ? ` • Provider: ${treatment.provider}` : ''}
                  </Text>
                  {treatment.notes && (
                    <Text style={[styles.treatmentNotes, { color: themedColors.textSecondary }]}>{treatment.notes}</Text>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actionButtons, { backgroundColor: themedColors.background }]}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton, { backgroundColor: themedColors.primary }]}
          onPress={handleEdit}
        >
          <Edit size={18} color="white" />
          <Text style={[styles.actionButtonText, language === 'ur' ? styles.urduText : null]}>{t('records.editRecord')}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton, { backgroundColor: themedColors.error }]}
          onPress={handleDelete}
        >
          <Trash2 size={18} color="white" />
          <Text style={[styles.actionButtonText, language === 'ur' ? styles.urduText : null]}>{t('common.delete')}</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  animalSection: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  animalImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: 'hidden',
    marginRight: 16,
  },
  animalImage: {
    width: '100%',
    height: '100%',
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalSpecies: {
    fontSize: 14,
  },
  recordTypeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  recordTypeIconContainer: {
    marginRight: 8, // Default for LTR
  },
  urduRecordTypeBadge: {
    alignSelf: 'flex-end',
    flexDirection: 'row-reverse',
  },
  urduRecordTypeIconContainer: {
    marginRight: 0, // Reset LTR margin
    marginLeft: 8,   // Add RTL margin
  },
  recordTypeText: {
    fontSize: 16,
    fontWeight: '600',
  },
  recordDetailsCard: {
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  recordHeader: {
    marginBottom: 16,
  },
  recordTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  urduDateContainer: {
    flexDirection: 'row-reverse',
  },
  dateText: {
    fontSize: 14,
    marginHorizontal: 6, // Use marginHorizontal for bidirectional spacing
  },
  diseaseSection: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  urduSectionHeader: {
    flexDirection: 'row-reverse',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginHorizontal: 8, // Use marginHorizontal for bidirectional spacing
  },
  diseaseCard: {
    backgroundColor: '#FEE2E2',
    borderRadius: 8,
    padding: 12,
  },
  diseaseName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  diseaseDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  severityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  severityLabel: {
    fontSize: 14,
    marginRight: 8,
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  severityText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  descriptionSection: {
    marginBottom: 16,
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  symptomsSection: {
    marginBottom: 16,
  },
  symptomsList: {
    backgroundColor: 'rgba(254, 226, 226, 0.3)',
    borderRadius: 8,
    padding: 12,
  },
  symptomItem: {
    marginBottom: 4,
  },
  symptomText: {
    fontSize: 14,
  },
  medicationsSection: {
    marginBottom: 16,
  },
  medicationItem: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  medicationName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  medicationDetails: {
    fontSize: 14,
    marginBottom: 4,
  },
  medicationNotes: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  vaccinationsSection: {
    marginBottom: 16,
  },
  vaccinationItem: {
    backgroundColor: '#EBF8FF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  vaccinationName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  vaccinationDetails: {
    fontSize: 14,
    marginBottom: 4,
  },
  vaccinationNotes: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  treatmentsSection: {
    marginBottom: 16,
  },
  treatmentItem: {
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  treatmentName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  treatmentDetails: {
    fontSize: 14,
    marginBottom: 4,
  },
  treatmentNotes: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10, // Adjusted padding
    borderRadius: 8,
    marginHorizontal: 4,
  },
  editButton: {
    // backgroundColor will be set dynamically
  },
  deleteButton: {
    // backgroundColor will be set dynamically
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14, // Reduced font size
    fontWeight: 'bold',
    marginLeft: 6, // Adjusted margin
  },
});
