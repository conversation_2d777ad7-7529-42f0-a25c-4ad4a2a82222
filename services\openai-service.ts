import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';

const OPENAI_API_BASE = __DEV__ 
  ? 'http://localhost:8080/https://api.openai.com'
  : 'https://api.openai.com';

// Interface for animal analysis result
interface AnimalAnalysisResult {
  species: string;
  breed: string;
  gender: 'male' | 'female' | 'unknown';
  estimatedAge?: number;
  confidence: number;
}

// Interface for health check analysis result
interface HealthCheckAnalysisResult {
  temperature?: number;
  weight?: number;
  appetite: 'normal' | 'increased' | 'decreased' | 'none';
  hydration: 'normal' | 'dehydrated' | 'overhydrated';
  respiration: 'normal' | 'increased' | 'decreased' | 'labored';
  gait: 'normal' | 'limping' | 'stiff' | 'unable';
  fecal: 'normal' | 'diarrhea' | 'constipated' | 'bloody';
  coat: 'normal' | 'dull' | 'patchy' | 'irritated';
  eyes: 'normal' | 'discharge' | 'cloudy' | 'red';
  ears: 'normal' | 'discharge' | 'red' | 'swollen';
  analysis: string;
  recommendedNextCheckDays: number;
}

/**
 * Analyzes an animal image using OpenAI's Vision API
 * @param imageUri URI of the image to analyze
 * @param apiKey OpenAI API key
 * @returns Analysis result with animal details
 */
export const analyzeAnimalImage = async (
  imageUri: string,
  apiKey: string
): Promise<AnimalAnalysisResult> => {
  try {
    // For web platform, we need a different approach since FileSystem.readAsStringAsync is not available
    let base64Image: string;
    
    if (Platform.OS === 'web') {
      // For web, we'll use fetch and convert to base64
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const result = reader.result as string;
          // Extract the base64 part (remove data:image/jpeg;base64, prefix)
          const base64 = result.split(',')[1];
          sendImageToOpenAI(base64, apiKey)
            .then(resolve)
            .catch(reject);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } else {
      // For native platforms, use FileSystem
      try {
        base64Image = await FileSystem.readAsStringAsync(imageUri, {
          encoding: FileSystem.EncodingType.Base64,
        });
      } catch (error) {
        console.error('Error reading file as base64:', error);
        
        // Alternative approach for native platforms if direct reading fails
        const response = await fetch(imageUri);
        const blob = await response.blob();
        
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const result = reader.result as string;
            // Extract the base64 part (remove data:image/jpeg;base64, prefix)
            const base64 = result.split(',')[1];
            sendImageToOpenAI(base64, apiKey)
              .then(resolve)
              .catch(reject);
          };
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      }
      
      return await sendImageToOpenAI(base64Image, apiKey);
    }
  } catch (error) {
    console.error('Error in analyzeAnimalImage:', error);
    throw error;
  }
};

/**
 * Analyzes an animal health image using OpenAI's Vision API
 * @param imageUri URI of the image to analyze
 * @param apiKey OpenAI API key
 * @returns Analysis result with health check details
 */
export const analyzeAnimalHealthImage = async (
  imageUri: string,
  apiKey: string
): Promise<HealthCheckAnalysisResult> => {
  try {
    // For web platform, we need a different approach since FileSystem.readAsStringAsync is not available
    let base64Image: string;
    
    if (Platform.OS === 'web') {
      // For web, we'll use fetch and convert to base64
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const result = reader.result as string;
          // Extract the base64 part (remove data:image/jpeg;base64, prefix)
          const base64 = result.split(',')[1];
          sendHealthImageToOpenAI(base64, apiKey)
            .then(resolve)
            .catch(reject);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } else {
      // For native platforms, use FileSystem
      try {
        base64Image = await FileSystem.readAsStringAsync(imageUri, {
          encoding: FileSystem.EncodingType.Base64,
        });
      } catch (error) {
        console.error('Error reading file as base64:', error);
        
        // Alternative approach for native platforms if direct reading fails
        const response = await fetch(imageUri);
        const blob = await response.blob();
        
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const result = reader.result as string;
            // Extract the base64 part (remove data:image/jpeg;base64, prefix)
            const base64 = result.split(',')[1];
            sendHealthImageToOpenAI(base64, apiKey)
              .then(resolve)
              .catch(reject);
          };
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      }
      
      return await sendHealthImageToOpenAI(base64Image, apiKey);
    }
  } catch (error) {
    console.error('Error in analyzeAnimalHealthImage:', error);
    throw error;
  }
};

/**
 * Sends the image to OpenAI API for animal analysis
 * @param base64Image Base64-encoded image
 * @param apiKey OpenAI API key
 * @returns Processed analysis result
 */
const sendImageToOpenAI = async (
  base64Image: string,
  apiKey: string
): Promise<AnimalAnalysisResult> => {
  try {
    // Using gpt-4o model which supports vision capabilities
    const model = "gpt-4o";
    
    const response = await fetch(`${OPENAI_API_BASE}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Origin': 'http://localhost:8081',
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: "system",
            content: "You are an expert veterinarian and animal identifier. Analyze the image and provide detailed information about the animal in JSON format."
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Identify this animal. Return ONLY a JSON object with these fields: species (Cow, Goat, Poultry, Fish, or Other), breed (specific breed name), gender (male, female, or unknown), estimatedAge (in years, number only), and confidence (percentage, number only)."
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${base64Image}`
                }
              }
            ]
          }
        ],
        max_tokens: 300
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content;
    
    // Extract JSON from the response
    let jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('Failed to parse JSON response from OpenAI');
    }
    
    const jsonResponse = JSON.parse(jsonMatch[0]);
    
    // Validate and normalize the response
    return {
      species: validateSpecies(jsonResponse.species),
      breed: jsonResponse.breed || 'Unknown',
      gender: validateGender(jsonResponse.gender),
      estimatedAge: typeof jsonResponse.estimatedAge === 'number' ? jsonResponse.estimatedAge : undefined,
      confidence: typeof jsonResponse.confidence === 'number' ? jsonResponse.confidence : 70
    };
  } catch (error) {
    console.error('Error in sendImageToOpenAI:', error);
    throw error;
  }
};

/**
 * Sends the image to OpenAI API for health analysis
 * @param base64Image Base64-encoded image
 * @param apiKey OpenAI API key
 * @returns Processed health analysis result
 */
const sendHealthImageToOpenAI = async (
  base64Image: string,
  apiKey: string
): Promise<HealthCheckAnalysisResult> => {
  try {
    // Using gpt-4o model which supports vision capabilities
    const model = "gpt-4o";
    
    const response = await fetch(`${OPENAI_API_BASE}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Origin': 'http://localhost:8081',
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: "system",
            content: "You are an expert veterinarian. Analyze the image of an animal and provide a detailed health assessment in JSON format."
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Analyze this animal's health condition from the image. Return ONLY a JSON object with these fields: appetite (normal/increased/decreased/none), hydration (normal/dehydrated/overhydrated), respiration (normal/increased/decreased/labored), gait (normal/limping/stiff/unable), fecal (normal/diarrhea/constipated/bloody), coat (normal/dull/patchy/irritated), eyes (normal/discharge/cloudy/red), ears (normal/discharge/red/swollen), analysis (text summary), recommendedNextCheckDays (number of days until next check). If you can estimate temperature or weight, include those too."
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${base64Image}`
                }
              }
            ]
          }
        ],
        max_tokens: 500
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content;
    
    // Extract JSON from the response
    let jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('Failed to parse JSON response from OpenAI');
    }
    
    const jsonResponse = JSON.parse(jsonMatch[0]);
    
    // Validate and normalize the response with defaults for missing fields
    return {
      temperature: typeof jsonResponse.temperature === 'number' ? jsonResponse.temperature : undefined,
      weight: typeof jsonResponse.weight === 'number' ? jsonResponse.weight : undefined,
      appetite: validateHealthField(jsonResponse.appetite, ['normal', 'increased', 'decreased', 'none'], 'normal'),
      hydration: validateHealthField(jsonResponse.hydration, ['normal', 'dehydrated', 'overhydrated'], 'normal'),
      respiration: validateHealthField(jsonResponse.respiration, ['normal', 'increased', 'decreased', 'labored'], 'normal'),
      gait: validateHealthField(jsonResponse.gait, ['normal', 'limping', 'stiff', 'unable'], 'normal'),
      fecal: validateHealthField(jsonResponse.fecal, ['normal', 'diarrhea', 'constipated', 'bloody'], 'normal'),
      coat: validateHealthField(jsonResponse.coat, ['normal', 'dull', 'patchy', 'irritated'], 'normal'),
      eyes: validateHealthField(jsonResponse.eyes, ['normal', 'discharge', 'cloudy', 'red'], 'normal'),
      ears: validateHealthField(jsonResponse.ears, ['normal', 'discharge', 'red', 'swollen'], 'normal'),
      analysis: jsonResponse.analysis || "No detailed analysis provided.",
      recommendedNextCheckDays: typeof jsonResponse.recommendedNextCheckDays === 'number' ? 
        jsonResponse.recommendedNextCheckDays : 7
    };
  } catch (error) {
    console.error('Error in sendHealthImageToOpenAI:', error);
    throw error;
  }
};

/**
 * Validates and normalizes the species value
 */
const validateSpecies = (species: string): string => {
  if (!species) return 'Cow';
  
  const validSpecies = ['Cow', 'Goat', 'Poultry', 'Fish', 'Dog', 'Cat', 'Pig'];
  
  // Normalize to title case
  const normalizedSpecies = species.charAt(0).toUpperCase() + species.slice(1).toLowerCase();
  
  // Check if it's a valid species
  if (validSpecies.includes(normalizedSpecies)) {
    return normalizedSpecies;
  }
  
  // Handle common variations
  if (normalizedSpecies.includes('Chicken') || 
      normalizedSpecies.includes('Hen') || 
      normalizedSpecies.includes('Rooster')) {
    return 'Poultry';
  }
  
  if (normalizedSpecies.includes('Bull') || 
      normalizedSpecies.includes('Cattle') || 
      normalizedSpecies.includes('Bovine')) {
    return 'Cow';
  }
  
  return 'Cow'; // Default to Cow if unknown
};

/**
 * Validates and normalizes the gender value
 */
const validateGender = (gender: string): 'male' | 'female' | 'unknown' => {
  if (!gender) return 'unknown';
  
  const normalizedGender = gender.toLowerCase();
  
  if (normalizedGender.includes('male')) return 'male';
  if (normalizedGender.includes('female')) return 'female';
  
  // Handle specific animal gender terms
  if (normalizedGender.includes('bull') || 
      normalizedGender.includes('rooster') || 
      normalizedGender.includes('cock') || 
      normalizedGender.includes('buck')) {
    return 'male';
  }
  
  if (normalizedGender.includes('cow') || 
      normalizedGender.includes('hen') || 
      normalizedGender.includes('doe')) {
    return 'female';
  }
  
  return 'unknown';
};

/**
 * Validates a health field against allowed values
 */
const validateHealthField = (value: string, allowedValues: string[], defaultValue: string): any => {
  if (!value) return defaultValue;
  
  const normalizedValue = value.toLowerCase();
  
  if (allowedValues.includes(normalizedValue)) {
    return normalizedValue;
  }
  
  return defaultValue;
};

export const analyzeReceiptImage = async (imageUri: string, apiKey: string) => {
  let base64Image;
  
  if (Platform.OS === 'web') {
    // For web, use fetch and convert to base64
    const response = await fetch(imageUri);
    const blob = await response.blob();
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        // Extract the base64 part
        const base64 = result.split(',')[1];
        sendReceiptToOpenAI(base64, apiKey)
          .then(resolve)
          .catch(reject);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } else {
    // For native platforms, use FileSystem
    try {
      base64Image = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      return await sendReceiptToOpenAI(base64Image, apiKey);
    } catch (error) {
      console.error('Error reading file as base64:', error);
      
      // Alternative approach for native platforms if direct reading fails
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const result = reader.result as string;
          // Extract the base64 part
          const base64 = result.split(',')[1];
          sendReceiptToOpenAI(base64, apiKey)
            .then(resolve)
            .catch(reject);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    }
  }
};

const sendReceiptToOpenAI = async (base64Image: string, apiKey: string) => {
  try {
    console.log('Sending receipt to OpenAI...');
    
    const response = await fetch(`${OPENAI_API_BASE}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Origin': 'http://localhost:8081',
      },
      body: JSON.stringify({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "This is a receipt image. Extract the following information in JSON format: \n" +
                      "- amount (number): The total amount on the receipt\n" +
                      "- date (ISO string): The date on the receipt\n" +
                      "- category (one of: ANIMAL_PURCHASE, FEED, MEDICATION, VACCINATION, VETERINARY, EQUIPMENT, UTILITIES, LABOR, MAINTENANCE, OTHER): The expense category\n" +
                      "- items (array): An array of items with these properties:\n" +
                      "  * name (string): Name of the item\n" +
                      "  * quantity (number): Quantity purchased\n" +
                      "  * pricePerUnit (number): Price per unit\n" +
                      "  * totalPrice (number): Total price for this item\n" +
                      "- paymentMethod (one of: CASH, CARD, BANK_TRANSFER, OTHER): How the payment was made\n" +
                      "- shopName (string): Name of the shop or vendor if present\n" +
                      "- shopContact (string): Contact number of the shop if present\n" +
                      "- shopAddress (string): Address of the shop if present\n\n" +
                      "Return ONLY valid JSON with these fields. If a field cannot be determined, omit it from the JSON."
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${base64Image}`
                }
              }
            ]
          }
        ],
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API error:', errorData);
      throw new Error(errorData.error?.message || 'Error analyzing receipt');
    }
    
    const data = await response.json();
    console.log('OpenAI response received');
    
    const content = data.choices[0].message.content;
    
    // Extract JSON from the response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      console.error('Failed to extract JSON from response:', content);
      throw new Error('Failed to extract JSON from response');
    }
    
    return JSON.parse(jsonMatch[0]);
  } catch (error) {
    console.error('Error in sendReceiptToOpenAI:', error);
    throw error;
  }
};

export const generatePregnancyPlans = async (animalData: any, apiKey: string) => {
  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a veterinary expert specializing in livestock pregnancy management. Generate detailed diet and treatment plans for pregnant animals based on their species, health data, and pregnancy status."
          },
          {
            role: "user",
            content: `Generate diet and treatment plans for a pregnant ${animalData.species} with the following details:
Animal Details:
- Age: ${animalData.age || 'Unknown'} years
- Weight: ${animalData.weight || 'Unknown'} kg
- Temperature: ${animalData.temperature || 'Normal'}
- Appetite: ${animalData.healthStatus.appetite}
- Hydration: ${animalData.healthStatus.hydration}
- Has abnormalities: ${animalData.healthStatus.abnormalities ? 'Yes' : 'No'}
- Pregnancy status: ${animalData.pregnancyStatus}

Provide the response ONLY in JSON format. The JSON object should have two main keys: "aiGeneratedDietPlan" and "aiGeneratedTreatmentPlan".

1.  "aiGeneratedDietPlan": An array of objects. Each object should represent a weekly diet recommendation and must include:
    *   "week" (number): The week number.
    *   "stage" (string): One of "early", "mid", "late".
    *   "title" (string): A concise title for the week's diet.
    *   "description" (string): A brief description of the diet focus.
    *   "nutrients" (array of strings): List key nutrients (e.g., ["Calcium", "Protein (increased for growth)", "Vitamin D"]). Do NOT prefix items with "AI-recommended" or similar phrases.
    *   "foods" (array of strings): List recommended foods with quantities if applicable (e.g., ["Good quality hay (ad libitum)", "Concentrate feed (0.5kg/day)", "Fresh water"]). Do NOT prefix items with "AI-recommended" or similar phrases.

2.  "aiGeneratedTreatmentPlan": An array of objects. Each object MUST represent a weekly health recommendation and include ALL of the following fields:
    *   "week" (number): The specific week number of the pregnancy this recommendation applies to. This field is mandatory.
    *   "stage" (string): The stage of pregnancy, must be one of "early", "mid", or "late". This field is mandatory.
    *   "title" (string): A concise title for this week's health focus (e.g., "Week 5: Initial Vet Checkup"). This field is mandatory.
    *   "description" (string): A brief description of the health considerations for this week. This field is mandatory.
    *   "checkups" (array of strings, optional): A list of recommended checkups for this week. Provide plain text, e.g., ["Monitor body temperature", "Observe appetite"]. Do NOT prefix items with "AI-recommended" or similar phrases.
    *   "treatments" (array of strings, optional): A list of recommended treatments, medications, or vaccinations for this week. Provide plain text, e.g., ["Administer prenatal vitamins", "Ensure access to clean water"]. Do NOT prefix items with "AI-recommended" or similar phrases.

Ensure the output is a single, valid JSON object.`
          }
        ],
        temperature: 0.7,
        // Ensure the model is instructed to return JSON
        response_format: { type: "json_object" }
      })
    });

    const data = await response.json();
    
    if (!data.choices || data.choices.length === 0) {
      throw new Error('Invalid response from OpenAI API');
    }
    
    const content = data.choices[0].message.content;
    return JSON.parse(content);
  } catch (error) {
    console.error('Error in generatePregnancyPlans:', error);
    throw error;
  }
};
