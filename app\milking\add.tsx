import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  StyleSheet,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { useAnimalStore } from '@/store/animal-store';
import { useMilkingStore } from '@/store/milking-store';
import { MilkQuality } from '@/types/milking';
import { Calendar, Droplets, Sun, Sunset, Moon, Award, ThumbsUp, Meh, ThumbsDown } from 'lucide-react-native';
import Input from '@/components/Input';
import Button from '@/components/Button';
import DatePickerInput from '@/components/DatePickerInput';
import GenericDropdown from '@/components/GenericDropdown';
import AnimalSearchDropdown from '@/components/AnimalSearchDropdown';
import LoadingIndicator from '@/components/LoadingIndicator';
import EmptyState from '@/components/EmptyState';

export default function AddMilkingScreen() {
  const { t, language } = useTranslation();
  const router = useRouter();
  const themedColors = useThemeColors();
  const { farmId, animalId } = useLocalSearchParams<{ farmId: string; animalId?: string }>();
  
  const { user } = useAuthStore();
  const { getFarm } = useFarmStore();
  const { animals, fetchAnimalsByFarm } = useAnimalStore();
  const { addRecord, isLoading } = useMilkingStore();

  const [selectedAnimal, setSelectedAnimal] = useState<string>('');
  const [date, setDate] = useState(new Date());
  const [session, setSession] = useState<'morning' | 'afternoon' | 'evening'>('morning');
  const [quantity, setQuantity] = useState<string>('');
  const [quality, setQuality] = useState<MilkQuality>(MilkQuality.GOOD);
  const [fat, setFat] = useState<string>('');
  const [protein, setProtein] = useState<string>('');
  const [temperature, setTemperature] = useState<string>('');
  const [notes, setNotes] = useState<string>('');

  const farm = farmId ? getFarm(farmId) : null;
  const styles = getStyles(themedColors, language);

  useEffect(() => {
    if (farmId) {
      fetchAnimalsByFarm(farmId);
    }
  }, [farmId]);

  // Auto-select animal if animalId is provided (coming from animal detail screen)
  useEffect(() => {
    if (animalId && animals.length > 0) {
      const animal = animals.find(a => a.id === animalId);
      if (animal) {
        setSelectedAnimal(animalId);
      }
    }
  }, [animalId, animals]);

  const sessionOptions = [
    {
      id: 'morning',
      label: t('milking.sessions.morning').charAt(0).toUpperCase() + t('milking.sessions.morning').slice(1),
      icon: <Sun size={20} color={themedColors.primary} />
    },
    {
      id: 'afternoon',
      label: t('milking.sessions.afternoon').charAt(0).toUpperCase() + t('milking.sessions.afternoon').slice(1),
      icon: <Sunset size={20} color={themedColors.primary} />
    },
    {
      id: 'evening',
      label: t('milking.sessions.evening').charAt(0).toUpperCase() + t('milking.sessions.evening').slice(1),
      icon: <Moon size={20} color={themedColors.primary} />
    },
  ];

  const qualityOptions = [
    {
      id: MilkQuality.EXCELLENT,
      label: t('milking.qualities.excellent').charAt(0).toUpperCase() + t('milking.qualities.excellent').slice(1),
      icon: <Award size={20} color="#10B981" />
    },
    {
      id: MilkQuality.GOOD,
      label: t('milking.qualities.good').charAt(0).toUpperCase() + t('milking.qualities.good').slice(1),
      icon: <ThumbsUp size={20} color="#3B82F6" />
    },
    {
      id: MilkQuality.FAIR,
      label: t('milking.qualities.fair').charAt(0).toUpperCase() + t('milking.qualities.fair').slice(1),
      icon: <Meh size={20} color="#F59E0B" />
    },
    {
      id: MilkQuality.POOR,
      label: t('milking.qualities.poor').charAt(0).toUpperCase() + t('milking.qualities.poor').slice(1),
      icon: <ThumbsDown size={20} color="#EF4444" />
    },
  ];

  const validateForm = (): boolean => {
    if (!selectedAnimal) {
      Alert.alert(t('common.error'), t('milking.errors.animalRequired'));
      return false;
    }
    if (!quantity || isNaN(parseFloat(quantity)) || parseFloat(quantity) <= 0) {
      Alert.alert(t('common.error'), t('milking.errors.quantityRequired'));
      return false;
    }
    if (!quality) {
      Alert.alert(t('common.error'), t('milking.errors.qualityRequired'));
      return false;
    }
    if (!session) {
      Alert.alert(t('common.error'), t('milking.errors.sessionRequired'));
      return false;
    }

    // Validate optional numeric fields only if they have values
    if (fat && fat.trim() !== '' && (isNaN(parseFloat(fat)) || parseFloat(fat) < 0)) {
      Alert.alert(t('common.error'), t('milking.errors.invalidFat'));
      return false;
    }
    if (protein && protein.trim() !== '' && (isNaN(parseFloat(protein)) || parseFloat(protein) < 0)) {
      Alert.alert(t('common.error'), t('milking.errors.invalidProtein'));
      return false;
    }
    if (temperature && temperature.trim() !== '' && (isNaN(parseFloat(temperature)) || parseFloat(temperature) < 0)) {
      Alert.alert(t('common.error'), t('milking.errors.invalidTemperature'));
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !user || !farm) return;

    try {
      const selectedAnimalData = animals.find(a => a.id === selectedAnimal);
      if (!selectedAnimalData) {
        Alert.alert(t('common.error'), t('milking.errors.animalRequired'));
        return;
      }

      const recordData = {
        farmId: farm.id,
        farmName: farm.name,
        animalId: selectedAnimal,
        animalName: selectedAnimalData.name,
        animalSpecies: selectedAnimalData.species,
        date: date.getTime(),
        session,
        quantity: parseFloat(quantity),
        quality,
        fat: fat && fat.trim() !== '' ? parseFloat(fat) : undefined,
        protein: protein && protein.trim() !== '' ? parseFloat(protein) : undefined,
        temperature: temperature && temperature.trim() !== '' ? parseFloat(temperature) : undefined,
        notes: notes && notes.trim() !== '' ? notes.trim() : undefined,
        createdBy: user.id,
        tenantId: user.id,
      };

      await addRecord(recordData);
      Alert.alert(t('common.success'), t('milking.addSuccess'));
      router.back();
    } catch (error) {
      console.error('Error adding milking record:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
    }
  };

  if (isLoading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  // Show empty state if no animals are available for this farm
  const farmAnimals = animals.filter(a => a.farmId === farmId);
  if (farmAnimals.length === 0) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <Stack.Screen
          options={{
            title: t('milking.addRecord'),
            headerBackTitle: t('common.back'),
          }}
        />
        <View style={styles.emptyStateContainer}>
          <EmptyState
            title={t('animals.noAnimals')}
            message={t('animals.addAnimalsFirst')}
            actionLabel={t('animals.addAnimal')}
            onAction={() => router.push(`/animals/add?farmId=${farmId}`)}
            icon={<Droplets size={48} color={themedColors.primary} />}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('milking.addRecord'),
          headerBackTitle: t('common.back'),
        }}
      />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Animal Selection */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, language === 'ur' && styles.urduText]}>
              {t('milking.animal')} <Text style={styles.required}>*</Text>
            </Text>
            <AnimalSearchDropdown
              animals={animals.filter(a => a.farmId === farmId)}
              value={selectedAnimal}
              onSelect={(animalId) => {
                setSelectedAnimal(animalId);
              }}
              onAnimalSelect={(animalId) => {
                setSelectedAnimal(animalId);
              }}
              placeholder={t('animals.selectAnimal')}
              farmId={farmId}
            />
          </View>

          {/* Date and Session */}
          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <Text style={[styles.label, language === 'ur' && styles.urduText]}>
                {t('milking.date')} <Text style={styles.required}>*</Text>
              </Text>
              <DatePickerInput
                date={date}
                onDateChange={setDate}
              />
            </View>
            <View style={styles.halfWidth}>
              <Text style={[styles.label, language === 'ur' && styles.urduText]}>
                {t('milking.session')} <Text style={styles.required}>*</Text>
              </Text>
              <GenericDropdown
                items={sessionOptions}
                value={session}
                onSelect={(value) => setSession(value as 'morning' | 'afternoon' | 'evening')}
                placeholder={t('milking.sessions.morning')}
                containerStyle={styles.dropdownStyle}
              />
            </View>
          </View>

          {/* Quantity and Quality */}
          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <Input
                label={`${t('milking.quantity')} *`}
                value={quantity}
                onChangeText={setQuantity}
                placeholder="0.0"
                keyboardType="numeric"
              />
            </View>
            <View style={styles.halfWidth}>
              <Text style={[styles.label, language === 'ur' && styles.urduText]}>
                {t('milking.quality')} <Text style={styles.required}>*</Text>
              </Text>
              <GenericDropdown
                items={qualityOptions}
                value={quality}
                onSelect={(value) => setQuality(value as MilkQuality)}
                placeholder={t('milking.qualities.good')}
                containerStyle={styles.dropdownStyle}
              />
            </View>
          </View>

          {/* Optional Fields */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, language === 'ur' && styles.urduText]}>
              {t('common.optional')}
            </Text>
            
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <Input
                  label={t('milking.fat')}
                  value={fat}
                  onChangeText={setFat}
                  placeholder="3.5"
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.halfWidth}>
                <Input
                  label={t('milking.protein')}
                  value={protein}
                  onChangeText={setProtein}
                  placeholder="3.2"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <Input
              label={t('milking.temperature')}
              value={temperature}
              onChangeText={setTemperature}
              placeholder="37.0"
              keyboardType="numeric"
            />

            <Input
              label={t('milking.notes')}
              value={notes}
              onChangeText={setNotes}
              placeholder={t('common.optional')}
              multiline
              numberOfLines={3}
            />
          </View>
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.buttonContainer}>
        <Button
          title={t('common.save')}
          onPress={handleSubmit}
          isLoading={isLoading}
        />
      </View>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: themedColors.background,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: 16,
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: themedColors.text,
      marginBottom: 12,
      textAlign: language === 'ur' ? 'right' : 'left',
    },
    row: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      marginBottom: 16,
    },
    halfWidth: {
      width: '48%',
    },
    label: {
      fontSize: 14,
      fontWeight: '500',
      color: themedColors.text,
      marginBottom: 8,
      textAlign: language === 'ur' ? 'right' : 'left',
    },
    buttonContainer: {
      padding: 16,
      backgroundColor: themedColors.background,
      borderTopWidth: 1,
      borderTopColor: themedColors.border,
    },
    urduText: {
      textAlign: 'right',
    },
    emptyStateContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    dropdownStyle:{
      maxHeight:50
    },
    required: {
      fontSize: 14,
      fontWeight: 'bold',
    },
  });
