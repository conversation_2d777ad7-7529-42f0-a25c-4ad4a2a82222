import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, StyleProp, ViewStyle } from 'react-native';
// No longer need router import
import { Animal } from '@/types/animal';
import { useThemeColors } from '@/hooks/useThemeColors';
import { AlertTriangle, Calendar, CheckCircle, Syringe, Building2 } from 'lucide-react-native';
import { isHealthCheckDue, isVaccinationDue } from '@/utils/healthStatus';
import { useTranslation } from '@/hooks/useTranslation';
import { useRecordStore } from '@/store/record-store'; // Correct path with hyphen
import { RecordType } from '@/types/record';
import { useFarmData } from '@/hooks/useFarmData';
import { formatDate } from '@/utils/date-utils';

interface AnimalCardProps {
  animal: Animal & {
    farmName?: string;
    farmId?: string;
  };
  onPress?: () => void;
  cardStyle?: StyleProp<ViewStyle>;
  showHealthStatus?: boolean;
  hasHealthIssues?: boolean;
}

const AnimalCard: React.FC<AnimalCardProps> = ({
  animal,
  onPress,
  cardStyle,
  showHealthStatus = true,
  hasHealthIssues = false
}) => {
  const themedColors = useThemeColors();

  const { t, language } = useTranslation();
  const { records } = useRecordStore();
  const { farmName, loading } = useFarmData(animal.farmId);
  const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';
  const getLastVaccinationDate = (animalId: string): number | null => {
    const animalVaccinations = records.filter(record => {
      return record.animalId === animalId && record.type === RecordType.VACCINATION;
    });

    if (animalVaccinations.length > 0) {
      const sortedVaccinations = animalVaccinations.sort((a, b) => b.date - a.date);
      return sortedVaccinations[0].date;
    }
    return null;
  };

  const checkDate = animal.lastHealthCheck;
  const healthCheckDue = isHealthCheckDue(animal);
  const vaccinationDue = isVaccinationDue(animal);
  const isIll = hasHealthIssues || animal.hasHealthIssues;
  const needsAttention = healthCheckDue || vaccinationDue;

  const getFarmName = () => {
    if (loading) return t('common.loading');
    return farmName || t('common.unknown');
  };
  const styles = getStyles(themedColors, language);
  // Determine the contrast color for icons and text within status tags
  // const statusTagContrastColor = (themedColors as any).textContrast || '#FFFFFF';
  const statusTagContrastColor =  '#FFFFFF';
  return (
    <TouchableOpacity
      style={[styles.container, cardStyle]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Status tag at the top right */}
      {showHealthStatus && (
        <View style={styles.statusTagTopRight}>
          {isIll ? (
            <View style={styles.unhealthyTag}>
              <AlertTriangle size={12} color={statusTagContrastColor} />
              <Text style={[styles.unhealthyText, language === 'ur' ? styles.urduText : null]}>{t('animals.unhealthy')}</Text>
            </View>
          ) : needsAttention ? (
            <View style={styles.needAttentionTag}>
              <Text style={[styles.needAttentionText, language === 'ur' ? styles.urduText : null]}>{t('animals.needAttention')}</Text>
            </View>
          ) : (
            <View style={styles.healthyTag}>
              <CheckCircle size={12} color={statusTagContrastColor} />
              <Text style={[styles.healthyText, language === 'ur' ? styles.urduText : null]}>{t('animals.healthy')}</Text>
            </View>
          )}
        </View>
      )}
      <View style={styles.cardContent}>
        {/* Left section - Image */}
        <View style={styles.imageSection}>
          {animal.imageUri ? (
            <Image
              source={{ uri: animal.imageUri }}
              style={styles.image}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderImage}>
              <Text style={styles.placeholderText}>{animal.name.charAt(0)}</Text>
            </View>
          )}
        </View>

        {/* Right section - Details */}
        <View style={styles.detailsSection}>
          <View style={styles.details}>
            <View style={styles.nameContainer}>
              <Text style={styles.name}>{animal.name}</Text>
            </View>
            <Text style={[styles.breed, language === 'ur' ? styles.urduText : null]}>{t(`animals.${animal.species.toLowerCase()}`).charAt(0).toUpperCase() + t(`animals.${animal.species.toLowerCase()}`).slice(1)}{animal.breed ? ` • ${t(`animals.${animal.breed}`)}` : ''}</Text>

            <View style={[styles.checkContainer, language === 'ur' ? styles.urduContainer : null]}>
              {language === 'ur' ? (
                <>
                  <View style={styles.iconContainer}>
                    <Calendar size={10} color={themedColors.primary} />
                  </View>
                  <Text style={[styles.checkText, styles.urduText]} numberOfLines={1} ellipsizeMode="tail">{t('animals.lastHealthCheck')}: {checkDate ? formatDate(checkDate, locale) : t('animals.noCheckYet')}</Text>
                </>
              ) : (
                <>
                  <View style={styles.iconContainer}>
                    <Calendar size={10} color={themedColors.primary} />
                  </View>
                  <Text style={styles.checkText} numberOfLines={1} ellipsizeMode="tail">{t('animals.lastHealthCheck')}: {checkDate ? formatDate(checkDate, locale) : t('animals.noCheckYet')}</Text>
                </>
              )}
            </View>

            <View style={[styles.checkContainer, language === 'ur' ? styles.urduContainer : null]}>
              <View style={styles.iconContainer}>
                <Syringe size={10} color={themedColors.primary} />
              </View>
              <Text style={[styles.checkText, language === 'ur' ? styles.urduText : null]} numberOfLines={1} ellipsizeMode="tail">
                {t('animals.lastVaccine')}: {getLastVaccinationDate(animal.id) 
                  ? formatDate(getLastVaccinationDate(animal.id), locale)
                  : t('animals.noVaccineYet')}
              </Text>
            </View>

            <View style={[styles.checkContainer, language === 'ur' ? styles.urduContainer : null]}>
              <View style={styles.iconContainer}>
                <Building2 size={10} color={themedColors.primary} />
              </View>
              <Text style={[styles.checkText, language === 'ur' ? styles.urduText : null]} numberOfLines={1} ellipsizeMode="tail">
                {t('farms.farm')}: {getFarmName()}
              </Text>
            </View>
          </View>

          {/* Status tags removed from bottom and moved to top right */}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const getStyles = (colors: ReturnType<typeof useThemeColors>, language: string) => {
  const warningColor = (colors as any).warning || (colors.isDarkMode ? '#FBBF24' : '#F59E0B'); // Amber 400 / Amber 600
  const textContrastColor =  '#FFFFFF'; // Default to white
  // const textContrastColor = (colors as any).textContrast || '#FFFFFF'; // Default to white
  return StyleSheet.create({

    urduContainer: {
    flexDirection: 'row-reverse',
    justifyContent: 'flex-start',
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
    marginRight: 8,
    marginLeft: 0,
  },
  statusTagTopRight: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 10,
  },
  statusTagsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  needAttentionTag: {
    backgroundColor: warningColor,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  needAttentionText: {
    color: textContrastColor, // Assuming you have a textContrast for warning backgrounds
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  healthyTag: {
    backgroundColor: colors.success,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    alignSelf: 'flex-start',
  },
  healthyText: {
   color: textContrastColor, // Assuming you have a textContrast for warning backgrounds
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  unhealthyTag: {
    backgroundColor: colors.error,
    paddingHorizontal: 4,
    paddingVertical: 4,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
    alignSelf: 'flex-start',
  },
  unhealthyText: {
   color: textContrastColor, // Assuming you have a textContrast for warning backgrounds
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  container: {
    backgroundColor: colors.card,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    position: 'relative',
    marginBottom: 16,
  },
  cardContent: {
    flexDirection: 'row',
    height: 140, // Decreased height for a more compact card
  },
  imageSection: {
    width: '30%',
    position: 'relative',
  },
  detailsSection: {
    width: '70%',    
    padding: 8,
    justifyContent: 'space-between',
  },
  warningText: {
    color: warningColor,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.primaryLight || (colors.isDarkMode ? '#A5B4FC' : '#E0E7FF'), // Fallback for primaryLight
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
  },
  details: {
    flex: 1,
    marginBottom: 8,
  },
  nameContainer: {
    flexDirection: 'row',
    paddingRight: 70, // Add padding to prevent overlap with status tag
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 3,
    color: colors.text,
    textAlign: 'left',
  },
  breed: {
    fontSize: 13,
    color: colors.textSecondary,
    marginBottom: 6,
  },
  checkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 3,
    flexWrap: 'nowrap',
    width: '100%',
  },
  checkText: {
    fontSize: 12,
    color: colors.text,
    marginLeft: 6,
    flexShrink: 1,
  },
  iconContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.primaryLight || (colors.isDarkMode ? 'rgba(165, 180, 252, 0.2)' : 'rgba(224, 231, 255, 0.5)'),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 0,
    marginLeft: 0,
    flexShrink: 0,
  },
  valueText: {
    color: colors.textSecondary,
    fontSize: 12,
  },
});
};


export default AnimalCard;
