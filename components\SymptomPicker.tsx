import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  TextInput,
} from 'react-native';
import { colors } from '@/constants/colors';
import { Symptom, SymptomCategory } from '@/types/animal';
import { symptoms, symptomCategories } from '@/mocks/symptoms';
import { Search, X } from 'lucide-react-native';

interface SymptomPickerProps {
  selectedSymptoms: string[];
  onSelectSymptom: (symptomId: string) => void;
}

const SymptomPicker: React.FC<SymptomPickerProps> = ({ 
  selectedSymptoms, 
  onSelectSymptom 
}) => {
  const [activeCategory, setActiveCategory] = useState<SymptomCategory>('general');
  const [searchQuery, setSearchQuery] = useState('');

  const filteredSymptoms = searchQuery 
    ? symptoms.filter(s => 
        s.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : symptoms.filter(s => s.category === activeCategory);

  const handleCategoryPress = (category: SymptomCategory) => {
    setActiveCategory(category);
    setSearchQuery('');
  };

  const handleClearSearch = () => {
    setSearchQuery('');
  };

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search symptoms"
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={colors.textSecondary}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={handleClearSearch}>
              <X size={18} color={colors.textSecondary} />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      {!searchQuery && (
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesContainer}
          contentContainerStyle={styles.categoriesContent}
        >
          {symptomCategories.map(category => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryButton,
                activeCategory === category.id && styles.activeCategoryButton
              ]}
              onPress={() => handleCategoryPress(category.id as SymptomCategory)}
            >
              <Text style={[
                styles.categoryText,
                activeCategory === category.id && styles.activeCategoryText
              ]}>
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}

      <ScrollView style={styles.symptomsContainer}>
        <View style={styles.symptomsGrid}>
          {filteredSymptoms.map(symptom => (
            <TouchableOpacity
              key={symptom.id}
              style={[
                styles.symptomButton,
                selectedSymptoms.includes(symptom.id) && styles.selectedSymptomButton
              ]}
              onPress={() => onSelectSymptom(symptom.id)}
            >
              <Text style={[
                styles.symptomText,
                selectedSymptoms.includes(symptom.id) && styles.selectedSymptomText
              ]}>
                {symptom.name}
              </Text>
            </TouchableOpacity>
          ))}
          {filteredSymptoms.length === 0 && (
            <Text style={styles.noSymptomsText}>No symptoms found</Text>
          )}
        </View>
      </ScrollView>

      {selectedSymptoms.length > 0 && (
        <View style={styles.selectedContainer}>
          <Text style={styles.selectedTitle}>
            Selected Symptoms ({selectedSymptoms.length})
          </Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.selectedChipsContainer}
          >
            {selectedSymptoms.map(id => {
              const symptom = symptoms.find(s => s.id === id);
              return (
                <TouchableOpacity
                  key={id}
                  style={styles.selectedChip}
                  onPress={() => onSelectSymptom(id)}
                >
                  <Text style={styles.selectedChipText}>{symptom?.name}</Text>
                  <X size={14} color={colors.primary} />
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: colors.text,
  },
  categoriesContainer: {
    maxHeight: 50,
  },
  categoriesContent: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    gap: 8,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  activeCategoryButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryText: {
    fontSize: 14,
    color: colors.text,
  },
  activeCategoryText: {
    color: 'white',
    fontWeight: '500',
  },
  symptomsContainer: {
    flex: 1,
  },
  symptomsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    gap: 8,
  },
  symptomButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedSymptomButton: {
    backgroundColor: colors.primaryLight,
    borderColor: colors.primary,
  },
  symptomText: {
    fontSize: 14,
    color: colors.text,
  },
  selectedSymptomText: {
    color: colors.primary,
    fontWeight: '500',
  },
  noSymptomsText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
    width: '100%',
  },
  selectedContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  selectedTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  selectedChipsContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  selectedChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  selectedChipText: {
    fontSize: 14,
    color: colors.primary,
  },
});

export default SymptomPicker;
