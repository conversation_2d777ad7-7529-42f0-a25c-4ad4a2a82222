/**
 * Utility function to clear all user-specific data from stores
 * This is used during logout to ensure no data persists between users
 */
import AsyncStorage from '@react-native-async-storage/async-storage';

export const clearAllUserStores = async () => {
  try {
    // Clear all user-specific stores
    // We use require() to avoid circular dependencies
    
    // Clear animal store
    const { useAnimalStore } = require('@/store/animal-store');
    useAnimalStore.getState().clearAnimals();
    
    // Clear farm store
    const { useFarmStore } = require('@/store/farm-store');
    useFarmStore.getState().clearFarms();
    
    // Clear record store
    const { useRecordStore } = require('@/store/record-store');
    useRecordStore.getState().clearRecords();
    
    // Clear health check store
    const { useHealthCheckStore } = require('@/store/health-check-store');
    useHealthCheckStore.getState().clearHealthChecks();
    
    // Clear milking store
    const { useMilkingStore } = require('@/store/milking-store');
    useMilkingStore.getState().clearRecords();
    
    // Clear staff store
    const { useStaffStore } = require('@/store/staff-store');
    useStaffStore.getState().clearStaff();
    
    // Clear pregnancy store
    const { usePregnancyStore } = require('@/store/pregnancy-store');
    usePregnancyStore.getState().clearPregnancies();
    
    // Clear task store
    const { useTaskStore } = require('@/store/task-store');
    useTaskStore.getState().clearTasks();
    
    // Clear expense store
    const { useExpenseStore } = require('@/store/expense-store');
    useExpenseStore.getState().clearExpenses();

    // Clear user-specific AsyncStorage items including Zustand persist storage
    await AsyncStorage.multiRemove([
      'ownerEmail',
      'ownerPassword',
      'sessionRestored',
      // Clear all Zustand persist storage keys
      'record-storage',
      'health-check-store',
      'animal-storage',
      'farm-storage',
      'milking-store',
      'staff-storage',
      'pregnancy-storage',
      'task-storage',
      'expense-storage'
    ]);

    console.log('All user stores and AsyncStorage items cleared successfully');
  } catch (error) {
    console.error('Error clearing user stores:', error);
  }
};
