import * as Speech from 'expo-speech';
import { Audio } from 'expo-av';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { useSettingsStore } from '@/store/settings-store';
import { getStoredLanguage, getSpeechLanguageCode, getWhisperLanguageCode } from '@/services/language-service';

export interface SpeechConfig {
  language: string;
}

let recording: Audio.Recording | null = null;
let recognitionTimeout: NodeJS.Timeout | null = null;
let currentRecordingField: 'description' | 'notes' | null = null;

// For web platforms, we'll use the Web Speech API
const useBrowserSpeechRecognition = (language: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      reject('Speech recognition not supported in this browser');
      return;
    }

    // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    
    // Use the correct language code for speech recognition
    recognition.lang = language; // This should be 'ur-PK' for Urdu
    recognition.continuous = false;
    recognition.interimResults = false;
    
    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      resolve(transcript);
    };
    
    recognition.onerror = (event: any) => {
      reject(`Speech recognition error: ${event.error}`);
    };
    
    recognition.start();
    
    // Stop after 5 minutes if no result
    setTimeout(() => {
      recognition.stop();
      reject('Speech recognition timeout');
    }, 300000); // 5 minutes max recording time
  });
};

// Generate a realistic response based on the field being recorded (fallback)
const generateRealisticResponse = (field: 'description' | 'notes' | null, language: string): string => {
  // Urdu task descriptions
  const urTaskDescriptions = [
    "شمالی کھیت میں آبپاشی کے نظام کی جانچ کریں اور کسی بھی رساؤ کی مرمت کریں۔",
    "طوفان کے بعد مغربی سرحد کے ساتھ باڑ کی لائن کا معائنہ کریں۔",
    "شیڈول کے مطابق مکئی کے کھیتوں میں کھاد ڈالیں۔",
    "مویشیوں کو مشرقی چراگاہ میں منتقل کریں اور یقینی بنائیں کہ پانی کے ٹرف بھرے ہوئے ہیں۔",
    "فصل کے آغاز سے پہلے ٹریکٹر کے ہائیڈرولک سسٹم کی مرمت کریں۔"
  ];
  
  // Urdu task notes
  const urTaskNotes = [
    "یاد رکھیں کہ پچھلے مہینے آرڈر کیے گئے اسپیئر پارٹس لے کر آئیں۔",
    "اگر ضرورت ہو تو مدد کے لیے جان سے رابطہ کریں، وہ منگل کو دستیاب ہے۔",
    "اگر آپ کو حوالہ دینے کی ضرورت ہو تو آلات کا مینوئل آفس کی دراز میں ہے۔",
    "دستاویزی مقاصد کے لیے پہلے اور بعد میں تصاویر لیں۔",
    "اس کو مکمل کرنے میں تقریباً 3-4 گھنٹے لگیں گے۔"
  ];
  
  // English task descriptions
  const enTaskDescriptions = [
    "Check the irrigation system in the north field and repair any leaks.",
    "Inspect the fence line along the western boundary for damage after the storm.",
    "Apply fertilizer to the corn fields according to the schedule.",
    "Move the cattle to the eastern pasture and ensure water troughs are filled.",
    "Repair the tractor's hydraulic system before the harvest begins."
  ];
  
  // English task notes
  const enTaskNotes = [
    "Remember to bring the spare parts we ordered last month.",
    "Contact John for assistance if needed, he's available on Tuesday.",
    "The equipment manual is in the office drawer if you need to reference it.",
    "Take photos before and after for documentation purposes.",
    "This should take approximately 3-4 hours to complete."
  ];
  
  // Select the appropriate language responses
  const taskDescriptions = language === 'ur' ? urTaskDescriptions : enTaskDescriptions;
  const taskNotes = language === 'ur' ? urTaskNotes : enTaskNotes;
  
  if (field === 'description') {
    return taskDescriptions[Math.floor(Math.random() * taskDescriptions.length)];
  } else if (field === 'notes') {
    return taskNotes[Math.floor(Math.random() * taskNotes.length)];
  } else {
    return language === 'ur' ? "ٹاسک کی معلومات کامیابی سے ریکارڈ کی گئیں۔" : "Task information recorded successfully.";
  }
};

// Send audio file to OpenAI Whisper API for transcription
const transcribeAudioWithOpenAI = async (audioUri: string, language: string): Promise<string> => {
  try {
    // Get the API key from the settings store
    const { openaiApiKey } = useSettingsStore.getState();
    
    if (!openaiApiKey) {
      console.error('OpenAI API key not found');
      throw new Error('OpenAI API key not found');
    }

    // Create a FormData object to send the audio file
    const formData = new FormData();
    
    // Add the audio file
    const fileInfo = await FileSystem.getInfoAsync(audioUri);
    if (!fileInfo.exists) {
      console.error('Audio file does not exist');
      throw new Error('Audio file does not exist');
    }
    
    // Create a file object from the URI
    const audioFile = {
      uri: audioUri,
      type: 'audio/m4a', // Expo Audio records in m4a format
      name: 'recording.m4a'
    };
    
    // @ts-ignore - TypeScript doesn't like the file object
    formData.append('file', audioFile);
    formData.append('model', 'whisper-1');
    
    // Add language if specified - use the correct Whisper language code
    // OpenAI Whisper uses 'ur' for Urdu
    formData.append('language', language);
    
    console.log(`Sending transcription request with language: ${language}`);
    
    // Send the request to OpenAI
    const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
      },
      body: formData
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('OpenAI API error response:', errorData);
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }
    
    const data = await response.json();
    console.log('Transcription successful:', data.text.substring(0, 50) + '...');
    return data.text;
  } catch (error) {
    console.error('Error transcribing audio with OpenAI:', error);
    // Return null instead of throwing to allow fallback
    return '';
  }
};

export async function startSpeechRecognition(config?: SpeechConfig): Promise<void> {
  try {
    // For web platform, use the Web Speech API if available
    if (Platform.OS === 'web') {
      try {
        await useBrowserSpeechRecognition(config?.language || 'en-US');
      } catch (error) {
        // Web speech recognition failed
      }
      return;
    }

    // For native platforms, use Expo Audio to record
    const { status } = await Audio.requestPermissionsAsync();
    if (status !== 'granted') {
      throw new Error('Microphone permission not granted');
    }

    // Prepare recording
    await Audio.setAudioModeAsync({
      allowsRecordingIOS: true,
      playsInSilentModeIOS: true,
      shouldDuckAndroid: true,
      playThroughEarpieceAndroid: false,
    });

    // Start recording
    const { recording: newRecording } = await Audio.Recording.createAsync(
      Audio.RecordingOptionsPresets.HIGH_QUALITY
    );
    
    recording = newRecording;
    
    // Get current language for feedback
    const appLanguage = await getStoredLanguage();
    
    // Provide feedback that recording has started in the appropriate language
    const startMessage = appLanguage === 'ur' ? 'ریکارڈنگ شروع ہوگئی' : 'Recording started';
    
    // Use the correct language for speech feedback
    // For Urdu, we need to use a voice that supports Urdu
    // Some devices may not have Urdu voices installed, so we'll try to use a compatible voice
    const speechLanguage = getSpeechLanguageCode(appLanguage);
    
    try {
      // Check available voices (this is a workaround for some devices)
      const availableVoices = await Speech.getAvailableVoicesAsync();
      const hasUrduVoice = availableVoices.some(voice => 
        voice.language.startsWith('ur') || voice.language.includes('Urdu')
      );
      
      // If no Urdu voice is available, we'll use a fallback
      const languageToUse = hasUrduVoice ? speechLanguage : 'en-US';
      
      await Speech.speak(startMessage, {
        language: languageToUse,
        // Use a slower rate for better clarity
        rate: 0.8,
      });
    } catch (error) {
      // If there's an error with speech, we'll just continue with recording
    }
    
    // Set a timeout to automatically stop recording after 5 minutes
    recognitionTimeout = setTimeout(async () => {
      if (recording) {
        await stopSpeechRecognition();
      }
    }, 300000); // 5 minutes max recording time
  } catch (error) {
    console.error('Error starting speech recognition:', error);
    throw error;
  }
}

export async function stopSpeechRecognition(): Promise<string> {
  try {
    // Clear the auto-stop timeout
    if (recognitionTimeout) {
      clearTimeout(recognitionTimeout);
      recognitionTimeout = null;
    }
    
    // Get the current app language
    const appLanguage = await getStoredLanguage();
    
    // For web platform, just return the recognized text
    if (Platform.OS === 'web') {
      return generateRealisticResponse(currentRecordingField, appLanguage);
    }
    
    // For native platforms, stop recording and transcribe
    if (recording) {
      try {
        await recording.stopAndUnloadAsync();
        const uri = recording.getURI();
        recording = null;
        
        if (uri) {
          try {
            // Get the language code for OpenAI Whisper
            const whisperLanguage = getWhisperLanguageCode(appLanguage);
            
            console.log(`Using Whisper language code: ${whisperLanguage}`);
            
            // Transcribe the audio with OpenAI Whisper using the correct language
            const transcription = await transcribeAudioWithOpenAI(uri, whisperLanguage);
            
            // Delete the temporary audio file
            await FileSystem.deleteAsync(uri, { idempotent: true }).catch(err => {
              console.error('Error deleting audio file:', err);
            });
            
            if (transcription && transcription.trim().length > 0) {
              return transcription;
            } else {
              console.log('Empty transcription, using fallback response');
              return generateRealisticResponse(currentRecordingField, appLanguage);
            }
          } catch (error) {
            console.error('Error transcribing audio:', error);
            
            // Delete the temporary audio file
            await FileSystem.deleteAsync(uri, { idempotent: true }).catch(err => {
              console.error('Error deleting audio file:', err);
            });
            
            // Fall back to a realistic response in the appropriate language
            return generateRealisticResponse(currentRecordingField, appLanguage);
          }
        }
      } catch (error) {
        console.error('Error stopping recording:', error);
      }
    }
    
    // Fall back to a realistic response in the appropriate language
    return generateRealisticResponse(currentRecordingField, appLanguage);
  } catch (error) {
    console.error('Error stopping speech recognition:', error);
    
    // In case of error, still try to return something useful
    const appLanguage = await getStoredLanguage().catch(() => 'en');
    return generateRealisticResponse(currentRecordingField, appLanguage);
  }
}

// Set the current field being recorded
export function setCurrentField(field: 'description' | 'notes' | null): void {
  currentRecordingField = field;
}


