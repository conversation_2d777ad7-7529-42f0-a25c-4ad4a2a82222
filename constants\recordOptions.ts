import { RecordType } from '@/types/record';

// Define the structure for record options
export interface RecordOption {
  id: string;
  label: string;
  description: string;
  imageUri?:string
}

// Define the structure for animal-specific record options
export interface AnimalRecordOptions {
  [key: string]: {
    [key in RecordType]?: RecordOption[];
  };
}

// Define the record options for each animal type and record type
export const animalRecordOptions: AnimalRecordOptions = {
  // English and Urdu translations are handled in the i18n files
  // The IDs here are used as keys for translation lookups
  'Cow': {
    [RecordType.MEDICATION]: [
      { 
        id: 'cow-med-antibiotics', 
        label: 'Antibiotics', 
        description: 'Used to treat bacterial infections such as mastitis, pneumonia.',
        imageUri: require('../assets/images/Cattle-stand-tallcowMed.jpg')
      },
      { 
        id: 'cow-med-anthelmintics', 
        label: 'Anthelmintics (Dewormers)', 
        description: 'For internal parasites like worms.',
        imageUri: require('../assets/images/abdc.png')
      },
      { 
        id: 'cow-med-pain-relievers', 
        label: 'Pain Relievers', 
        description: 'NSAIDs for inflammation or injury-related pain.',
        imageUri: require('../assets/images/melovemCowmed.png')
      },
      { 
        id: 'cow-med-digestive', 
        label: 'Digestive Boosters', 
        description: 'Improve rumen function and appetite.',
        imageUri: require('../assets/images/Aftovac Bottle-0.jpg')
      },
    ],
    [RecordType.VACCINATION]: [
      { 
        id: 'cow-vac-fmd', 
        label: 'FMD Vaccine', 
        description: 'Protects against Foot and Mouth Disease (every 6 months).',
        imageUri: require('../assets/images/Aftovac Bottle-0 (1).jpg')
      },
      { 
        id: 'cow-vac-hs', 
        label: 'HS Vaccine', 
        description: 'Prevents Hemorrhagic Septicemia (yearly).',
        imageUri: require('../assets/images/Intervac-HS-2.jpg')
      },
      { 
        id: 'cow-vac-bq', 
        label: 'BQ Vaccine', 
        description: 'Prevents Black Quarter (yearly).',
        imageUri: require('../assets/images/blackQuater.jpg')
      },
      { 
        id: 'cow-vac-brucellosis', 
        label: 'Brucellosis Vaccine', 
        description: 'For female calves to prevent infertility.',
        imageUri: require('../assets/images/RB-51-CZV.jpg')
      },
    ],
    [RecordType.SURGERY]: [
      { 
        id: 'cow-sur-csection', 
        label: 'C-section', 
        description: 'In case of difficult calving.'
      },
      { 
        id: 'cow-sur-dehorning', 
        label: 'Horn Removal (Dehorning)', 
        description: 'Common in young calves for safety.'
      },
      { 
        id: 'cow-sur-abscess', 
        label: 'Abscess Drainage', 
        description: 'Minor surgery for pus-filled swellings.'
      },
    ],
    [RecordType.BIRTH]: [
      { 
        id: 'cow-birth-normal', 
        label: 'Normal Calving', 
        description: 'Regular, natural birth record.'
      },
      { 
        id: 'cow-birth-assisted', 
        label: 'Assisted Calving', 
        description: 'Requires manual or vet-assisted delivery.'
      },
      { 
        id: 'cow-birth-stillbirth', 
        label: 'Stillbirth', 
        description: 'Record of unsuccessful birth.'
      },
    ],
    [RecordType.GENERAL]: [
      { 
        id: 'cow-check-weekly', 
        label: 'Weekly Health Check', 
        description: 'General inspection: eyes, coat, temperature.'
      },
      { 
        id: 'cow-check-hoof', 
        label: 'Hoof Inspection', 
        description: 'Check for infections or overgrowth.'
      },
      { 
        id: 'cow-check-feeding', 
        label: 'Feeding Behavior', 
        description: 'Observe appetite and water intake.'
      },
    ],
  },
  'Goat': {
    [RecordType.MEDICATION]: [ // images issue
      { 
        id: 'goat-med-coccidiostats', 
        label: 'Coccidiostats', 
        description: 'For prevention/treatment of coccidiosis.',
        imageUri: require('../assets/images/CoccidiostatsGoat.jpg')
      },
      { 
        id: 'goat-med-antibiotics', 
        label: 'Antibiotics', 
        description: 'For pneumonia, infections.',
        imageUri: require('../assets/images/Antibioticsgoat.jpg')
      },
      { 
        id: 'goat-med-dewormers', 
        label: 'Dewormers', 
        description: 'Every 3–6 months depending on region.',
        imageUri: require('../assets/images/Dewormersgoat.jpg')
      },
    ],
    [RecordType.VACCINATION]: [
      { 
        id: 'goat-vac-ppr', 
        label: 'PPR Vaccine', 
        description: 'Protects against Peste des Petits Ruminants.',
        imageUri:'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcStZ-WhDwt03d6GilETz-gPGLXK9O7k-1Znjw&s'
      },
      { 
        id: 'goat-vac-enterotoxemia', 
        label: 'Enterotoxemia Vaccine', 
        description: 'Prevents overeating disease.',
        imageUri:'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTm_Eg_nDDNOhKDD7AFLE7X494hZ8S_VTTvdw&s'
      },
      { 
        id: 'goat-vac-fmd-pox', 
        label: 'FMD / Goat Pox Vaccines', 
        description: 'Protect from outbreaks.',
        imageUri:'https://www.merck-animal-health-usa.com/wp-content/uploads/sites/54/2022/12/PP-VAC_product-vial.png'
      },
    ],
    [RecordType.SURGERY]: [
      { 
        id: 'goat-sur-castration', 
        label: 'Castration', 
        description: 'Common for herd management.'
      },
      { 
        id: 'goat-sur-abscess', 
        label: 'Abscess Treatment', 
        description: 'Minor surgeries to remove infected cysts.'
      },
    ],
    [RecordType.BIRTH]: [
      { 
        id: 'goat-birth-normal', 
        label: 'Normal Calving', 
        description: 'Regular, natural birth record.'
      },
      { 
        id: 'goat-birth-assisted', 
        label: 'Assisted Calving', 
        description: 'Requires manual or vet-assisted delivery.'
      },
      { 
        id: 'goat-birth-stillbirth', 
        label: 'Stillbirth', 
        description: 'Record of unsuccessful birth.'
      },
    ],
    [RecordType.GENERAL]: [
      { 
        id: 'goat-check-hoof', 
        label: 'Hoof Trim Check', 
        description: 'Prevent overgrowth & foot rot.'
      },
      { 
        id: 'goat-check-parasite', 
        label: 'Parasite Check', 
        description: 'Visual + fecal exam for worms.'
      },
    ],
  },
  'Fish': {
    [RecordType.MEDICATION]: [
      { 
        id: 'fish-med-antibacterials', 
        label: 'Antibacterials', 
        description: 'For waterborne bacterial infections.',
        imageUri: require('../assets/images/AntibacterialsFish.jpg')
      },
      { 
        id: 'fish-med-antifungals', 
        label: 'Antifungals', 
        description: 'Used in fungal outbreaks (white patches).',
        imageUri: require('../assets/images/Antifungalsfish.jpg')
      },
      { 
        id: 'fish-med-water', 
        label: 'Water Conditioners', 
        description: 'Improve tank/pond health.',
        imageUri: require('../assets/images/WaterConditionersFish.jpg')
      },
    ],
    [RecordType.VACCINATION]: [
      { 
        id: 'fish-vac-bacterial', 
        label: 'Bacterial Disease Vaccines', 
        description: 'For Aeromonas, Vibrio (mostly in commercial fish).'
      },
      { 
        id: 'fish-vac-ipn', 
        label: 'IPN Vaccine', 
        description: 'Infectious Pancreatic Necrosis vaccine.'
      },
    ],
    [RecordType.SURGERY]: [
      { 
        id: 'fish-sur-rare', 
        label: 'Rare Procedures', 
        description: 'Usually not practiced in fish farming.'
      },
    ],
    [RecordType.BIRTH]: [
      { 
        id: 'fish-birth-spawning', 
        label: 'Spawning Record', 
        description: 'Note when fish lay eggs.'
      },
      { 
        id: 'fish-birth-hatch', 
        label: 'Hatch Rate', 
        description: 'Log how many fry survive.'
      },
    ],
    [RecordType.GENERAL]: [
      { 
        id: 'fish-check-water', 
        label: 'Water Quality Check', 
        description: 'pH, oxygen, ammonia levels.'
      },
      { 
        id: 'fish-check-behavior', 
        label: 'Behavior Check', 
        description: 'Erratic swimming, loss of appetite.'
      },
    ],
  },
  'Poultry': {
    [RecordType.MEDICATION]: [
      { 
        id: 'poultry-med-coccidiostats', 
        label: 'Coccidiostats', 
        description: 'Prevent deadly coccidiosis.',
        imageUri: require('../assets/images/CoccidiostatsPoultry.jpg')
      },
      { 
        id: 'poultry-med-antibiotics', 
        label: 'Antibiotics', 
        description: 'For respiratory or digestive infections.',
        imageUri: require('../assets/images/AntibioticsPoultry.jpg')
      },
      { 
        id: 'poultry-med-vitamins', 
        label: 'Vitamin Supplements', 
        description: 'Boost immunity, especially in broilers.',
        imageUri: require('../assets/images/VitaminSupplements.jpg')
      },
    ],
    [RecordType.VACCINATION]: [
      { 
        id: 'poultry-vac-newcastle', 
        label: 'Newcastle Disease', 
        description: 'One of the most critical.'
      },
      { 
        id: 'poultry-vac-gumboro', 
        label: 'Gumboro (IBD) Vaccine', 
        description: 'For chicks at 2–3 weeks.'
      },
      { 
        id: 'poultry-vac-mareks', 
        label: 'Marek\'s Disease', 
        description: 'Given to chicks at hatch.'
      },
    ],
    [RecordType.SURGERY]: [
      { 
        id: 'poultry-sur-uncommon', 
        label: 'Not commonly performed', 
        description: 'Surgery is rare in poultry.'
      },
    ],
    [RecordType.BIRTH]: [
      { 
        id: 'poultry-birth-egg', 
        label: 'Egg Laying Records', 
        description: 'Track egg production per hen/batch.'
      },
      { 
        id: 'poultry-birth-hatch', 
        label: 'Hatch Report', 
        description: 'No. of chicks from incubator or natural hatching.'
      },
    ],
    [RecordType.GENERAL]: [
      { 
        id: 'poultry-check-feather', 
        label: 'Feather Health Check', 
        description: 'Loss of feathers = stress or mites.'
      },
      { 
        id: 'poultry-check-leg', 
        label: 'Leg Strength Check', 
        description: 'Especially in broilers.'
      },
    ],
  },
};

// Get record options for a specific animal type and record type
export const getRecordOptions = (
  animalType: string | null, 
  recordType: RecordType | string
): RecordOption[] => {
  if (!animalType || !recordType) {
    return [];
  }
  
  return animalRecordOptions[animalType]?.[recordType as RecordType] || [];
};


