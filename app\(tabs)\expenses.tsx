import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, FlatList, TouchableOpacity, RefreshControl, StyleSheet, Modal, Alert } from 'react-native';
import { useRouter, useFocusEffect } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '@/store/auth-store';
import { useExpenseStore } from '@/store/expense-store';
import { Expense, ExpenseCategory } from '@/types/expense';
import {
  DollarSign, Plus, Calendar, ChevronDown, X, Layers,
  Cat, Wheat, Pill, Syringe, Stethoscope, Wrench,
  Lightbulb, Users, Hammer, HelpCircle, ArrowUpRight, ArrowDownRight
} from 'lucide-react-native';
import { colors } from '@/constants/colors';
import ExpenseCard from '@/components/ExpenseCard';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { useTranslation } from '@/hooks/useTranslation';
import GenericDropdown, { DropdownItem } from '@/components/GenericDropdown';
import { useFarmStore } from '@/store/farm-store';
import { format, startOfMonth, endOfMonth, subMonths } from 'date-fns';
import { formatDateToYearsandMonths } from '@/utils/date-utils';
import DatePickerInput from '@/components/DatePickerInput';
import { Building2 } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';
import { getCurrencySymbol, getExpenseTotals } from '@/utils/currency-utils';
import { useSettingsStore } from '@/store/settings-store';

export default function ExpensesScreen() {
  const router = useRouter();
  const { user } = useAuthStore();
  const { expenses, isLoading, error, fetchExpenses, fetchExpensesForUser } = useExpenseStore();
  const { farms, fetchFarms } = useFarmStore();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<Date>(new Date());
  const { t, language } = useTranslation();
  const { preferredCurrency } = useSettingsStore();
  const [refreshing, setRefreshing] = useState(false);
  const [filteredExpenses, setFilteredExpenses] = useState<Expense[]>([]);
  const [showMonthPicker, setShowMonthPicker] = useState(false);
  const [tempDate, setTempDate] = useState(new Date());
  const [previousMonthData, setPreviousMonthData] = useState({
    totalSpent: 0,
    totalEntries: 0
  });


  // Use the theme hook for colors
  const {
    background: screenBackgroundColor,
    card: sectionCardColor,
    text: textColor,
    textSecondary: secondaryTextColor,
    primary: primaryColor,
    border: borderColor,
    success: successColor,
    error: errorColor,
    isDarkMode,
  } = useThemeColors();

  const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';

  // Calculate expense totals - prefer PKR but fallback to highest currency if no PKR
  const expenseTotals = getExpenseTotals(filteredExpenses, 'PKR');
  const totalSpent = expenseTotals.primaryTotal;
  const displayCurrency = expenseTotals.primaryCurrency; // Use the actual primary currency found

  // Calculate percentage changes
  const spentPercentChange = previousMonthData.totalSpent > 0
    ? Math.round(((totalSpent - previousMonthData.totalSpent) / previousMonthData.totalSpent) * 100)
    : null; // null means no previous data to compare

  const entriesPercentChange = previousMonthData.totalEntries > 0
    ? Math.round(((filteredExpenses.length - previousMonthData.totalEntries) / previousMonthData.totalEntries) * 100)
    : null; // null means no previous data to compare

  // Category options with colorful, distinct icons
  const categoryOptions: DropdownItem[] = [
    {
      id: 'all',
      label: t('expenses.allCategories') || 'All Categories',
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(52, 152, 219, 0.15)' }]}>
        <Layers size={16} color="#3498DB" />
      </View>
    },
    {
      id: ExpenseCategory.ANIMAL_PURCHASE,
      label: t('expenses.category.animalPurchase'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(142, 68, 173, 0.15)' }]}>
        <Cat size={16} color="#8E44AD" />
      </View>
    },
    {
      id: ExpenseCategory.FEED,
      label: t('expenses.category.feed'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(39, 174, 96, 0.15)' }]}>
        <Wheat size={16} color="#27AE60" />
      </View>
    },
    {
      id: ExpenseCategory.MEDICATION,
      label: t('expenses.category.medication'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(231, 76, 60, 0.15)' }]}>
        <Pill size={16} color="#E74C3C" />
      </View>
    },
    {
      id: ExpenseCategory.VACCINATION,
      label: t('expenses.category.vaccination'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(52, 152, 219, 0.15)' }]}>
        <Syringe size={16} color="#3498DB" />
      </View>
    },
    {
      id: ExpenseCategory.VETERINARY,
      label: t('expenses.category.veterinary'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(46, 204, 113, 0.15)' }]}>
        <Stethoscope size={16} color="#2ECC71" />
      </View>
    },
    {
      id: ExpenseCategory.EQUIPMENT,
      label: t('expenses.category.equipment'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(149, 165, 166, 0.15)' }]}>
        <Wrench size={16} color="#95A5A6" />
      </View>
    },
    {
      id: ExpenseCategory.UTILITIES,
      label: t('expenses.category.utilities'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(243, 156, 18, 0.15)' }]}>
        <Lightbulb size={16} color="#F39C12" />
      </View>
    },
    {
      id: ExpenseCategory.LABOR,
      label: t('expenses.category.labor'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(26, 188, 156, 0.15)' }]}>
        <Users size={16} color="#1ABC9C" />
      </View>
    },
    {
      id: ExpenseCategory.MAINTENANCE,
      label: t('expenses.category.maintenance'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(52, 73, 94, 0.15)' }]}>
        <Hammer size={16} color="#34495E" />
      </View>
    },
    {
      id: ExpenseCategory.OTHER,
      label: t('expenses.category.other'),
      icon: <View style={[styles.colorfulIconBg, { backgroundColor: 'rgba(127, 140, 141, 0.15)' }]}>
        <HelpCircle size={16} color="#7F8C8D" />
      </View>
    },
  ];

  // Fetch farms when component mounts
  useEffect(() => {
    if (user) {
      fetchFarms(user.id);
    }
  }, [user]);



  // Fetch expenses when component mounts
  useFocusEffect(
    useCallback(() => {
      const loadExpenses = async () => {
        if (user) {
          setRefreshing(true);
          try {
            await fetchExpensesForUser(user.id);
          } catch (error) {
            console.error('Error loading expenses:', error);
          } finally {
            setRefreshing(false);
          }
        }
      };

      loadExpenses();
    }, [user, fetchExpensesForUser])
  );

  // Get previous month data
  useEffect(() => {
    if (expenses.length > 0) {
      // First get current month expenses to determine primary currency
      const currentStart = startOfMonth(selectedMonth).getTime();
      const currentEnd = endOfMonth(selectedMonth).getTime();

      const currentFiltered = expenses.filter(expense => {
        const expenseDate = expense.date;
        return expenseDate >= currentStart && expenseDate <= currentEnd;
      });

      // Get current month's primary currency
      const currentExpenseTotals = getExpenseTotals(currentFiltered, 'PKR');
      const currentPrimaryCurrency = currentExpenseTotals.primaryCurrency;

      // Now get previous month data
      const prevMonth = subMonths(selectedMonth, 1);
      const prevStart = startOfMonth(prevMonth).getTime();
      const prevEnd = endOfMonth(prevMonth).getTime();

      const prevFiltered = expenses.filter(expense => {
        const expenseDate = expense.date;
        return expenseDate >= prevStart && expenseDate <= prevEnd;
      });

      // Calculate previous month using the same currency as current month
      const prevExpenseTotals = getExpenseTotals(prevFiltered, currentPrimaryCurrency);
      const prevTotalSpent = prevExpenseTotals.primaryTotal;

      setPreviousMonthData({
        totalSpent: prevTotalSpent,
        totalEntries: prevFiltered.length
      });
    }
  }, [expenses, selectedMonth]);

  // Filter expenses by selected month and category
  useEffect(() => {
    if (expenses.length > 0) {
      const start = startOfMonth(selectedMonth).getTime();
      const end = endOfMonth(selectedMonth).getTime();

      const filtered = expenses.filter(expense => {
        const expenseDate = expense.date;
        const matchesDate = expenseDate >= start && expenseDate <= end;
        const matchesCategory = selectedCategory ? expense.category === selectedCategory : true;

        return matchesDate && matchesCategory;
      });

      setFilteredExpenses(filtered);
    } else {
      setFilteredExpenses([]);
    }
  }, [expenses, selectedMonth, selectedCategory]);

  const handleRefresh = useCallback(async () => {
    if (user) {
      setRefreshing(true);
      try {
        await fetchExpensesForUser(user.id);
      } catch (error) {
        console.error('Error refreshing expenses:', error);
      } finally {
        setRefreshing(false);
      }
    }
  }, [user, fetchExpensesForUser]);

  const handleAddExpense = useCallback(() => {
    try {
      router.push('/expenses/add');
    } catch (error) {
      console.error('Navigation error:', error);
      Alert.alert(t('common.error'), t('common.navigationError') || 'Navigation failed');
    }
  }, [router, t]);

  const handleExpensePress = useCallback((expense: Expense) => {
    try {
      router.push(`/expenses/${expense.id}`);
    } catch (error) {
      console.error('Navigation error:', error);
      Alert.alert(t('common.error'), t('common.navigationError') || 'Navigation failed');
    }
  }, [router, t]);

  const handleCategoryFilter = (category: string | null) => {
    setSelectedCategory(category);
  };

  const handleDateChange = (date: Date) => {
    const newDate = new Date(date);
    newDate.setDate(1);
    setSelectedMonth(newDate);
    setShowMonthPicker(false);
  };

  // Show loading state
  if (isLoading && !refreshing) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: screenBackgroundColor }]} edges={['left', 'right']}>
        <LoadingIndicator fullScreen message={t('expenses.loading')} />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: screenBackgroundColor }]} edges={['left', 'right']}>
      {/* Monthly Overview Header */}
      <View style={[
        styles.monthlyOverviewContainer,
        { backgroundColor: sectionCardColor },
        isDarkMode && {
          borderBottomWidth: 1,
          borderBottomColor: borderColor
        }
      ]}>
        <View style={[styles.monthlyOverviewHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
          <Text style={[styles.monthlyOverviewTitle, { color: textColor }]}>{t('expenses.monthlyOverview') || 'Monthly Overview'}</Text>
          <TouchableOpacity
            style={[styles.monthSelector, isDarkMode && styles.monthSelectorDark]}
            onPress={() => setShowMonthPicker(true)}
          >
            <Text style={[styles.monthSelectorText, isDarkMode && styles.monthSelectorTextDark]}>
              {formatDateToYearsandMonths(selectedMonth, locale)}
            </Text>
            <ChevronDown size={16} color={primaryColor} />
          </TouchableOpacity>
        </View>

        <View style={styles.statsContainer}>
          <View style={[
            styles.statCard,
            { backgroundColor: isDarkMode ? sectionCardColor : '#E6F7EF' },
            isDarkMode && {
              borderWidth: 1,
              borderColor: borderColor
            }
          ]}>
            <Text style={[styles.statLabel, { color: secondaryTextColor }]}>{t('expenses.totalSpent') || 'Total Spent'}</Text>
            <Text style={[styles.statValue, { color: textColor }]}>
              {getCurrencySymbol(displayCurrency)} {totalSpent.toLocaleString()}
            </Text>
            {expenseTotals.hasMultipleCurrencies && expenseTotals.otherCurrencies.length > 0 && (
              <View style={styles.otherCurrenciesContainer}>
                {expenseTotals.otherCurrencies.map(({ currency, amount }) => (
                  <Text key={currency} style={[styles.otherCurrencyText, { color: secondaryTextColor }]}>
                    + {getCurrencySymbol(currency)} {amount.toLocaleString()}
                  </Text>
                ))}
              </View>
            )}
            {totalSpent > 0 && (
              <View style={styles.percentChangeContainer}>
                {spentPercentChange !== null ? (
                  spentPercentChange > 0 ? (
                    <>
                      <ArrowUpRight size={14} color={successColor} />
                      <Text style={[styles.percentChangeText, styles.percentIncrease]}>
                        {Math.abs(spentPercentChange)}% vs last month
                      </Text>
                    </>
                  ) : spentPercentChange < 0 ? (
                    <>
                      <ArrowDownRight size={14} color={errorColor} />
                      <Text style={[styles.percentChangeText, styles.percentDecrease]}>
                        {Math.abs(spentPercentChange)}% vs last month
                      </Text>
                    </>
                  ) : (
                    <Text style={[styles.percentChangeText, { color: secondaryTextColor }]}>
                      Same as last month
                    </Text>
                  )
                ) : (
                  <Text style={[styles.percentChangeText, { color: secondaryTextColor }]}>
                    First month with expenses
                  </Text>
                )}
              </View>
            )}
          </View>

          <View style={[
            styles.statCard,
            { backgroundColor: isDarkMode ? sectionCardColor : '#EFF6FF' },
            isDarkMode && {
              borderWidth: 1,
              borderColor: borderColor
            }
          ]}>
            <Text style={[styles.statLabel, { color: secondaryTextColor }]}>{t('expenses.totalEntries') || 'Total Entries'}</Text>
            <Text style={[styles.statValue, { color: textColor }]}>{filteredExpenses.length}</Text>
            {filteredExpenses.length > 0 && (
              <View style={styles.percentChangeContainer}>
                {entriesPercentChange !== null ? (
                  entriesPercentChange > 0 ? (
                    <>
                      <ArrowUpRight size={14} color={successColor} />
                      <Text style={[styles.percentChangeText, styles.percentIncrease]}>
                        {Math.abs(entriesPercentChange)}% vs last month
                      </Text>
                    </>
                  ) : entriesPercentChange < 0 ? (
                    <>
                      <ArrowDownRight size={14} color={errorColor} />
                      <Text style={[styles.percentChangeText, styles.percentDecrease]}>
                        {Math.abs(entriesPercentChange)}% vs last month
                      </Text>
                    </>
                  ) : (
                    <Text style={[styles.percentChangeText, { color: secondaryTextColor }]}>
                      Same as last month
                    </Text>
                  )
                ) : (
                  <Text style={[styles.percentChangeText, { color: secondaryTextColor }]}>
                    First month with entries
                  </Text>
                )}
              </View>
            )}
          </View>
        </View>
      </View>

      {/* Month Picker Modal */}
      <Modal
        visible={showMonthPicker}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowMonthPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: sectionCardColor }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: textColor }]}>{t('expenses.selectMonth') || 'Select Month'}</Text>
              <TouchableOpacity onPress={() => setShowMonthPicker(false)} style={styles.closeButton}>
                <X size={24} color={textColor} />
              </TouchableOpacity>
            </View>

            <View style={styles.datePickerContainer}>
              <DatePickerInput
                value={tempDate}
                onChange={(date) => setTempDate(date)}
                leftIcon={<Calendar size={20} color={textColor} />}
              />

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={[styles.cancelButton, { borderColor: borderColor }]}
                  onPress={() => setShowMonthPicker(false)}
                >
                  <Text style={[styles.cancelButtonText, { color: textColor }]}>{t('common.cancel')}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.confirmButton, { backgroundColor: primaryColor }]}
                  onPress={() => handleDateChange(tempDate)}
                >
                  <Text style={[styles.confirmButtonText, { color: isDarkMode ? textColor : colors.white }]}>{t('common.confirm')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Category Filter */}
      <View style={[styles.filterContainer, { backgroundColor: screenBackgroundColor }]}>
        <Text style={[styles.filterLabel, { color: textColor }]}>{t('expenses.selectCategory')}</Text>
        <GenericDropdown
          placeholder={t('expenses.selectCategory') || 'Select Category'}
          items={categoryOptions}
          value={selectedCategory || 'all'}
          onSelect={(value) => handleCategoryFilter(value === 'all' ? null : value)}
          modalTitle={t('expenses.selectCategory') || 'Select Category'}
          searchPlaceholder={t('common.searchCategories') || 'Search Categories'}
          containerStyle={styles.dropdownStyle}
        />
      </View>

      {filteredExpenses.length === 0 ? (
        <EmptyState
          title={t('expenses.noExpenses')}
          message={t('expenses.addYourFirstExpense')}
          actionLabel={t('expenses.addExpense')}
          onAction={handleAddExpense}
          icon={<DollarSign size={48} color={primaryColor} />}
        />
      ) : (
        <FlatList
          data={filteredExpenses}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <ExpenseCard
              expense={item}
              onPress={() => handleExpensePress(item)}
            />
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[primaryColor]}
              tintColor={primaryColor}
            />
          }
        />
      )}

      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.floatingButton, { backgroundColor: primaryColor }]}
        onPress={handleAddExpense}
      >
        <Plus size={24} color="white" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 0,
  },
  monthlyOverviewContainer: {
    padding: 12,
    paddingTop: 8,
    marginBottom: 0,
  },
  monthlyOverviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  monthlyOverviewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  monthSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: "#E2E8F0",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  monthSelectorDark: {
    backgroundColor: '#4B5563',
  },
  monthSelectorText: {
    fontSize: 14,
    fontWeight: '500',
    color: "#64748B",
    marginRight: 4,
  },
  monthSelectorTextDark: {
    color: '#D1D5DB',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 8,
    padding: 12,
  },
  statLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  percentChangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  percentChangeText: {
    fontSize: 12,
    marginLeft: 2,
  },
  percentIncrease: {
    color: '#16A34A',
  },
  percentDecrease: {
    color: '#DC2626',
  },
  filterContainer: {
    paddingHorizontal: 12,
    paddingTop: 5,
    paddingBottom: 6,
    minHeight: 95
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  dropdownStyle: {
    marginBottom: 8,
  },
  colorfulIconBg: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatingButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  listContent: {
    padding: 12,
    paddingTop: 0,
    paddingBottom: 100,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    borderRadius: 12,
    overflow: 'hidden',
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    gap: 12,
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderWidth: 1,
  },
  cancelButtonText: {
    fontWeight: '500',
  },
  confirmButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  confirmButtonText: {
    fontWeight: '500',
  },
  otherCurrenciesContainer: {
    marginTop: 4,
    gap: 2,
  },
  otherCurrencyText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
});