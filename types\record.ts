import { Symptom } from './symptom';

export interface AnimalRecord {
  id: string;
  animalId: string;
  date: number; // timestamp
  title: string;
  description: string;
  type: RecordType;
  symptoms: Symptom[];
  medications?: Medication[];
  vaccinations?: Vaccination[];
  treatments?: Treatment[];
  attachments?: Attachment[];
  confirmedDisease?: Disease;
  predictedDiseases?: Disease[];
  treatment?: string;
  createdBy?: string;
  createdAt: number;
  updatedAt: number;
}

export enum RecordType {
  GENERAL = 'general',
  MEDICATION = 'medication',
  VACCINATION = 'vaccination',
  TREATMENT = 'treatment',
  SURGERY = 'surgery',
  BIRTH = 'birth',
  DEATH = 'death',
  OTHER = 'other',
}

export interface Medication {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  startDate: number;
  endDate?: number;
  notes?: string;
}

export interface Vaccination {
  id: string;
  name: string;
  date: number;
  expiryDate?: number;
  batchNumber?: string;
  manufacturer?: string;
  notes?: string;
}

export interface Treatment {
  id: string;
  name: string;
  date: number;
  provider?: string;
  cost?: number;
  notes?: string;
}

export interface Attachment {
  id: string;
  type: 'image' | 'document' | 'audio' | 'video';
  url: string;
  name: string;
  size?: number;
  mimeType?: string;
  createdAt: number;
}

export interface Disease {
  id: string;
  name: string;
  description: string;
  symptoms: string[];
  treatments: string[];
  severity: 'low' | 'medium' | 'high';
  zoonotic: boolean;
}
