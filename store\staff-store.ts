import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { collection, addDoc, updateDoc, deleteDoc, getDocs, doc, query, where, getDoc, setDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Define Staff type
export interface Staff {
  id: string;
  name: string;
  position?: string;
  role?: string;
  phone?: string;
  email?: string;
  salary?: number;
  farmId: string;
  tenantId: string;
  imageUrl?: string;
  createdAt: number;
  updatedAt: number;
  isEmployee: boolean;
}

interface StaffState {
  staff: Staff[];
  isLoading: boolean;
  error: string | null;
  fetchStaff: (farmId: string) => Promise<Staff[]>;
  getStaffById: (id: string) => Promise<Staff | null>;
  addStaff: (staff: Omit<Staff, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Staff>;
  updateStaff: (id: string, updates: Partial<Staff>) => Promise<Staff>;
  deleteStaff: (id: string) => Promise<void>;
  clearStaff: () => void;
}

export const useStaffStore = create<StaffState>()(
  persist(
    (set, get) => ({
      staff: [],
      isLoading: false,
      error: null,
      
      fetchStaff: async (farmId: string) => {
        set({ isLoading: true, error: null });
        try {
          // Query Firestore for staff belonging to this farm
          const staffRef = collection(firestore, 'staff');
          const q = query(staffRef, where("farmId", "==", farmId));
          const querySnapshot = await getDocs(q);

          const fetchedStaff: Staff[] = [];
          querySnapshot.forEach((doc) => {
            fetchedStaff.push({ id: doc.id, ...doc.data() } as Staff);
          });

          set({ 
            staff: fetchedStaff,
            isLoading: false 
          });
          
          return fetchedStaff;
        } catch (error) {
          console.error('Error fetching staff:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch staff',
            isLoading: false
          });
          return [];
        }
      },
      
      getStaffById: async (id: string) => {
        try {
          // First check if the staff is in the store
          const storeStaff = get().staff.find(s => s.id === id);
          if (storeStaff) return storeStaff;
          
          // If not in store, fetch from Firestore
          const docRef = doc(firestore, 'staff', id);
          const docSnap = await getDoc(docRef);
          
          if (docSnap.exists()) {
            return { id: docSnap.id, ...docSnap.data() } as Staff;
          }
          
          return null;
        } catch (error) {
          console.error('Error getting staff:', error);
          return null;
        }
      },
      
      addStaff: async (staffData) => {
        set({ isLoading: true, error: null });
        try {
          const timestamp = Date.now();
          
          // Handle image upload if present
          let imageUrl = staffData.imageUrl;
          if (imageUrl && (imageUrl.startsWith('file:') || 
                          imageUrl.startsWith('content:') || 
                          imageUrl.startsWith('ph:') ||
                          imageUrl.startsWith('data:image'))) {
            const storage = getStorage();
            const imagePath = `staff/${staffData.tenantId}/${timestamp}.jpg`;
            const imageRef = ref(storage, imagePath);
            
            let blob: Blob;
            const response = await fetch(imageUrl);
            blob = await response.blob();
            
            await uploadBytes(imageRef, blob);
            imageUrl = await getDownloadURL(imageRef);
          }
          
          const staffWithTimestamps = {
            ...staffData,
            imageUrl,
            createdAt: timestamp,
            updatedAt: timestamp,
            isEmployee: true // Add flag to identify as employee
          };
          
          // Add to users collection instead of staff collection
          const userRef = doc(collection(firestore, 'users'));
          await setDoc(userRef, staffWithTimestamps);
          
          const newStaff = {
            id: userRef.id,
            ...staffWithTimestamps,
          };
          
          set(state => ({
            staff: [...state.staff, newStaff],
            isLoading: false,
          }));
          
          return newStaff;
        } catch (error) {
          console.error('Error adding staff:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to add staff',
            isLoading: false
          });
          throw error;
        }
      },
      
      updateStaff: async (id, updates) => {
        set({ isLoading: true, error: null });
        try {
          const timestamp = Date.now();
          
          // Handle image upload if present
          let imageUrl = updates.imageUrl;
          if (imageUrl && (imageUrl.startsWith('file:') || 
                          imageUrl.startsWith('content:') || 
                          imageUrl.startsWith('ph:') ||
                          imageUrl.startsWith('data:image'))) {
            const storage = getStorage();
            const imagePath = `staff/${updates.tenantId || get().staff.find(s => s.id === id)?.tenantId}/${timestamp}.jpg`;
            const imageRef = ref(storage, imagePath);
            
            let blob: Blob;
            const response = await fetch(imageUrl);
            blob = await response.blob();
            
            await uploadBytes(imageRef, blob);
            imageUrl = await getDownloadURL(imageRef);
            updates.imageUrl = imageUrl;
          }
          
          const updatesWithTimestamp = {
            ...updates,
            updatedAt: timestamp,
          };
          
          await updateDoc(doc(firestore, 'staff', id), updatesWithTimestamp);
          
          const updatedStaff = get().staff.find(s => s.id === id);
          if (!updatedStaff) {
            throw new Error('Staff not found');
          }
          
          const newStaff = {
            ...updatedStaff,
            ...updatesWithTimestamp,
          };
          
          set(state => ({
            staff: state.staff.map(s => s.id === id ? newStaff : s),
            isLoading: false,
          }));
          
          return newStaff;
        } catch (error) {
          console.error('Error updating staff:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to update staff',
            isLoading: false
          });
          throw error;
        }
      },
      
      deleteStaff: async (id) => {
        set({ isLoading: true, error: null });
        try {
          await deleteDoc(doc(firestore, 'staff', id));
          
          set(state => ({
            staff: state.staff.filter(s => s.id !== id),
            isLoading: false,
          }));
        } catch (error) {
          console.error('Error deleting staff:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to delete staff',
            isLoading: false
          });
          throw error;
        }
      },

      clearStaff: () => {
        set({ staff: [], error: null });
      },
    }),
    {
      name: 'staff-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
