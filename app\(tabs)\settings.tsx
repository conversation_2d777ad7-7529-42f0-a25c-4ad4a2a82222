import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useSettingsStore } from '@/store/settings-store';
import { useAuthStore } from '@/store/auth-store';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import {
  LogOut,
  Moon,
  Sun,
  Volume2,
  VolumeX,
  Globe,
  User,
  Type,
  Info,
  ChevronRight,
  Sparkles,
  AlertTriangle,
} from 'lucide-react-native';
import Button from '@/components/Button';

export default function SettingsScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { logout, user } = useAuthStore();
  const themedColors = useThemeColors();
  const {
    language,
    textSize,
    soundEnabled,
    darkMode,
    aiVisionEnabled,
    setSoundEnabled,
    setDarkMode,
    setAIVisionEnabled,
  } = useSettingsStore();

  const styles = getStyles(themedColors, language);

  const handleLogout = async () => {
    Alert.alert(
      t('settings.logoutTitle'),
      t('settings.logoutMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.logout'),
          onPress: async () => {
            await logout();
            router.replace('/');
          },
          style: 'destructive',
        },
      ]
    );
  };

  const handleLanguageSelect = () => {
    router.push('/language-selection');
  };

  const handleTextSizeSelect = () => {
    router.push('/text-size');
  };

  const handleAbout = () => {
    router.push('/about');
  };

  const handleAccountSettings = () => {
    router.push('/account/update');
  };

  const handleSymptomsPress = () => {
    router.push('/symptoms-checker');
  };

  const handleProfilePress = () => {
    router.push('/profile');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themedColors.background }]} edges={['bottom']}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('settings.account')}</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleProfilePress}
          >
            <View style={styles.settingIconContainer}>
              <User size={20} color={themedColors.primary} />
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{t('settings.profile')}</Text>
              <Text style={styles.settingDescription}>{t('settings.profileDescription')}</Text>
            </View>
            <ChevronRight size={20} color={themedColors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleAccountSettings}
          >
            <View style={styles.settingIconContainer}>
              <User size={20} color={themedColors.primary} />
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{t('settings.accountSettings')}</Text>
              <Text style={styles.settingDescription}>{t('settings.accountSettingsDescription')}</Text>
            </View>
            <ChevronRight size={20} color={themedColors.textSecondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('settings.appearance')}</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleLanguageSelect}
          >
            <View style={styles.settingIconContainer}>
              <Globe size={20} color={themedColors.primary} />
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{t('settings.language')}</Text>
              <Text style={styles.settingDescription}>
                {language === 'en' ? 'English' : language === 'ur' ? 'اردو' : 'ਪੰਜਾਬੀ'}
              </Text>
            </View>
            <ChevronRight size={20} color={themedColors.textSecondary} />
          </TouchableOpacity>

          {/* <TouchableOpacity  Non functional will implement latter on
            style={styles.settingItem}
            onPress={handleTextSizeSelect}
          >
            <View style={styles.settingIconContainer}>
              <Type size={20} color={themedColors.primary} />
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{t('settings.textSize')}</Text>
              <Text style={styles.settingDescription}>
                {textSize === 'small'
                  ? t('settings.textSizeSmall')
                  : textSize === 'medium'
                    ? t('settings.textSizeMedium')
                    : t('settings.textSizeLarge')}
              </Text>
            </View>
            <ChevronRight size={20} color={themedColors.textSecondary} />
          </TouchableOpacity> */}

          <View style={styles.settingItem}>
            <View style={styles.settingIconContainer}>
              {darkMode ? (
                <Moon size={20} color={themedColors.primary} />
              ) : (
                <Sun size={20} color={themedColors.primary} />
              )}
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{t('settings.darkMode')}</Text>
              <Text style={styles.settingDescription}>{t('settings.darkModeDescription')}</Text>
            </View>
            <Switch
              value={darkMode}
              onValueChange={setDarkMode}
              trackColor={{ false: themedColors.inactive || '#E5E7EB', true: themedColors.primaryLight }}
              thumbColor={darkMode ? themedColors.primary : themedColors.card}
              ios_backgroundColor={themedColors.inactive || '#E5E7EB'}
            />
          </View>
        </View>

        {/* <View style={styles.section}> need to imlement
          <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('settings.preferences')}</Text>

          <View style={styles.settingItem}>
            <View style={styles.settingIconContainer}>
              {soundEnabled ? (
                <Volume2 size={20} color={themedColors.primary} />
              ) : (
                <VolumeX size={20} color={themedColors.primary} />
              )}
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{t('settings.sound')}</Text>
              <Text style={styles.settingDescription}>{t('settings.soundDescription')}</Text>
            </View>
            <Switch
              value={soundEnabled}
              onValueChange={setSoundEnabled}
              trackColor={{ false: themedColors.inactive || '#E5E7EB', true: themedColors.primaryLight }}
              thumbColor={soundEnabled ? themedColors.primary : themedColors.card}
            />
          </View>
        </View> */}

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('settings.aiFeatures')}</Text>

          <View style={styles.settingItem}>
            <View style={styles.settingIconContainer}>
              <Sparkles size={20} color={themedColors.primary} />
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{t('settings.aiVisionAnalysis')}</Text>
              <Text style={styles.settingDescription}>
                {t('settings.aiVisionDescription')}
              </Text>
            </View>
            <Switch
              value={aiVisionEnabled}
              onValueChange={setAIVisionEnabled}
              trackColor={{ false: themedColors.inactive || '#E5E7EB', true: themedColors.primaryLight }}
              thumbColor={aiVisionEnabled ? themedColors.primary : themedColors.card}
              ios_backgroundColor={themedColors.inactive || '#E5E7EB'}
            />
          </View>


        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('settings.about')}</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleAbout}
          >
            <View style={styles.settingIconContainer}>
              <Info size={20} color={themedColors.primary} />
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{t('settings.aboutApp')}</Text>
              <Text style={styles.settingDescription}>{t('settings.aboutAppDescription')}</Text>
            </View>
            <ChevronRight size={20} color={themedColors.textSecondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('settings.healthTools')}</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleSymptomsPress}
          >
            <View style={styles.settingIconContainer}>
              <AlertTriangle size={20} color={themedColors.primary} />
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{t('settings.symptomsChecker')}</Text>
              <Text style={styles.settingDescription}>{t('settings.symptomsCheckerDescription')}</Text>
            </View>
            <ChevronRight size={20} color={themedColors.textSecondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.logoutSection}>
          <Button
            title={t('settings.logout')}
            onPress={handleLogout}
            variant="outline"
            leftIcon={<LogOut size={20} color={themedColors.error} />}
            style={styles.logoutButton}
            textStyle={styles.logoutButtonText}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set by SafeAreaView
  },
  urduText: {
    fontFamily: Platform.OS === 'ios' ? 'Geeza Pro' : 'NotoNaskhArabic-Regular', // Example fonts
    textAlign: 'right',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
    backgroundColor: themedColors.isDarkMode ? 'transparent' : themedColors.card,
    marginHorizontal: themedColors.isDarkMode ? 0 : 8,
    borderRadius: themedColors.isDarkMode ? 0 : 12,
    paddingVertical: themedColors.isDarkMode ? 0 : 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: themedColors.isDarkMode ? 0 : 3 },
        shadowOpacity: themedColors.isDarkMode ? 0 : 0.12,
        shadowRadius: themedColors.isDarkMode ? 0 : 6,
      },
      android: {
        elevation: themedColors.isDarkMode ? 0 : 3,
      },
    }),
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: themedColors.isDarkMode ? 12 : 16,
    marginLeft: themedColors.isDarkMode ? 8 : 0,
    marginRight: themedColors.isDarkMode ? 8 : 0,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  settingItem: {
    flexDirection: language === 'ur' ? 'row-reverse' : 'row',
    alignItems: 'center',
    backgroundColor: themedColors.isDarkMode ? themedColors.card : '#FAFAFA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: themedColors.isDarkMode ? 1 : 2 },
        shadowOpacity: themedColors.isDarkMode ? 0.2 : 0.08,
        shadowRadius: themedColors.isDarkMode ? 2 : 4,
      },
      android: {
        elevation: themedColors.isDarkMode ? 1 : 2,
      },
    }),
  },
  settingIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: themedColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: language === 'ur' ? 0 : 12,
    marginLeft: language === 'ur' ? 12 : 0,
  },
  settingContent: {
    flex: 1,
    marginHorizontal: 8,
    alignItems: language === 'ur' ? 'flex-end' : 'flex-start',
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 4,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  settingDescription: {
    fontSize: 14,
    color: themedColors.textSecondary,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  logoutSection: {
    padding: 16,
    marginBottom: 24,
    marginHorizontal: themedColors.isDarkMode ? 0 : 8,
    backgroundColor: themedColors.isDarkMode ? 'transparent' : themedColors.card,
    borderRadius: themedColors.isDarkMode ? 0 : 12,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: themedColors.isDarkMode ? 0 : 3 },
        shadowOpacity: themedColors.isDarkMode ? 0 : 0.12,
        shadowRadius: themedColors.isDarkMode ? 0 : 6,
      },
      android: {
        elevation: themedColors.isDarkMode ? 0 : 3,
      },
    }),
  },
  logoutButton: {
    borderColor: themedColors.error,
    backgroundColor: themedColors.isDarkMode ? 'rgba(255, 59, 48, 0.2)' : 'rgba(255, 59, 48, 0.1)',
  },
  logoutButtonText: {
    color: themedColors.error,
  },

});
