import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Image,
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAnimalStore } from '@/store/animal-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { ArrowLeft, Activity, Calendar } from 'lucide-react-native';

interface Alert {
  id: string;
  type: 'overdue' | 'abnormality';
  animalId: string;
  animalName: string;
  message: string;
  date: number;
  severity: 'high' | 'medium' | 'low';
  imageUri?: string;
}

export default function AlertsScreen() {
  const router = useRouter();
  const { animals } = useAnimalStore();
  const { healthChecks } = useHealthCheckStore();
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);

  const [refreshing, setRefreshing] = useState(false);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isLoading, setIsLoading] = useState(true); // Keep this for initial loading

  const generateAlerts = () => {
    if (animals.length === 0) {
      setAlerts([]);
      setIsLoading(false);
      return;
    }

    const now = Date.now();
    const newAlerts: Alert[] = [];

    // Check for overdue health checks
    animals.forEach(animal => {
      // Get the latest health check for this animal
      const animalHealthChecks = healthChecks
        .filter(check => check.animalId === animal.id)
        .sort((a, b) => b.date - a.date);

      if (animalHealthChecks.length === 0) {
        // No health checks yet, consider it overdue if animal was added more than 7 days ago
        if (now - animal.createdAt > 7 * 24 * 60 * 60 * 1000) {
          newAlerts.push({
            id: `overdue-${animal.id}-${now}`,
            type: 'overdue',
            animalId: animal.id,
            animalName: animal.name,
            message: t('healthChecks.initialCheckOverdue', { name: animal.name }),
            date: now,
            severity: 'high',
            imageUri: animal.imageUri,
          });
        }
      } else {
        const latestCheck = animalHealthChecks[0];
        // Check if next check date is in the past
        if (latestCheck.nextCheckDate && latestCheck.nextCheckDate < now) {
          const daysPastDue = Math.floor((now - latestCheck.nextCheckDate) / (24 * 60 * 60 * 1000));
          let severity: 'high' | 'medium' | 'low' = 'low';

          if (daysPastDue > 14) {
            severity = 'high';
          } else if (daysPastDue > 7) {
            severity = 'medium';
          }

          newAlerts.push({
            id: `overdue-${animal.id}-${latestCheck.id}`,
            type: 'overdue',
            animalId: animal.id,
            animalName: animal.name,
            message: t('healthChecks.checkOverdueByDays', { name: animal.name, days: daysPastDue }),
            date: latestCheck.nextCheckDate,
            severity,
            imageUri: animal.imageUri,
          });
        }
      }

      // Check for abnormalities in the latest health check
      if (animalHealthChecks.length > 0) {
        const latestCheck = animalHealthChecks[0];
        if (latestCheck.abnormalities) {
          // Only add if the check was within the last 30 days
          if (now - latestCheck.date < 30 * 24 * 60 * 60 * 1000) {
            newAlerts.push({
              id: `abnormality-${animal.id}-${latestCheck.id}`,
              type: 'abnormality',
              animalId: animal.id,
              animalName: animal.name,
              message: t('alerts.hasHealthAbnormalities', { name: animal.name }),
              date: latestCheck.date,
              severity: 'high',
              imageUri: animal.imageUri,
            });
          }
        }
      }
    });

    // Sort alerts by severity (high to low) and then by date (newest first)
    newAlerts.sort((a, b) => {
      const severityOrder = { high: 0, medium: 1, low: 2 };
      if (severityOrder[a.severity] !== severityOrder[b.severity]) {
        return severityOrder[a.severity] - severityOrder[b.severity];
      }
      return b.date - a.date;
    });

    setAlerts(newAlerts);
    setIsLoading(false);
  };

  useEffect(() => {
    generateAlerts();
  }, [animals, healthChecks]);

  const handleRefresh = async () => {
    setRefreshing(true);
    generateAlerts();
    setRefreshing(false);
  };

  const handleAlertPress = (alert: Alert) => {
    if (alert.type === 'overdue') {
      router.push(`/health-checks/add?animalId=${alert.animalId}`);
    } else {
      router.push(`/animals/${alert.animalId}`);
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderAlertItem = ({ item }: { item: Alert }) => {
    // Determine alert type (not used in UI currently but kept for future use)
    // const isOverdue = item.type === 'overdue';

    return (
      <TouchableOpacity // @ts-ignore
        style={styles.alertItem}
        onPress={() => handleAlertPress(item)}
        activeOpacity={0.8}
      >
        <View style={styles.alertImageContainer}>
          {item.imageUri ? (
            <Image
              source={{ uri: item.imageUri }}
              style={styles.animalImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderImage}>
              <Text style={styles.placeholderText}>{item.animalName.charAt(0)}</Text>
            </View>
          )}
        </View>

        <View style={styles.alertContent}>
          <Text style={styles.alertTitle}>{item.animalName}</Text>
          <Text style={styles.alertMessage}>{item.message}</Text>
          <View style={styles.dateContainer}>
            <Calendar size={14} color={themedColors.textSecondary} style={styles.dateIcon} />
            <Text style={styles.alertDate}>{formatDate(item.date)}</Text>
          </View>
        </View>

        <View style={[
          styles.severityIndicator,
          { backgroundColor: item.severity === 'high'
            ? themedColors.error
            : item.severity === 'medium'
              ? themedColors.warning
              : themedColors.success
          }
        ]} />
      </TouchableOpacity>
    );
  };

  if (isLoading && !refreshing) {
    return <LoadingIndicator fullScreen message={t('alerts.loadingAlerts')} />;
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('alerts.title'),
        headerTitleAlign: language === 'ur' ? 'center' : 'left',
        headerTitleStyle: { fontWeight: 'bold' },
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={themedColors.text} />
          </TouchableOpacity>
        ),
      }} />

      {alerts.length === 0 ? (
        <EmptyState
          title={t('alerts.noAlerts')}
          message={t('alerts.allHealthy')}
          icon={<Activity size={48} color={themedColors.success} />}
          style={styles.emptyState}
        />
      ) : (
        <FlatList
          data={alerts}
          keyExtractor={item => item.id}
          renderItem={renderAlertItem}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[themedColors.primary]}
              tintColor={themedColors.primary}
            />
          }
          ListHeaderComponent={
            <View style={styles.header}>
              <Text style={styles.headerTitle}>
                {alerts.length} {t('alerts.alertsRequiringAttention')}
              </Text>
              <Text style={styles.headerSubtitle}>
                {t('alerts.tapToTakeAction')}
              </Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 4,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  headerSubtitle: {
    fontSize: 14,
    color: themedColors.textSecondary,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  listContent: {
    padding: 16,
    paddingTop: 0,
  },
  alertItem: {
    flexDirection: language === 'ur' ? 'row-reverse' : 'row',
    backgroundColor: themedColors.card,
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    // Theme-aware shadow properties
    ...(themedColors.isDarkMode ? {
      shadowColor: 'rgba(255,255,255,0.1)',
    } : {
      shadowColor: '#000',
    }),
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: themedColors.isDarkMode ? 0.2 : 0.05,
    shadowRadius: 2,
    elevation: themedColors.isDarkMode ? 1 : 2,
    borderWidth: 1,
    borderColor: themedColors.border,
    maxHeight:120,
    minHeight:100
  },
  alertImageContainer: {
    width: 80,
    height: '100%',
    overflow: 'hidden',
  },
  animalImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: themedColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.primary,
  },
  alertContent: {
    flex: 1,
    padding: 12,
    justifyContent: 'center',
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 4,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  alertMessage: {
    fontSize: 14,
    color: themedColors.text,
    marginBottom: 8,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  dateContainer: {
    flexDirection: language === 'ur' ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  dateIcon: {
    marginHorizontal: 4,
  },
  alertDate: {
    fontSize: 12,
    color: themedColors.textSecondary,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  severityIndicator: {
    width: 4,
    // backgroundColor is set dynamically
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
  },
});