// This script can be run to fix any discrepancies between the staffCount field
// and the actual number of staff members in a farm.

const { firestore } = require('../config/firebase');
const { collection, getDocs, doc, getDoc, updateDoc, query, where } = require('firebase/firestore');

async function fixStaffCount() {
  try {
    console.log('Starting staff count fix script...');
    
    // Get all farms
    const farmsRef = collection(firestore, 'farms');
    const farmsSnapshot = await getDocs(farmsRef);
    
    console.log(`Found ${farmsSnapshot.size} farms to process`);
    
    // Process each farm
    for (const farmDoc of farmsSnapshot.docs) {
      const farmId = farmDoc.id;
      const farmData = farmDoc.data();
      
      console.log(`Processing farm: ${farmData.name} (${farmId})`);
      
      // Get all employees for this farm
      const employeesRef = collection(firestore, 'users');
      const q = query(employeesRef, 
        where('assignedFarms', 'array-contains', farmId),
        where('isEmployee', '==', true));
      const employeesSnapshot = await getDocs(q);
      
      const actualStaffCount = employeesSnapshot.size;
      const currentStaffCount = farmData.staffCount || 0;
      
      console.log(`Current staff count: ${currentStaffCount}, Actual staff count: ${actualStaffCount}`);
      
      // If there's a discrepancy, update the farm document
      if (currentStaffCount !== actualStaffCount) {
        console.log(`Fixing staff count for farm ${farmId} from ${currentStaffCount} to ${actualStaffCount}`);
        
        // Get all employee IDs
        const employeeIds = employeesSnapshot.docs.map(doc => doc.id);
        
        // Update the farm document
        await updateDoc(doc(firestore, 'farms', farmId), {
          staff: employeeIds,
          staffCount: actualStaffCount,
          updatedAt: Date.now()
        });
        
        console.log(`Successfully fixed staff count for farm ${farmId}`);
      } else {
        console.log(`Staff count for farm ${farmId} is already correct (${actualStaffCount})`);
      }
    }
    
    console.log('Staff count fix script completed successfully');
  } catch (error) {
    console.error('Error fixing staff count:', error);
  }
}

// Run the function
fixStaffCount();
