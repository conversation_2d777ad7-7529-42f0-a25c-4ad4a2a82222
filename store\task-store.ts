import { create } from 'zustand';
import { Task, TaskStatus } from '@/types/task';
import { 
  getTasks, 
  addTask, 
  updateTask, 
  deleteTask 
} from '@/services/task-service';

interface TaskState {
  tasks: Task[];
  loading: boolean;
  error: string | null;

  // Actions
  fetchTasks: (userId: string, userRole: string) => Promise<void>;
  addNewTask: (taskData: Partial<Task>) => Promise<string>;
  updateTaskStatus: (taskId: string, status: TaskStatus) => Promise<void>;
  removeTask: (taskId: string) => Promise<void>;
  updateTaskDetails: (taskId: string, updates: Partial<Task>) => Promise<void>;
  clearTasks: () => void;
}

export const useTaskStore = create<TaskState>((set, get) => ({
  tasks: [],
  loading: false,
  error: null,
  
  fetchTasks: async (userId: string, userRole: string) => {
    try {
      set({ loading: true, error: null });
      const tasks = await getTasks(userId, userRole);
      set({ tasks, loading: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch tasks', 
        loading: false 
      });
    }
  },
  
  addNewTask: async (taskData: Partial<Task>) => {
    try {
      set({ loading: true, error: null });
      const taskId = await addTask(taskData);
      
      // Refresh tasks list
      const { fetchTasks } = get();
      if (taskData.assignedTo) {
        await fetchTasks(taskData.assignedTo, 'user'); // Assuming 'user' role for assigned tasks
      }
      
      set({ loading: false });
      return taskId;
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to add task', 
        loading: false 
      });
      throw error;
    }
  },
  
  updateTaskStatus: async (taskId: string, status: TaskStatus) => {
    try {
      set({ loading: true, error: null });
      
      // Find the task to get the assignee ID
      const task = get().tasks.find(t => t.id === taskId);
      if (!task) throw new Error('Task not found');
      
      await updateTask(taskId, { 
        status,
        completed_at: status === TaskStatus.COMPLETED ? new Date() : undefined
      });
      
      // Refresh tasks list
      const { fetchTasks } = get();
      await fetchTasks(task.assignedTo, 'user'); // Assuming 'user' role for assigned tasks
      
      set({ loading: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update task', 
        loading: false 
      });
      throw error;
    }
  },
  
  removeTask: async (taskId: string) => {
    try {
      set({ loading: true, error: null });
      
      // Find the task to get the assignee ID
      const task = get().tasks.find(t => t.id === taskId);
      if (!task) throw new Error('Task not found');
      
      await deleteTask(taskId);
      
      // Update local state
      set(state => ({
        tasks: state.tasks.filter(t => t.id !== taskId),
        loading: false
      }));
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to remove task', 
        loading: false 
      });
      throw error;
    }
  },
  
  updateTaskDetails: async (taskId: string, updates: Partial<Task>) => {
    try {
      set({ loading: true, error: null });
      
      // Find the task to get the assignee ID
      const task = get().tasks.find(t => t.id === taskId);
      if (!task) throw new Error('Task not found');
      
      await updateTask(taskId, updates);
      
      // Refresh tasks list
      const { fetchTasks } = get();
      await fetchTasks(task.assignedTo, 'user'); // Assuming 'user' role for assigned tasks
      
      set({ loading: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update task', 
        loading: false 
      });
      throw error;
    }
  },

  clearTasks: () => {
    set({ tasks: [], error: null });
  }
}));


