import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useAuthStore } from '@/store/auth-store';
import { useAnimalStore } from '@/store/animal-store';
import { useRecordStore } from '@/store/record-store';
import { RecordCard } from '@/components/RecordCard';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import GenericDropdown, { DropdownItem } from '@/components/GenericDropdown';
import { useTranslation } from '@/hooks/useTranslation';
import { Plus, FileText, ArrowLeft, Filter, Syringe, Pill, Scissors, <PERSON>, Stethoscope, FileType } from 'lucide-react-native';
import { RecordType } from '@/types/record';

export default function HealthRecordsScreen() {
  const router = useRouter();
  const themedColors = useThemeColors();
  const { user } = useAuthStore();
  const { animals } = useAnimalStore();
  const { records, fetchRecords, isLoading } = useRecordStore();
  const { t, language } = useTranslation();

  const [refreshing, setRefreshing] = useState(false);
  const [filterType, setFilterType] = useState<RecordType | 'all'>('all');

  useEffect(() => {
    if (user) {
      fetchRecords();
    }
  }, [user]);

  const handleRefresh = async () => {
    setRefreshing(true);
    if (user) {
      await fetchRecords();
    }
    setRefreshing(false);
  };

  const handleAddRecord = () => {
    router.push('/records/add');
  };

  // Filter records based on selected type
  const filteredRecords = records.filter(record => {
    if (filterType === 'all') return true;
    return record.type === filterType;
  });

  // Sort records by date (newest first)
  const sortedRecords = [...filteredRecords].sort((a, b) => b.date - a.date);

  // Filter options for dropdown
  const filterOptions: DropdownItem[] = [
    { id: 'all', label: t('records.allRecords'), icon: <FileText size={32} color={themedColors.primary} />, description: t('records.showAllRecords') },
    { id: RecordType.VACCINATION, label: t('records.vaccinations'), icon: <Syringe size={32} color={themedColors.primary} />, description: t('records.vaccinationsDescription') },
    { id: RecordType.MEDICATION, label: t('records.medications'), icon: <Pill size={32} color={themedColors.primary} />, description: t('records.medicationsDescription') },
    { id: RecordType.SURGERY, label: t('records.surgeries'), icon: <Scissors size={32} color={themedColors.primary} />, description: t('records.surgeriesDescription') },
    { id: RecordType.GENERAL, label: t('records.checkups'), icon: <Stethoscope size={32} color={themedColors.primary} />, description: t('records.checkupsDescription') },
    { id: RecordType.BIRTH, label: t('records.births'), icon: <Baby size={32} color={themedColors.primary} />, description: t('records.birthsDescription') },
  ];

  if (isLoading && !refreshing) {
    return <LoadingIndicator fullScreen message={t('records.title')} />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themedColors.background }]} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('records.title'),
        headerTitleAlign: language === 'ur' ? 'center' : 'left',
        headerTitleStyle: { fontWeight: 'bold', color: themedColors.text },
        headerStyle: {
          backgroundColor: themedColors.background,
        },
        headerTintColor: themedColors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={themedColors.text} />
          </TouchableOpacity>
        )
      }} />

      <View style={[styles.formGroup, { backgroundColor: themedColors.background, borderBottomColor: themedColors.border }]}>
        <View style={styles.labelContainer}>
          <FileText size={24} color={themedColors.primary} />
          <Text style={[styles.label, { color: themedColors.text }, language === 'ur' ? styles.urduText : null]}>{t('records.filterRecords')}</Text>
        </View>
        <GenericDropdown
          placeholder={t('records.searchRecordTypes')}
          items={filterOptions}
          value={filterType}
          onSelect={(value) => setFilterType(value as RecordType | 'all')}
          modalTitle={t('records.filterHealthRecords')}
          searchPlaceholder="Search record types..."
          renderIcon={<FileType size={24} color={themedColors.textSecondary} />}
        />
      </View>

      {sortedRecords.length === 0 ? (
        <EmptyState
          title={t('records.noRecords')}
          message={t('records.addRecordsMessage')}
          actionLabel={t('records.addRecord')}
          onAction={handleAddRecord}
          icon={<FileText size={48} color={themedColors.primary} />}
          style={styles.emptyState}
        />
      ) : (
        <FlatList
          data={sortedRecords}
          keyExtractor={item => item.id}
          renderItem={({ item }) => {
            const animal = animals.find(a => a.id === item.animalId);
            return (
              <RecordCard
                record={item}
                showAnimalName
                animalName={animal?.name}
              />
            );
          }}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[themedColors.primary]}
              tintColor={themedColors.primary}
            />
          }
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  formGroup: {
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 12,
    borderBottomWidth: 1,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  listContent: {
    padding: 16,
  },
  emptyState: {
    marginTop: 40,
  },
});
