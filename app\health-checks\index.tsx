import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import { useAnimalStore } from '@/store/animal-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { useThemeColors } from '@/hooks/useThemeColors';
import HealthCheckCard from '@/components/HealthCheckCard';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { useTranslation } from '@/hooks/useTranslation';
import { Plus, Activity, ArrowLeft, CheckCircle, AlertTriangle, List, Search } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function HealthChecksScreen() {
  const router = useRouter();
  const { user } = useAuthStore();
  const { animals } = useAnimalStore();
  const { healthChecks, fetchHealthChecks, isLoading } = useHealthCheckStore();
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();

  const [refreshing, setRefreshing] = useState(false);
  const [filterType, setFilterType] = useState<'all' | 'normal' | 'abnormal'>('all');

  useEffect(() => {
    if (user) {
      fetchHealthChecks();
    }
  }, [user]);

  const handleRefresh = async () => {
    setRefreshing(true);
    if (user) {
      await fetchHealthChecks();
    }
    setRefreshing(false);
  };

  const handleAddHealthCheck = () => {
    router.push('/health-checks/add');
  };

  // Filter health checks based on selected filter
  const filteredHealthChecks = healthChecks.filter(check => {
    if (filterType === 'normal') return !check.abnormalities;
    if (filterType === 'abnormal') return check.abnormalities;
    return true;
  });

  // Sort health checks by date (newest first)
  const sortedHealthChecks = [...filteredHealthChecks].sort((a, b) => b.date - a.date);

  if (isLoading && !refreshing) {
    return <LoadingIndicator fullScreen message={t('healthChecks.title')} />;
  }

  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('healthChecks.title'),
        headerTitleAlign: language === 'ur' ? 'center' : 'left',
        headerTitleStyle: { fontWeight: 'bold' },
        headerStyle: {
          backgroundColor: themedColors.background,
        },
        headerTintColor: themedColors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={themedColors.text} />
          </TouchableOpacity>
        )
      }} />

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            filterType === 'all' && styles.filterButtonActive
          ]}
          onPress={() => setFilterType('all')}
        >
          <List
            size={16}
            color={filterType === 'all' ? themedColors.primary : themedColors.textSecondary}
          />
          <Text style={[
            styles.filterButtonText,
            filterType === 'all' && styles.filterButtonTextActive,
            language === 'ur' ? styles.urduText : null
          ]}>
            {t('healthChecks.all')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filterType === 'normal' && styles.filterButtonActive,
            filterType === 'normal' && { backgroundColor: themedColors.successLight }
          ]}
          onPress={() => setFilterType('normal')}
        >
          <CheckCircle
            size={16}
            color={filterType === 'normal' ? themedColors.success : themedColors.textSecondary}
          />
          <Text style={[
            styles.filterButtonText,
            filterType === 'normal' && styles.filterButtonTextActive,
            filterType === 'normal' && { color: themedColors.success },
            language === 'ur' ? styles.urduText : null
          ]}>
            {t('healthChecks.normal')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filterType === 'abnormal' && styles.filterButtonActive,
            filterType === 'abnormal' && { backgroundColor: themedColors.errorLight }
          ]}
          onPress={() => setFilterType('abnormal')}
        >
          <AlertTriangle
            size={16}
            color={filterType === 'abnormal' ? themedColors.error : themedColors.textSecondary}
          />
          <Text style={[
            styles.filterButtonText,
            filterType === 'abnormal' && styles.filterButtonTextActive,
            filterType === 'abnormal' && { color: themedColors.error },
            language === 'ur' ? styles.urduText : null
          ]}>
            {t('healthChecks.abnormal')}
          </Text>
        </TouchableOpacity>
      </View>

      {sortedHealthChecks.length === 0 ? (
        <EmptyState
          title={t('healthChecks.noChecks')}
          message={t('healthChecks.performRegularChecks')}
          actionLabel={t('healthChecks.addCheck')}
          onAction={handleAddHealthCheck}
          icon={<Activity size={48} color={themedColors.primary} />}
          style={styles.emptyState}
        />
      ) : (
        <FlatList
          data={sortedHealthChecks}
          keyExtractor={item => item.id}
          renderItem={({ item }) => {
            const animal = animals.find(a => a.id === item.animalId);
            return (
              <HealthCheckCard
                healthCheck={item}
                showAnimalName
                animalName={animal?.name}
              />
            );
          }}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[themedColors.primary]}
              tintColor={themedColors.primary}
            />
          }
        />
      )}

      <TouchableOpacity
        style={styles.floatingButton}
        onPress={handleAddHealthCheck}
      >
        <LinearGradient
          colors={[themedColors.primary, themedColors.primaryDark]}
          style={styles.floatingButtonGradient}
        >
          <Plus size={24} color="white" />
        </LinearGradient>
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  filterContainer: {
    flexDirection: 'row',
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    backgroundColor: themedColors.card,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
  },
  filterButton: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
    backgroundColor: themedColors.background,
    gap: 6,
  },
  filterButtonActive: {
    backgroundColor: themedColors.primaryLight,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: themedColors.textSecondary,
  },
  filterButtonTextActive: {
    color: themedColors.primary,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  floatingButtonGradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
