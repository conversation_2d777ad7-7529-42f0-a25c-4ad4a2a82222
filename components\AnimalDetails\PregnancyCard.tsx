import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { useThemeColors } from '../../hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';

interface Pregnancy {
  id: string;
  status: string;
  expectedDate?: string;
  daysRemaining?: string;
  stage?: string;
  progress?: number;
  animalId: string;
  animalName: string;
  species: string;
  sireName: string;
  animalImageUri?: string;
  imageUrl?: string;
}

const PregnancyCard = ({ pregnancy, onPress }: { pregnancy: Pregnancy; onPress?: () => void }) => {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);
  
  // Get placeholder image for animals without an image
  const getPlaceholderImage = (species) => {
    const placeholderUrl = species?.toLowerCase() === 'cow' 
      ? 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D'
      : species?.toLowerCase() === 'goat'
      ? 'https://images.unsplash.com/photo-1524024973431-2ad916746881?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8Z29hdCUyMGZhY2V8ZW58MHx8MHx8fDA%3D'
      : species?.toLowerCase() === 'poultry'
      ? 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8Y2hpY2tlbiUyMGZhY2V8ZW58MHx8MHx8fDA%3D'
      : species?.toLowerCase() === 'fish'
      ? 'https://images.unsplash.com/photo-1524704796725-9fc3044a58b2?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGZpc2glMjBmYWNlfGVufDB8fDB8fHww'
      : 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y293JTIwZmFjZXxlbnwwfHwwfHx8MA%3D%3D';
    
    return placeholderUrl;
  };
  
  // Use the animal's image from the pregnancy object
  const animalImage = pregnancy.animalImageUri || 
                     pregnancy.imageUrl || 
                     getPlaceholderImage(pregnancy.species);

  // Format expected date if it's not already formatted
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    // If it's already a formatted string with spaces or slashes, assume it's already formatted
    if (typeof dateString === 'string' && (dateString.includes(' ') || dateString.includes('/'))) {
      return dateString;
    }
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (e) {
      console.error('Error formatting date:', e);
      return dateString || '';
    }
  };

  // Debug log to see what's happening

  const cardContent = (
    <View style={styles.cardContainer}>
      <Image
        source={{ uri: animalImage }}
        style={styles.animalImage}
        resizeMode="cover"
        // Add default image on error
        onError={(e) => {
          console.log("Image failed to load:", e.nativeEvent.error);
        }}
      />
      
      <View style={styles.detailsContainer}>
        <View style={styles.statusRow}>
          <Text style={styles.header}>{pregnancy.animalName || 'Unknown'}</Text>
          <Text style={styles.statusBadge}>{t(`pregnancy.${pregnancy.status || 'unknown'}`)}</Text>
        </View>

        <View style={styles.infoGrid}>
          <View style={styles.infoItem}>
            <Text style={styles.label}>{t('pregnancy.sire')}</Text>
            <Text style={styles.value}>{pregnancy.sireName || 'Unknown'}</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Text style={styles.label}>{t('pregnancy.species')}</Text>
            <Text style={styles.value}>{pregnancy.species || 'Unknown'}</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Text style={styles.label}>{t('pregnancy.expectedDate')}</Text>
            <Text style={styles.value}>{formatDate(pregnancy.expectedDate) || 'Unknown'}</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Text style={styles.label}>{t('pregnancy.progress')}</Text>
            <Text style={styles.value}>
              {typeof pregnancy.progress === 'number' 
                ? `${pregnancy.progress.toFixed(2)}%` 
                : '0.00%'}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  // If onPress is provided, wrap in TouchableOpacity, otherwise just return the content
  return onPress ? (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      {cardContent}
    </TouchableOpacity>
  ) : cardContent;
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) =>
  StyleSheet.create({
    cardContainer: {
      flexDirection: 'row',
      backgroundColor: themedColors.card,
      borderRadius: 15,
      marginTop: 15,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: themedColors.isDarkMode ? 0.35 : 0.15,
      shadowRadius: 4,
      elevation: 2,
      overflow: 'hidden', // Ensure image doesn't overflow
    },
    animalImage: {
      width: 90,
      height: 120, // Set a fixed height
      borderTopLeftRadius: 15,
      borderBottomLeftRadius: 15,
    },
    detailsContainer: {
      flex: 1,
      justifyContent: 'center',
      padding: 15,
    },
    statusRow: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 10,
    },
    header: {
      fontSize: 18,
      fontWeight: 'bold',
      color: themedColors.text,
      flex: 1,
    },
    statusBadge: {
      fontSize: 12,
      fontWeight: '600',
      color: '#fff',
      backgroundColor: themedColors.primary,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      overflow: 'hidden',
      textAlign: 'center',
    },
    infoGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 5,
    },
    infoItem: {
      width: '50%',
      marginBottom: 8,
    },
    label: {
      fontSize: 12,
      color: themedColors.textSecondary,
      marginBottom: 2,
      textAlign: language === 'ur' ? 'right' : 'left',
    },
    value: {
      fontSize: 14,
      color: themedColors.text,
      fontWeight: '500',
      textAlign: language === 'ur' ? 'right' : 'left',
    },
  });

export default PregnancyCard;
