import React from 'react';
import { View, FlatList, TouchableOpacity, Text, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { Users, Mail, Phone, ChevronRight } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { Employee } from '@/services/employee-service';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';

interface StaffTabProps {
  farmStaff: Employee[];
  isLoadingStaff: boolean;
  onAddEmployee: () => void;
  styles: any;
}

export default function StaffTab({ farmStaff, isLoadingStaff, onAddEmployee, styles }: StaffTabProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const themedColors = useThemeColors();

  if (isLoadingStaff) {
    return (
      <View style={styles.tabContent}>
        <LoadingIndicator message={t('common.loading')} />
      </View>
    );
  }

  return (
    <View style={styles.tabContent}>
      {farmStaff.length === 0 ? (
        <EmptyState
          title={t('farms.noStaff')}
          message={t('farms.addYourFirstStaff')}
          actionLabel={t('farms.addEmployee')}
          onAction={onAddEmployee}
          icon={<Users size={48} color={themedColors.primary} />}
        />
      ) : (
        <FlatList
          data={farmStaff}
          keyExtractor={item => item.id || Math.random().toString()}
          renderItem={({ item }) => (
            <TouchableOpacity style={styles.staffCard} onPress={() => router.push(`/employees/${item.id}`)}>
              <View style={styles.staffAvatarContainer}>
                {item.photo ? (
                  <Image source={{ uri: item.photo }} style={styles.staffAvatar} />
                ) : (
                  <View style={styles.staffAvatarPlaceholder}>
                    <Text style={styles.staffAvatarText}>
                      {item.name.substring(0, 2).toUpperCase()}
                    </Text>
                  </View>
                )}
                <View style={styles.staffStatusIndicator} />
              </View>
              <View style={styles.staffInfo}>
                <View style={styles.staffNameRow}>
                  <Text style={styles.staffName}>{item.name}</Text>
                  <View style={styles.staffRoleBadge}>
                    <Text style={styles.staffRoleBadgeText}>
                      {t(`farms.staffSection.roles.${item.role.toLowerCase()}`)}
                    </Text>
                  </View>
                </View>
                <View style={styles.staffDetailRow}>
                  <Mail size={14} color={themedColors.textSecondary} style={styles.staffDetailIcon} />
                  <Text style={styles.staffDetail}>{item.email || 'No email'}</Text>
                </View>
                <View style={styles.staffDetailRow}>
                  <Phone size={14} color={themedColors.textSecondary} style={styles.staffDetailIcon} />
                  <Text style={styles.staffDetail}>{item.phone_number || 'No phone'}</Text>
                </View>
              </View>
              <ChevronRight size={20} color={themedColors.textSecondary} />
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );
}
