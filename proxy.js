const cors_proxy = require('cors-anywhere');

const host = 'localhost';
const port = 8080;

cors_proxy.createServer({
    originWhitelist: [], // Allow all origins
    requireHeader: ['origin', 'x-requested-with'],
    removeHeaders: ['cookie', 'cookie2']
}).listen(port, host, (err) => {
    if (err) {
        console.error(`Error starting CORS proxy server: ${err.message}`);
        return;
    }
    console.log(`CORS proxy server started on http://${host}:${port}`);
});