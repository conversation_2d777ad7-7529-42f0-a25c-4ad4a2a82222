import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Modal,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { useMilkingStore } from '@/store/milking-store';
import { useAnimalStore } from '@/store/animal-store';
import { useFarmStore } from '@/store/farm-store';
import { MilkingRecord, MilkQuality } from '@/types/milking';
import { Milk, Droplets, Plus, Filter, Calendar, ChevronDown, X } from 'lucide-react-native';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import GenericDropdown from '@/components/GenericDropdown';
import { formatDate } from '@/utils/date-utils';

export default function FarmMilkingRecordsScreen() {
  const { t, language } = useTranslation();
  const router = useRouter();
  const themedColors = useThemeColors();
  const { farmId } = useLocalSearchParams<{ farmId: string }>();
  
  const {
    records,
    isLoading,
    error,
    fetchRecordsByFarm,
    clearError,
  } = useMilkingStore();
  
  const { animals, fetchAnimalsByFarm } = useAnimalStore();
  const { getFarm } = useFarmStore();

  const [filteredRecords, setFilteredRecords] = useState<MilkingRecord[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedAnimal, setSelectedAnimal] = useState<string>('all');
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedSession, setSelectedSession] = useState<string>('all');
  const [selectedQuality, setSelectedQuality] = useState<string>('all');

  const farm = farmId ? getFarm(farmId) : null;
  const styles = getStyles(themedColors, language);

  useEffect(() => {
    if (farmId) {
      fetchRecordsByFarm(farmId);
      fetchAnimalsByFarm(farmId);
    }
  }, [farmId]);

  useEffect(() => {
    applyFilters();
  }, [records, selectedAnimal, selectedMonth, selectedYear, selectedSession, selectedQuality]);

  const applyFilters = () => {
    let filtered = records;

    // Filter by animal
    if (selectedAnimal !== 'all') {
      filtered = filtered.filter(record => record.animalId === selectedAnimal);
    }

    // Filter by month and year
    filtered = filtered.filter(record => {
      const recordDate = new Date(record.date);
      return recordDate.getMonth() === selectedMonth && recordDate.getFullYear() === selectedYear;
    });

    // Filter by session
    if (selectedSession !== 'all') {
      filtered = filtered.filter(record => record.session === selectedSession);
    }

    // Filter by quality
    if (selectedQuality !== 'all') {
      filtered = filtered.filter(record => record.quality === selectedQuality);
    }

    setFilteredRecords(filtered);
  };

  const getMonthOptions = () => {
    const months = [];
    for (let i = 0; i < 12; i++) {
      const date = new Date(2024, i, 1);
      months.push({
        id: i.toString(),
        label: date.toLocaleDateString(language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-PK' : 'en-US', { month: 'long' })
      });
    }
    return months;
  };

  const getYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear; i >= currentYear - 5; i--) {
      years.push({ id: i.toString(), label: i.toString() });
    }
    return years;
  };

  const getAnimalOptions = () => {
    const farmAnimals = animals.filter(a => a.farmId === farmId);
    const options = [{ id: 'all', label: t('common.all') }];
    farmAnimals.forEach(animal => {
      options.push({ id: animal.id, label: animal.name });
    });
    return options;
  };

  const getSessionOptions = () => [
    { id: 'all', label: t('common.all') },
    { id: 'morning', label: t('milking.session.morning') },
    { id: 'afternoon', label: t('milking.session.afternoon') },
    { id: 'evening', label: t('milking.session.evening') },
  ];

  const getQualityOptions = () => [
    { id: 'all', label: t('common.all') },
    { id: MilkQuality.EXCELLENT, label: t('milking.quality.excellent') },
    { id: MilkQuality.GOOD, label: t('milking.quality.good') },
    { id: MilkQuality.FAIR, label: t('milking.quality.fair') },
    { id: MilkQuality.POOR, label: t('milking.quality.poor') },
  ];

  const getQualityColor = (quality: MilkQuality): string => {
    switch (quality) {
      case MilkQuality.EXCELLENT:
        return '#10B981';
      case MilkQuality.GOOD:
        return '#3B82F6';
      case MilkQuality.FAIR:
        return '#F59E0B';
      case MilkQuality.POOR:
        return '#EF4444';
      default:
        return themedColors.textSecondary;
    }
  };

  const getQualityLabel = (quality: MilkQuality): string => {
    return t(`milking.quality.${quality}`, { defaultValue: quality });
  };

  const getSessionLabel = (session: string): string => {
    return t(`milking.session.${session}`, { defaultValue: session });
  };

  const handleAddMilking = () => {
    router.push(`/milking/add?farmId=${farmId}`);
  };

  const clearFilters = () => {
    setSelectedAnimal('all');
    setSelectedMonth(new Date().getMonth());
    setSelectedYear(new Date().getFullYear());
    setSelectedSession('all');
    setSelectedQuality('all');
  };

  const renderMilkingRecord = ({ item }: { item: MilkingRecord }) => (
    <TouchableOpacity
      style={[styles.recordCard, language === 'ur' && styles.urduCard]}
      onPress={() => router.push(`/milking/${item.id}`)}
    >
      <View style={[styles.recordHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
        <View style={[styles.animalInfo, language === 'ur' && { alignItems: 'flex-end' }]}>
          <Text style={[styles.animalName, language === 'ur' && styles.urduText]}>
            {item.animalName}
          </Text>
          <Text style={[styles.recordDate, language === 'ur' && styles.urduText]}>
            {formatDate(item.date, language)} • {getSessionLabel(item.session)}
          </Text>
        </View>
        <View style={[
          styles.qualityBadge,
          { backgroundColor: getQualityColor(item.quality) + '20' }
        ]}>
          <Text style={[
            styles.qualityBadgeText,
            { color: getQualityColor(item.quality) },
            language === 'ur' && styles.urduText
          ]}>
            {getQualityLabel(item.quality)}
          </Text>
        </View>
      </View>
      
      <View style={[styles.recordBody, language === 'ur' && { flexDirection: 'row-reverse' }]}>
        <View style={[styles.quantityContainer, language === 'ur' && { alignItems: 'flex-end' }]}>
          <View style={[styles.quantityRow, language === 'ur' && { flexDirection: 'row-reverse' }]}>
            <Droplets size={20} color={themedColors.primary} />
            <Text style={[styles.quantityValue, language === 'ur' && styles.urduText]}>
              {item.quantity.toFixed(1)} {t('milking.liters')}
            </Text>
          </View>
        </View>
        
        {item.notes && (
          <View style={[styles.notesContainer, language === 'ur' && { alignItems: 'flex-end' }]}>
            <Text style={[styles.notesText, language === 'ur' && styles.urduText]} numberOfLines={2}>
              {item.notes}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderFiltersModal = () => (
    <Modal
      visible={showFilters}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowFilters(false)}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={[styles.modalTitle, language === 'ur' && styles.urduText]}>
            {t('common.filters')}
          </Text>
          <TouchableOpacity onPress={() => setShowFilters(false)}>
            <X size={24} color={themedColors.text} />
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.modalContent}>
          {/* Date Filters */}
          <View style={styles.filterSection}>
            <Text style={[styles.filterSectionTitle, language === 'ur' && styles.urduText]}>
              {t('common.date')}
            </Text>
            <View style={styles.dateFilters}>
              <View style={styles.dateFilter}>
                <Text style={[styles.filterLabel, language === 'ur' && styles.urduText]}>
                  {t('common.month')}
                </Text>
                <GenericDropdown
                  items={getMonthOptions()}
                  value={selectedMonth.toString()}
                  onSelect={(value) => setSelectedMonth(parseInt(value))}
                  placeholder={t('common.selectMonth')}
                />
              </View>
              <View style={styles.dateFilter}>
                <Text style={[styles.filterLabel, language === 'ur' && styles.urduText]}>
                  {t('common.year')}
                </Text>
                <GenericDropdown
                  items={getYearOptions()}
                  value={selectedYear.toString()}
                  onSelect={(value) => setSelectedYear(parseInt(value))}
                  placeholder={t('common.selectYear')}
                />
              </View>
            </View>
          </View>

          {/* Animal Filter */}
          <View style={styles.filterSection}>
            <Text style={[styles.filterSectionTitle, language === 'ur' && styles.urduText]}>
              {t('animals.animal')}
            </Text>
            <GenericDropdown
              items={getAnimalOptions()}
              value={selectedAnimal}
              onSelect={setSelectedAnimal}
              placeholder={t('animals.selectAnimal')}
            />
          </View>

          {/* Session Filter */}
          <View style={styles.filterSection}>
            <Text style={[styles.filterSectionTitle, language === 'ur' && styles.urduText]}>
              {t('milking.session')}
            </Text>
            <GenericDropdown
              items={getSessionOptions()}
              value={selectedSession}
              onSelect={setSelectedSession}
              placeholder={t('common.all')}
            />
          </View>

          {/* Quality Filter */}
          <View style={styles.filterSection}>
            <Text style={[styles.filterSectionTitle, language === 'ur' && styles.urduText]}>
              {t('milking.quality')}
            </Text>
            <GenericDropdown
              items={getQualityOptions()}
              value={selectedQuality}
              onSelect={setSelectedQuality}
              placeholder={t('common.all')}
            />
          </View>
        </ScrollView>

        <View style={styles.modalFooter}>
          <TouchableOpacity
            style={[styles.clearButton, { marginRight: language === 'ur' ? 0 : 12, marginLeft: language === 'ur' ? 12 : 0 }]}
            onPress={clearFilters}
          >
            <Text style={[styles.clearButtonText, language === 'ur' && styles.urduText]}>
              {t('common.clear')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.applyButton}
            onPress={() => setShowFilters(false)}
          >
            <Text style={[styles.applyButtonText, language === 'ur' && styles.urduText]}>
              {t('common.apply')}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );

  if (isLoading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  if (error) {
    const isIndexError = error.includes('index') || error.includes('composite') || error.includes('query requires');
    
    if (isIndexError) {
      return (
        <SafeAreaView style={styles.container} edges={['bottom']}>
          <Stack.Screen
            options={{
              title: farm?.name || t('milking.title'),
              headerBackTitle: t('common.back'),
            }}
          />
          <View style={styles.emptyContainer}>
            <EmptyState
              title={t('milking.noRecords')}
              message={t('milking.addYourFirstRecord')}
              actionLabel={t('milking.addRecord')}
              onAction={handleAddMilking}
              icon={<Milk size={48} color={themedColors.primary} />}
            />
          </View>
        </SafeAreaView>
      );
    }
    
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <Stack.Screen
          options={{
            title: farm?.name || t('milking.title'),
            headerBackTitle: t('common.back'),
          }}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              clearError();
              if (farmId) {
                fetchRecordsByFarm(farmId);
              }
            }}
          >
            <Text style={styles.retryButtonText}>{t('common.retry')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: farm?.name || t('milking.title'),
          headerBackTitle: t('common.back'),
          headerRight: () => (
            <TouchableOpacity onPress={() => setShowFilters(true)}>
              <Filter size={24} color={themedColors.primary} />
            </TouchableOpacity>
          ),
        }}
      />

      <View style={styles.content}>
        {/* Stats Header */}
        <View style={styles.statsHeader}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, language === 'ur' && styles.urduText]}>
              {filteredRecords.length}
            </Text>
            <Text style={[styles.statLabel, language === 'ur' && styles.urduText]}>
              {t('milking.records')}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, language === 'ur' && styles.urduText]}>
              {filteredRecords.reduce((sum, record) => sum + record.quantity, 0).toFixed(1)}L
            </Text>
            <Text style={[styles.statLabel, language === 'ur' && styles.urduText]}>
              {t('milking.totalProduction')}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, language === 'ur' && styles.urduText]}>
              {filteredRecords.length > 0 ? (filteredRecords.reduce((sum, record) => sum + record.quantity, 0) / filteredRecords.length).toFixed(1) : '0.0'}L
            </Text>
            <Text style={[styles.statLabel, language === 'ur' && styles.urduText]}>
              {t('milking.averagePerRecord')}
            </Text>
          </View>
        </View>

        {/* Active Filters Display */}
        {(selectedAnimal !== 'all' || selectedSession !== 'all' || selectedQuality !== 'all') && (
          <View style={styles.activeFilters}>
            <Text style={[styles.activeFiltersTitle, language === 'ur' && styles.urduText]}>
              {t('common.activeFilters')}:
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={[styles.filterTags, language === 'ur' && { flexDirection: 'row-reverse' }]}>
                {selectedAnimal !== 'all' && (
                  <View style={styles.filterTag}>
                    <Text style={[styles.filterTagText, language === 'ur' && styles.urduText]}>
                      {animals.find(a => a.id === selectedAnimal)?.name || selectedAnimal}
                    </Text>
                  </View>
                )}
                {selectedSession !== 'all' && (
                  <View style={styles.filterTag}>
                    <Text style={[styles.filterTagText, language === 'ur' && styles.urduText]}>
                      {getSessionLabel(selectedSession)}
                    </Text>
                  </View>
                )}
                {selectedQuality !== 'all' && (
                  <View style={styles.filterTag}>
                    <Text style={[styles.filterTagText, language === 'ur' && styles.urduText]}>
                      {getQualityLabel(selectedQuality as MilkQuality)}
                    </Text>
                  </View>
                )}
              </View>
            </ScrollView>
          </View>
        )}

        {/* Records List */}
        {filteredRecords.length === 0 ? (
          <View style={styles.emptyContainer}>
            <EmptyState
              title={t('milking.noRecordsForFilter')}
              message={t('milking.tryDifferentFilters')}
              actionLabel={t('milking.addRecord')}
              onAction={handleAddMilking}
              icon={<Milk size={48} color={themedColors.primary} />}
            />
          </View>
        ) : (
          <FlatList
            data={filteredRecords}
            keyExtractor={item => item.id}
            renderItem={renderMilkingRecord}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>

      {/* Add Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={handleAddMilking}
      >
        <Plus size={24} color="white" />
      </TouchableOpacity>

      {/* Filters Modal */}
      {renderFiltersModal()}
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: themedColors.background,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    statsHeader: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      justifyContent: 'space-around',
      backgroundColor: themedColors.card,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: themedColors.border,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: 20,
      fontWeight: 'bold',
      color: themedColors.primary,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 11,
      color: themedColors.textSecondary,
      textAlign: 'center',
    },
    activeFilters: {
      marginBottom: 16,
    },
    activeFiltersTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: themedColors.text,
      marginBottom: 8,
    },
    filterTags: {
      flexDirection: 'row',
      gap: 8,
    },
    filterTag: {
      backgroundColor: themedColors.primary + '20',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: themedColors.primary + '40',
    },
    filterTagText: {
      fontSize: 12,
      color: themedColors.primary,
      fontWeight: '500',
    },
    recordCard: {
      backgroundColor: themedColors.card,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: themedColors.border,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    urduCard: {
      alignItems: 'flex-end',
    },
    recordHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    animalInfo: {
      flex: 1,
    },
    animalName: {
      fontSize: 16,
      fontWeight: 'bold',
      color: themedColors.text,
      marginBottom: 4,
    },
    recordDate: {
      fontSize: 14,
      color: themedColors.textSecondary,
    },
    qualityBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    qualityBadgeText: {
      fontSize: 12,
      fontWeight: '600',
    },
    recordBody: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    quantityContainer: {
      flex: 1,
    },
    quantityRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    quantityValue: {
      fontSize: 16,
      fontWeight: '600',
      color: themedColors.text,
    },
    notesContainer: {
      flex: 2,
      marginLeft: language === 'ur' ? 0 : 16,
      marginRight: language === 'ur' ? 16 : 0,
    },
    notesText: {
      fontSize: 14,
      color: themedColors.textSecondary,
      fontStyle: 'italic',
    },
    listContent: {
      paddingBottom: 80,
    },
    addButton: {
      position: 'absolute',
      bottom: 20,
      right: 20,
      backgroundColor: themedColors.primary,
      width: 56,
      height: 56,
      borderRadius: 28,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorText: {
      fontSize: 16,
      color: themedColors.error,
      textAlign: 'center',
      marginBottom: 20,
    },
    retryButton: {
      backgroundColor: themedColors.primary,
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 8,
    },
    retryButtonText: {
      color: 'white',
      fontWeight: '600',
    },
    urduText: {
      textAlign: 'right',
    },
    // Modal Styles
    modalContainer: {
      flex: 1,
      backgroundColor: themedColors.background,
    },
    modalHeader: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: themedColors.border,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: themedColors.text,
    },
    modalContent: {
      flex: 1,
      padding: 16,
    },
    filterSection: {
      marginBottom: 24,
    },
    filterSectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: themedColors.text,
      marginBottom: 12,
    },
    filterLabel: {
      fontSize: 14,
      color: themedColors.textSecondary,
      marginBottom: 8,
    },
    dateFilters: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      gap: 12,
    },
    dateFilter: {
      flex: 1,
    },
    modalFooter: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      padding: 16,
      borderTopWidth: 1,
      borderTopColor: themedColors.border,
    },
    clearButton: {
      flex: 1,
      backgroundColor: themedColors.background,
      borderWidth: 1,
      borderColor: themedColors.border,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    clearButtonText: {
      color: themedColors.text,
      fontWeight: '600',
    },
    applyButton: {
      flex: 1,
      backgroundColor: themedColors.primary,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    applyButtonText: {
      color: 'white',
      fontWeight: '600',
    },
  });
