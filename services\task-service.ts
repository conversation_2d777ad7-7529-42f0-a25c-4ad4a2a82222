import { firestore } from '@/config/firebase';
import { collection, doc, addDoc, updateDoc, deleteDoc, getDoc, getDocs, query, where, orderBy } from 'firebase/firestore';
import { Task, TaskStatus, TaskRecurrence } from '@/types/task';

/**
 * Add a new task
 */
export const addTask = async (taskData: Partial<Task>): Promise<string> => {
  try {
    const taskRef = await addDoc(collection(firestore, 'tasks'), {
      ...taskData,
      created_at: new Date(),
      updated_at: new Date(),
      status: taskData.status || 'pending'
    });
    
    return taskRef.id;
  } catch (error) {
    console.error('Error adding task:', error);
    throw error;
  }
};

/**
 * Update an existing task
 */
export const updateTask = async (taskId: string, updates: Partial<Task>): Promise<void> => {
  try {
    const taskRef = doc(firestore, 'tasks', taskId);
    
    // Get the current task data
    const taskDoc = await getDoc(taskRef);
    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }
    
    const currentTask = { id: taskDoc.id, ...taskDoc.data() } as Task;
    
    // Update the task
    await updateDoc(taskRef, {
      ...updates,
      updated_at: new Date()
    });
    
    // If task is being marked as completed and it's a recurring task, create the next occurrence
    if (
      updates.status === TaskStatus.COMPLETED && 
      currentTask.recurrence !== TaskRecurrence.NONE
    ) {
      await createNextRecurringTask(currentTask);
    }
    
  } catch (error) {
    console.error('Error updating task:', error);
    throw error;
  }
};

/**
 * Delete a task
 */
export const deleteTask = async (taskId: string): Promise<void> => {
  try {
    await deleteDoc(doc(firestore, 'tasks', taskId));
  } catch (error) {
    console.error('Error deleting task:', error);
    throw error;
  }
};

/**
 * Get tasks based on user role and ID
 * - Owners see tasks for their farms only
 * - Admins see tasks for farms they're assigned to
 * - Caretakers only see tasks assigned to them
 */
export const getTasks = async (userId: string, userRole: string): Promise<Task[]> => {
  try {

    let q;

    // Owners and admins see tasks for their farms only
    if (userRole === 'owner' || userRole === 'admin') {
      // Filter tasks by assignedBy (tasks created by this user)
      // This ensures users only see tasks they created or are responsible for
      q = query(
        collection(firestore, 'tasks'),
        where('assignedBy', '==', userId)
      );
    } else {
      // Caretakers only see tasks assigned to them
      q = query(
        collection(firestore, 'tasks'),
        where('assignedTo', '==', userId)
      );
    }

    const querySnapshot = await getDocs(q);

    const tasks: Task[] = [];

    querySnapshot.forEach((doc) => {
      tasks.push({ id: doc.id, ...doc.data() } as Task);
    });
    
    // Sort the tasks by due date
    tasks.sort((a, b) => {
      const dateA = a.due_date instanceof Date ? a.due_date.getTime() : a.due_date;
      const dateB = b.due_date instanceof Date ? b.due_date.getTime() : b.due_date;
      return dateA - dateB;
    });
    
    return tasks;
  } catch (error) {
    console.error('Error getting tasks:', error);
    throw error;
  }
};

/**
 * Create the next occurrence of a recurring task
 */
const createNextRecurringTask = async (completedTask: Task): Promise<void> => {
  try {
    // Calculate the next due date based on recurrence type
    const nextDueDate = calculateNextDueDate(
      completedTask.due_date,
      completedTask.recurrence
    );
    
    // Create a new task with the same details but updated due date
    const newTaskData: Partial<Task> = {
      title: completedTask.title,
      description: completedTask.description,
      farmId: completedTask.farmId,
      farmName: completedTask.farmName,
      assignedTo: completedTask.assignedTo,
      assigneeName: completedTask.assigneeName,
      assignedBy: completedTask.assignedBy,
      assignedByName: completedTask.assignedByName,
      due_date: nextDueDate,
      priority: completedTask.priority,
      status: TaskStatus.PENDING,
      notes: completedTask.notes,
      recurrence: completedTask.recurrence,
      parent_task_id: completedTask.id,
      related_animal_id: completedTask.related_animal_id,
      related_animal_name: completedTask.related_animal_name,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    await addTask(newTaskData);
    
  } catch (error) {
    console.error('Error creating next recurring task:', error);
    throw error;
  }
};

/**
 * Calculate the next due date based on recurrence type
 */
const calculateNextDueDate = (currentDueDate: number, recurrence: TaskRecurrence): number => {
  const currentDate = new Date(currentDueDate);
  
  switch (recurrence) {
    case TaskRecurrence.DAILY:
      // Add 1 day
      return currentDate.setDate(currentDate.getDate() + 1);
      
    case TaskRecurrence.WEEKLY:
      // Add 7 days
      return currentDate.setDate(currentDate.getDate() + 7);
      
    case TaskRecurrence.MONTHLY:
      // Add 1 month (handle month rollover)
      const nextMonth = currentDate.getMonth() + 1;
      return currentDate.setMonth(nextMonth);
      
    default:
      // Should not happen, but return current date + 1 day as fallback
      return currentDate.setDate(currentDate.getDate() + 1);
  }
};


