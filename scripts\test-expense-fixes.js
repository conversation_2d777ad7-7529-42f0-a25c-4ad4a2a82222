/**
 * Test script to verify expense fixes
 * Run this with: node scripts/test-expense-fixes.js
 */

console.log('Testing expense fixes...');

// Test 1: Check if error boundary component exists
const fs = require('fs');
const path = require('path');

const errorBoundaryPath = path.join(__dirname, '../components/ExpenseErrorBoundary.tsx');
if (fs.existsSync(errorBoundaryPath)) {
  console.log('✅ ExpenseErrorBoundary component exists');
} else {
  console.log('❌ ExpenseErrorBoundary component missing');
}

// Test 2: Check if expenses.tsx has error handling
const expensesPath = path.join(__dirname, '../app/(tabs)/expenses.tsx');
if (fs.existsSync(expensesPath)) {
  const expensesContent = fs.readFileSync(expensesPath, 'utf8');
  
  if (expensesContent.includes('ExpenseErrorBoundary')) {
    console.log('✅ Expenses screen wrapped with error boundary');
  } else {
    console.log('❌ Expenses screen not wrapped with error boundary');
  }
  
  if (expensesContent.includes('useFocusEffect')) {
    console.log('✅ Expenses screen uses useFocusEffect for better lifecycle management');
  } else {
    console.log('❌ Expenses screen missing useFocusEffect');
  }
  
  if (expensesContent.includes('safeAsyncOperation')) {
    console.log('✅ Expenses screen has safe async operation wrapper');
  } else {
    console.log('❌ Expenses screen missing safe async operation wrapper');
  }
} else {
  console.log('❌ Expenses screen file missing');
}

// Test 3: Check if expense store has caching
const storeePath = path.join(__dirname, '../store/expense-store.ts');
if (fs.existsSync(storeePath)) {
  const storeContent = fs.readFileSync(storeePath, 'utf8');
  
  if (storeContent.includes('lastFetchTime') && storeContent.includes('cacheTimeout')) {
    console.log('✅ Expense store has caching mechanism');
  } else {
    console.log('❌ Expense store missing caching mechanism');
  }
  
  if (storeContent.includes('clearExpenses')) {
    console.log('✅ Expense store has clear function');
  } else {
    console.log('❌ Expense store missing clear function');
  }
  
  if (storeContent.includes('Promise.race') && storeContent.includes('timeout')) {
    console.log('✅ Expense store has timeout protection');
  } else {
    console.log('❌ Expense store missing timeout protection');
  }
} else {
  console.log('❌ Expense store file missing');
}

// Test 4: Check if translations are added
const translationsPath = path.join(__dirname, '../translations/en.json');
if (fs.existsSync(translationsPath)) {
  const translationsContent = fs.readFileSync(translationsPath, 'utf8');
  
  if (translationsContent.includes('errorTitle') && translationsContent.includes('loadError')) {
    console.log('✅ Error translations added');
  } else {
    console.log('❌ Error translations missing');
  }
} else {
  console.log('❌ Translations file missing');
}

console.log('\nTest completed. If all tests pass, the expense crash fixes should be working.');
console.log('Please test on your device to confirm the fixes work properly.');
