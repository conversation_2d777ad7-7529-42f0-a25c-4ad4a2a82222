import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

interface DebugInfoProps {
  title: string;
  data: any;
}

const DebugInfo: React.FC<DebugInfoProps> = ({ title, data }) => {
  const themedColors = useThemeColors();
  
  return (
    <View style={[styles.container, { backgroundColor: themedColors.card, borderColor: themedColors.border }]}>
      <Text style={[styles.title, { color: themedColors.text }]}>{title}</Text>
      <Text style={[styles.data, { color: themedColors.textSecondary }]}>
        {JSON.stringify(data, null, 2)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 10,
    padding: 10,
    borderWidth: 1,
    borderRadius: 5,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  data: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
});

export default DebugInfo;
