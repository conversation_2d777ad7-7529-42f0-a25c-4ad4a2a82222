import React, { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ImageBackground,
  Image,
  TextInput,
} from 'react-native';
import { useRouter, Redirect } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import Button from '@/components/Button';
import { useAuthStore } from '@/store/auth-store';
import { Mail, Lock, ArrowLeft, AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemeColors } from '@/hooks/useThemeColors';

export default function LoginScreen() {
  const router = useRouter();
  const themedColors = useThemeColors();
  const {
    login,
    loginWithGoogle,
    resetPassword,
    sendEmailVerification,
    error,
    isLoading,
    isAuthenticated,
    user,
    checkAuthState,
  } = useAuthStore();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [validationErrors, setValidationErrors] = useState<{
    email?: string;
    password?: string;
    resetEmail?: string;
  }>({});
  const [showVerificationPrompt, setShowVerificationPrompt] = useState(false);

  // Check auth state on mount
  useEffect(() => {
    console.log('Checking auth state...');
    checkAuthState();
  }, []);

  // Check if user exists but email is not verified
  useEffect(() => {
    if (user && !user.emailVerified && error?.includes('verify your email')) {
      setShowVerificationPrompt(true);
    } else {
      setShowVerificationPrompt(false);
    }
  }, [user, error]);

  // Redirect if already authenticated
  if (isAuthenticated) {
    console.log('User is authenticated, redirecting to tabs');
    return <Redirect href="/(tabs)" />;
  }

  const validateForm = () => {
    const errors: {
      email?: string;
      password?: string;
    } = {};

    if (!email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'Email is invalid';
    }

    if (!password) {
      errors.password = 'Password is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateResetEmail = () => {
    const errors: {
      resetEmail?: string;
    } = {};

    if (!resetEmail) {
      errors.resetEmail = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(resetEmail)) {
      errors.resetEmail = 'Email is invalid';
    }

    setValidationErrors(prev => ({ ...prev, ...errors }));
    return Object.keys(errors).length === 0;
  };

  const handleLogin = async () => {
    if (validateForm()) {
      try {
        console.log('Logging in with:', email);

        // Store the email and password securely for session restoration when adding users
        // This is needed because Firebase automatically signs in as the newly created user
        await AsyncStorage.setItem('ownerEmail', email);
        await AsyncStorage.setItem('ownerPassword', password);
        console.log('Stored owner credentials for session restoration');

        await login(email, password);
      } catch (err) {
        console.error('Login error:', err);
        // Error is already handled in the store
      }
    }
  };

  const handleGoogleLogin = async () => {
    try {
      console.log('Attempting Google login');
      await loginWithGoogle();
    } catch (err) {
      console.error('Google login error:', err);
      // Error is already handled in the store
    }
  };

  const handleRegister = () => {
    router.push('/(auth)/register');
  };

  const handleBack = () => {
    if (showResetPassword) {
      setShowResetPassword(false);
    } else {
      router.back();
    }
  };

  const handleForgotPassword = () => {
    setShowResetPassword(true);
    setResetEmail(email);
  };

  const handleResetPassword = async () => {
    if (validateResetEmail()) {
      try {
        console.log('Resetting password for:', resetEmail);
        await resetPassword(resetEmail);
        setSuccessMessage('Password reset email sent. Please check your inbox.');
        setTimeout(() => {
          setShowResetPassword(false);
        }, 3000);
      } catch (err) {
        console.error('Reset password error:', err);
        // Error is already handled in the store
      }
    }
  };

  const handleResendVerification = async () => {
    try {
      await sendEmailVerification();
      setSuccessMessage('Verification email sent. Please check your inbox.');
      setShowVerificationPrompt(false);
    } catch (err) {
      console.error('Error sending verification email:', err);
    }
  };

  return (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1470252649378-9c29740c9fa8?q=80&w=2070&auto=format&fit=crop' }}
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      <LinearGradient
        colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0.85)']}
        style={styles.gradient}
      >
        <SafeAreaView style={styles.container} edges={['bottom']}>
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
          >
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              keyboardShouldPersistTaps="handled"
            >
              <TouchableOpacity style={styles.backButton} onPress={handleBack}>
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>

              <View style={styles.logoContainer}>
                <Image
                  source={require('../../assets/images/ll.png')}
                  style={styles.logoImage}
                />
                <Text style={styles.logoText}>Livestock Tracker</Text>
              </View>

              <View style={styles.header}>
                <Text style={styles.title}>
                  {showResetPassword ? 'Reset Password' : 'Welcome Back'}
                </Text>
                <Text style={styles.subtitle}>
                  {showResetPassword
                    ? 'Enter your email to receive a reset link'
                    : 'Sign in to manage your livestock'
                  }
                </Text>
              </View>

              {successMessage ? (
                <View style={styles.successContainer}>
                  <CheckCircle size={20} color={colors.success} />
                  <Text style={styles.successText}>{successMessage}</Text>
                </View>
              ) : error && !showVerificationPrompt ? (
                <View style={styles.errorContainer}>
                  <AlertCircle size={20} color={colors.error} />
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              ) : null}

              {showVerificationPrompt && (
                <View style={styles.verificationContainer}>
                  <AlertCircle size={20} color="#FFD700" />
                  <Text style={styles.verificationText}>
                    Please verify your email to continue. Check your inbox for a verification link.
                  </Text>
                  <TouchableOpacity
                    style={styles.resendButton}
                    onPress={handleResendVerification}
                  >
                    <Text style={styles.resendText}>Resend verification email</Text>
                  </TouchableOpacity>
                </View>
              )}

              <View style={styles.form}>
                {showResetPassword ? (
                  <>
                    <CustomInput
                      label="Email"
                      placeholder="Enter your email"
                      value={resetEmail}
                      onChangeText={setResetEmail}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      error={validationErrors.resetEmail}
                      leftIcon={<Mail size={20} color="#A78B71" />}
                      darkMode
                    />

                    <Button
                      title="Send Reset Link"
                      onPress={handleResetPassword}
                      variant="primary"
                      size="large"
                      isLoading={isLoading}
                      style={styles.button}
                    />
                  </>
                ) : (
                  <>
                    <CustomInput
                      label="Email"
                      placeholder="Enter your email"
                      value={email}
                      onChangeText={setEmail}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      error={validationErrors.email}
                      leftIcon={<Mail size={20} color="#A78B71" />}
                      darkMode
                    />

                    <CustomInput
                      label="Password"
                      placeholder="Enter your password"
                      value={password}
                      onChangeText={setPassword}
                      secureTextEntry
                      isPassword
                      error={validationErrors.password}
                      leftIcon={<Lock size={20} color="#A78B71" />}
                      darkMode
                    />

                    <TouchableOpacity
                      style={styles.forgotPassword}
                      onPress={handleForgotPassword}
                    >
                      <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
                    </TouchableOpacity>

                    <Button
                      title="Sign In"
                      onPress={handleLogin}
                      variant="primary"
                      size="large"
                      isLoading={isLoading}
                      style={styles.button}
                    />

                    <View style={styles.orContainer}>
                      <View style={styles.orLine} />
                      <Text style={styles.orText}>Or continue with</Text>
                      <View style={styles.orLine} />
                    </View>

                    <TouchableOpacity 
                      style={styles.googleButton}
                      onPress={handleGoogleLogin}
                      disabled={isLoading}
                    >
                      <Image
                        source={require('../../assets/images/google-icon.png')}
                        style={styles.googleIcon}
                      />
                      <Text style={styles.googleButtonText}>Sign in with Google</Text>
                    </TouchableOpacity>
                  </>
                )}
              </View>

              {!showResetPassword && (
                <View style={styles.footer}>
                  <Text style={styles.footerText}>Don't have an account?</Text>
                  <TouchableOpacity onPress={handleRegister}>
                    <Text style={styles.footerLink}>Sign Up</Text>
                  </TouchableOpacity>
                </View>
              )}
            </ScrollView>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </LinearGradient>
    </ImageBackground>
  );
}

// Input component for this screen only
const CustomInput = ({ label, leftIcon, darkMode = false, error, isPassword = false, secureTextEntry, ...props }: any) => {
  const [showPassword, setShowPassword] = useState(false);
  
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  return (
    <View style={inputStyles.container}>
      {label && <Text style={[inputStyles.label, darkMode && inputStyles.labelDark]}>{label}</Text>}
      <View style={[inputStyles.inputContainer, darkMode && inputStyles.inputContainerDark]}>
        {leftIcon && <View style={inputStyles.iconContainer}>{leftIcon}</View>}
        <TextInput
          style={[
            inputStyles.input,
            leftIcon && inputStyles.inputWithIcon,
            (isPassword || props.rightIcon) && inputStyles.inputWithRightIcon,
            darkMode && inputStyles.inputDark
          ]}
          placeholderTextColor={darkMode ? "#A78B71" : colors.textSecondary}
          secureTextEntry={isPassword ? !showPassword : secureTextEntry}
          {...props}
        />
        {isPassword && (
          <TouchableOpacity
            style={inputStyles.eyeIconContainer}
            onPress={togglePasswordVisibility}
          >
            {showPassword ? (
              <EyeOff size={20} color={darkMode ? "#A78B71" : colors.textSecondary} />
            ) : (
              <Eye size={20} color={darkMode ? "#A78B71" : colors.textSecondary} />
            )}
          </TouchableOpacity>
        )}
        {props.rightIcon && !isPassword && (
          <View style={inputStyles.iconContainer}>{props.rightIcon}</View>
        )}
      </View>
      {error && <Text style={[inputStyles.errorText, darkMode && inputStyles.errorTextDark]}>{error}</Text>}
    </View>
  );
};

const inputStyles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  labelDark: {
    color: '#fff',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  inputContainerDark: {
    borderColor: 'rgba(167, 139, 113, 0.5)',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  iconContainer: {
    paddingLeft: 16,
  },
  eyeIconContainer: {
    paddingHorizontal: 16,
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
    paddingHorizontal: 16,
    fontSize: 16,
    color: colors.text,
  },
  inputDark: {
    color: '#fff',
  },
  inputWithIcon: {
    paddingLeft: 8,
  },
  inputWithRightIcon: {
    paddingRight: 40,
  },
  errorText: {
    color: colors.error,
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  errorTextDark: {
    color: '#FF6B6B',
  },
});

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  gradient: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  backButton: {
    marginBottom: 16,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#A78B71',
  },
  logoText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 12,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#E0E0E0',
    textAlign: 'center',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(254, 202, 202, 0.8)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    gap: 8,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    flex: 1,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(209, 250, 229, 0.8)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    gap: 8,
  },
  successText: {
    color: colors.success,
    fontSize: 14,
    flex: 1,
  },
  verificationContainer: {
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.5)',
  },
  verificationText: {
    color: '#FFD700',
    fontSize: 14,
    marginTop: 8,
    marginBottom: 8,
  },
  resendButton: {
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  resendText: {
    color: '#A78B71',
    fontSize: 14,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  form: {
    marginBottom: 24,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(167, 139, 113, 0.3)',
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    color: '#A78B71',
    fontSize: 14,
  },
  button: {
    marginBottom: 24,
    backgroundColor: '#A78B71',
  },
  orContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  orLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(167, 139, 113, 0.5)',
  },
  orText: {
    color: '#A78B71',
    marginHorizontal: 16,
    fontSize: 14,
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 4,
    padding: 12,
    marginBottom: 24,
    elevation: 1,
  },
  googleIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  googleButtonText: {
    color: '#757575',
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
    gap: 4,
  },
  footerText: {
    color: '#E0E0E0',
    fontSize: 14,
  },
  footerLink: {
    color: '#A78B71',
    fontSize: 14,
    fontWeight: '600',
  },
});






