#!/bin/bash

# Install dependencies
npm install --legacy-peer-deps

# Ensure the app icon is properly set
echo "Verifying app icon configuration..."
ls -la ./assets/images/

# Copy the icon to a backup location to ensure it's available
cp ./assets/images/ll.png ./assets/icon-backup.png

# Update app.json to use the backup icon if needed
sed -i 's|"./assets/images/ll.png"|"./assets/icon-backup.png"|g' ./app.json

echo "App icon configuration updated."

exit 0