import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image as RNImage,
} from 'react-native';
import { useLocalSearchPara<PERSON>, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAnimalStore } from '@/store/animal-store';
import { useTranslation } from '@/hooks/useTranslation';
import { useToast } from '@/contexts/ToastContext';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import Input from '@/components/Input';
import Button from '@/components/Button';
import DatePickerInput from '@/components/DatePickerInput';
import BreedDropdown from '@/components/BreedDropdown';
import { Save, ArrowLeft, Camera, Image, Plus, CheckCircle2, Scale, Tag, Calendar } from 'lucide-react-native';
import LoadingIndicator from '@/components/LoadingIndicator';
import { Animal } from '@/types/animal';
import { useFarmStore } from '@/store/farm-store';
import GenericDropdown from '@/components/GenericDropdown';
import { breedsBySpecies } from '@/constants/breeds';

const getBreedOptions = (species: string): string[] => {
  const options = breedsBySpecies[species] || [];
  return options.map(option => option.value);
};

const getGenderImages = (species: string) => {
  switch (species) {
    case 'Cow':
      return {
        male: 'https://img.pikbest.com/png-images/20241011/a-buffalo-skull-head-is-isolated-on-a-transparent-background_10946913.png!w700wp',
        female: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
      };
    case 'Goat':
      return {
        male: 'https://img.freepik.com/free-psd/close-up-goats-isolated_23-2151840080.jpg?ga=GA1.1.1740816856.1740755798&semt=ais_hybrid&w=740',
        female: 'https://images.unsplash.com/photo-1533318087102-b3ad366ed041?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
      };
    case 'Poultry':
      return {
        male: 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        female: 'https://images.unsplash.com/photo-1548550023-2bdb3c5beed7?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
      };
    case 'Fish':
      return {
        male: 'https://images.unsplash.com/photo-1524704654690-b56c05c78a00?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        female: 'https://images.unsplash.com/photo-1524704654690-b56c05c78a00?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
      };
    default:
      return {
        male: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        female: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
      };
  }
};

const SpeciesCard = ({ species, isSelected, onPress, imageUrl }) => (
  <TouchableOpacity 
    style={[styles.speciesCard, isSelected && styles.speciesCardSelected]} 
    onPress={onPress}
  >
    <View style={[styles.imageContainer, isSelected && styles.selectedImageContainer]}>
      <RNImage 
        source={{ uri: imageUrl }} 
        style={styles.speciesImage} 
        resizeMode="cover"
      />
    </View>
    <Text style={[styles.speciesText, isSelected && styles.speciesTextSelected]}>
      {species}
    </Text>
  </TouchableOpacity>
);

const GenderCard = ({ gender, isSelected, onPress, imageUrl }) => (
  <TouchableOpacity 
    style={[styles.genderCard, isSelected && styles.genderCardSelected]} 
    onPress={onPress}
  >
    <RNImage 
      source={{ uri: imageUrl }} 
      style={styles.genderImage} 
      resizeMode="cover"
    />
    <Text style={[styles.genderText, isSelected && styles.genderTextSelected]}>
      {gender}
    </Text>
  </TouchableOpacity>
);

export default function EditAnimalScreen() {
  const { t, language } = useTranslation();
  const { id, initialFarmId } = useLocalSearchParams<{ id: string; initialFarmId: string }>();
  const router = useRouter();
  const { getAnimal, updateAnimal, isLoading } = useAnimalStore();
  const { farms } = useFarmStore();
  const { showToast } = useToast();
  const { playFeedback } = useAudioFeedback();

  // Add animal state
  const [animal, setAnimal] = useState<Animal | null>(null);
  const [farmId, setFarmId] = useState(initialFarmId);
  const [name, setName] = useState('');
  const [species, setSpecies] = useState('');
  const [breed, setBreed] = useState('');
  const [age, setAge] = useState('');
  const [weight, setWeight] = useState('');
  const [gender, setGender] = useState('');
  const [tagId, setTagId] = useState('');
  const [birthDate, setBirthDate] = useState(new Date());
  const [imageUri, setImageUri] = useState<string | null>(null);
  
  const [errors, setErrors] = useState({
    name: '',
    species: '',
    breed: '',
    age: '',
    weight: '',
    tagId: '',
    farmId: '',
  });

  useEffect(() => {
    const loadAnimal = async () => {
      const animalData = await getAnimal(id);
      if (animalData) {
        setAnimal(animalData);
        setName(animalData.name);
        setSpecies(animalData.species);
        setBreed(animalData.breed);
        setAge(animalData.age.toString());
        setWeight(animalData.weight.toString());
        setGender(animalData.gender);
        setTagId(animalData.tagId);
        setBirthDate(new Date(animalData.birthDate));
        setImageUri(animalData.imageUri);
        setFarmId(animalData.farmId);
      }
    };

    loadAnimal();
  }, [id, getAnimal]);

  if (isLoading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  if (!animal) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{t('animals.notFound')}</Text>
      </View>
    );
  }

  // Convert farms to dropdown items format
  const farmItems = farms.map(farm => ({
    id: farm.id,
    label: farm.name,
    description: farm.location
  }));

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: '',
      species: '',
      breed: '',
      age: '',
      weight: '',
      tagId: '',
      farmId: '',
    };

    if (!name.trim()) {
      newErrors.name = t('validation.required');
      isValid = false;
    }

    if (!species) {
      newErrors.species = t('validation.required');
      isValid = false;
    }

    if (!breed) {
      newErrors.breed = t('validation.required');
      isValid = false;
    }

    if (!age.trim()) {
      newErrors.age = t('validation.required');
      isValid = false;
    }

    if (!weight.trim()) {
      newErrors.weight = t('validation.required');
      isValid = false;
    }

    if (!tagId.trim()) {
      newErrors.tagId = t('validation.required');
      isValid = false;
    }

    if (!farmId) {
      newErrors.farmId = t('validation.required');
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      playFeedback('error');
      showToast(t('validation.checkFields'), 'error');
      return;
    }

    try {
      await updateAnimal(id, {
        name: name.trim(),
        species: species.trim(),
        breed: breed.trim(),
        age: Number(age),
        weight: Number(weight),
        gender,
        tagId: tagId.trim(),
        birthDate: birthDate.getTime(),
        imageUri,
        farmId, // Use farmId from URL params
      });

      playFeedback('success');
      showToast(t('animals.updateSuccess'), 'success');
      router.back();
    } catch (error) {
      console.error('Error updating animal:', error);
      playFeedback('error');
      showToast(t('animals.updateError'), 'error');
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: t('animals.editAnimal'),
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color={colors.text} />
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView style={styles.scrollView}>
        <View style={styles.form}>
          {/* Image Section */}
          <View style={styles.imageSection}>
            {imageUri ? (
              <View style={styles.imagePreviewContainer}>
                <RNImage
                  source={{ uri: imageUri }}
                  style={styles.imagePreview}
                  resizeMode="cover"
                />
                <View style={styles.successIndicator}>
                  <CheckCircle2 size={24} color={colors.success} />
                  <Text style={[styles.successText, language === 'ur' ? styles.urduText : null]}>
                    {t('animals.photoAdded')}
                  </Text>
                </View>
              </View>
            ) : (
              <View style={styles.imagePlaceholder}>
                {species === 'Cow' && (
                  <RNImage
                    source={{
                      uri: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                    }}
                    style={styles.animalSilhouette}
                  />
                )}
              </View>
            )}
            <View style={styles.imageButtonsContainer}>
              <TouchableOpacity style={styles.cameraButton}>
                <View style={styles.buttonIconContainer}>
                  <Camera size={24} color="white" />
                </View>
                <Text style={styles.cameraButtonText}>{t('animals.takePhoto')}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.galleryButton}>
                <View style={styles.buttonIconContainer}>
                  <Image size={24} color="white" />
                </View>
                <Text style={styles.galleryButtonText}>{t('animals.choosePhoto')}</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formSection}>            
            <Input
              label={t('animals.name')}
              value={name}
              onChangeText={setName}
              error={errors.name}
              placeholder={t('animals.namePlaceholder')}
            />

            {/* Species Section */}
            <View style={styles.section}>
              <Text style={[styles.sectionLabel, language === 'ur' ? styles.urduText : null]}>
                {t('animals.species')}*
              </Text>
              <View style={styles.speciesContainer}>
                <View style={styles.speciesRow}>
                  <SpeciesCard
                    species="Cow"
                    isSelected={species === 'Cow'}
                    onPress={() => setSpecies('Cow')}
                    imageUrl="https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  />
                  <SpeciesCard
                    species="Goat"
                    isSelected={species === 'Goat'}
                    onPress={() => setSpecies('Goat')}
                    imageUrl="https://images.unsplash.com/photo-1533318087102-b3ad366ed041?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  />
                </View>
                <View style={styles.speciesRow}>
                  <SpeciesCard
                    species="Poultry"
                    isSelected={species === 'Poultry'}
                    onPress={() => setSpecies('Poultry')}
                    imageUrl="https://images.unsplash.com/photo-1548550023-2bdb3c5beed7?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  />
                  <SpeciesCard
                    species="Fish"
                    isSelected={species === 'Fish'}
                    onPress={() => setSpecies('Fish')}
                    imageUrl="https://images.unsplash.com/photo-1524704654690-b56c05c78a00?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  />
                </View>
              </View>
              {errors.species && (
                <Text style={styles.errorText}>{errors.species}</Text>
              )}
            </View>

            <BreedDropdown
            label={`${t('animals.breed')}*`}
              value={breed}
              onSelect={setBreed}
              options={getBreedOptions(species)}
              placeholder="Select breed"
              error={errors.breed}
              allowCustomBreed={true}
            />

            <Text style={styles.label}>{t('animals.gender')}</Text>
            <View style={styles.genderContainer}>
              <GenderCard
                gender={t('animals.male')}
                isSelected={gender === 'male'}
                onPress={() => setGender('male')}
                imageUrl={getGenderImages(species).male}
              />
              <GenderCard
                gender={t('animals.female')}
                isSelected={gender === 'female'}
                onPress={() => setGender('female')}
                imageUrl={getGenderImages(species).female}
              />
            </View>

            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Input
                  label="Age (years)*"
                  value={age}
                  onChangeText={setAge}
                  keyboardType="decimal-pad"
                  error={errors.age}
                  leftIcon={<Scale size={20} color={colors.textSecondary} />}
                />
              </View>
              <View style={styles.halfWidth}>
                <Input
                  label="Weight*"
                  value={weight}
                  onChangeText={setWeight}
                  keyboardType="decimal-pad"
                  error={errors.weight}
                  placeholder="Enter weight in kg"
                  leftIcon={<Scale size={20} color={colors.textSecondary} />}
                />
              </View>
            </View>

            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <DatePickerInput
                  label="Birth Date*"
                  value={birthDate}
                  onChange={setBirthDate}
                  // error={errors.birthDate}
                  leftIcon={<Calendar size={20} color={colors.textSecondary} />}
                />
              </View>
              <View style={styles.halfWidth}>
                <Input
                  label="Tag ID*"
                  value={tagId}
                  onChangeText={setTagId}
                  error={errors.tagId}
                  leftIcon={<Tag size={20} color={colors.textSecondary} />}
                />
              </View>
            </View>

            <Text style={styles.label}>Select Farm*</Text>
            <GenericDropdown
              items={farmItems}
              value={farmId}
              onSelect={setFarmId}
              placeholder="Select Farm"
              modalTitle={t('farms.selectFarm')}
              searchPlaceholder={t('farms.searchFarms')}
              error={errors.farmId}
            />
          </View>
        </View>
      </ScrollView>

      <View style={styles.submitButtonContainer}>
        <Button
          title={t('common.save')}
          onPress={handleSave}
          isLoading={isLoading}
          leftIcon={<Save size={20} color="white" />}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  form: {
    flex: 1,
  },
  imageSection: {
    padding: 16,
    alignItems: 'center',
  },
  imagePreviewContainer: {
    width: '100%',
    aspectRatio: 16 / 9,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  imagePreview: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    width: '100%',
    aspectRatio: 16 / 9,
    backgroundColor: colors.card,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  animalSilhouette: {
    width: '100%',
    height: '100%',
    opacity: 0.5,
  },
  imageButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 10,
  },
  buttonIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  cameraButton: {
    flex: 1,
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  galleryButton: {
    flex: 1,
    backgroundColor: colors.secondary,
    borderRadius: 12,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cameraButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 2,
  },
  galleryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 2,
  },
  successIndicator: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    padding: 8,
    borderRadius: 8,
  },
  successText: {
    marginLeft: 8,
    color: colors.success,
    fontSize: 14,
  },
  urduText: {
    marginRight: 8,
    marginLeft: 0,
  },
  formSection: {
    padding: 12,
    paddingTop: 4,
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  speciesContainer: {
    gap: 12,
  },
  speciesRow: {
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'space-between',
  },
  speciesCard: {
    flex: 1,
    aspectRatio: 1,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    height:100
  },
  speciesCardSelected: {
    backgroundColor: colors.primaryLight,
    borderColor: colors.primary,
    // borderWidth: 2,
  },
  imageContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 2,
    borderColor: colors.border,
    overflow: 'hidden',
    marginBottom: 8,
  },
  selectedImageContainer: {
    borderColor: colors.primary,
    borderWidth: 3,
  },
  speciesImage: {
    width: '100%',
    height: '100%',
  },
  speciesText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    textAlign: 'center',
  },
  speciesTextSelected: {
    color: colors.primary,
    fontWeight: '600',
  },
  errorText: {
    color: colors.error,
    fontSize: 12,
    marginTop: 4,
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  genderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  genderItem: {
    width: '31%',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    backgroundColor: colors.card,
  },
  genderItemSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  genderText: {
    fontSize: 14,
    color: colors.text,
  },
  genderTextSelected: {
    color: colors.primary,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  halfInput: {
    width: '48%',
  },
  submitButtonContainer: {
    padding: 16,
    paddingTop: 8,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 16,
  },
  fieldErrorText: {
    color: colors.error,
    fontSize: 12,
    marginBottom: 4,
    marginLeft: 4,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  speciesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  genderCard: {
    width: '48%', // Changed to 48% to fit 2 in a row with gap
    aspectRatio: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.card,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    height:100
  },
  genderCardSelected: {
    backgroundColor: colors.primaryLight,
    borderColor: colors.primary,
  },
  genderImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 4,
  },
  genderText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.text,
  },
  genderTextSelected: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  rowContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  saveButton: {
    marginTop: 24,
  },
});






















