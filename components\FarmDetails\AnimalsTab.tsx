import React from 'react';
import { View, FlatList } from 'react-native';
import { useRouter } from 'expo-router';
import { Cat } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { Animal } from '@/types/animal';
import { Farm } from '@/types/farm';
import AnimalCard from '@/components/AnimalCard';
import EmptyState from '@/components/EmptyState';

interface AnimalsTabProps {
  farmAnimals: Animal[];
  farm: Farm;
  onAddAnimal: () => void;
  styles: any;
}

export default function AnimalsTab({ farmAnimals, farm, onAddAnimal, styles }: AnimalsTabProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const themedColors = useThemeColors();

  return (
    <View style={styles.tabContent}>
      {farmAnimals.length === 0 ? (
        <EmptyState
          title={t('animals.noAnimals')}
          message={t('animals.addYourFirstAnimal')}
          actionLabel={t('animals.addAnimal')}
          onAction={onAddAnimal}
          icon={<Cat size={48} color={themedColors.primary} />}
        />
      ) : (
        <FlatList
          data={farmAnimals}
          keyExtractor={item => item.id}
          renderItem={({ item }) => {
            return (
              <AnimalCard
                animal={{
                  ...item,
                  farmName: farm.name,
                  farm: { name: farm.name }
                }}
                onPress={() => router.push(`/animals/${item.id}`)}
                showHealthStatus={true}
                hasHealthIssues={item.hasHealthIssues || false}
              />
            );
          }}
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );
}
