import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAnimalStore } from '@/store/animal-store';
import { useRecordStore } from '@/store/record-store';
import { RecordCard } from '@/components/RecordCard';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { Plus, FileText, ArrowLeft } from 'lucide-react-native';
import { AnimalRecord } from '@/types/record';

export default function AnimalRecordsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getAnimal } = useAnimalStore();
  const { records, fetchRecords, isLoading } = useRecordStore();

  const [animal, setAnimal] = useState(getAnimal(id));
  const [animalRecords, setAnimalRecords] = useState<AnimalRecord[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (id) {
      setAnimal(getAnimal(id));
      loadRecords();
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      setAnimalRecords(records.filter(record => record.animalId === id));
    }
  }, [id, records]);

  const loadRecords = async () => {
    if (id) {
      await fetchRecords(id);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadRecords();
    setRefreshing(false);
  };

  const handleAddRecord = () => {
    router.push({
      pathname: '/records/add',
      params: { animalId: id }
    });
  };

  if (isLoading && !refreshing) {
    return <LoadingIndicator fullScreen message="Loading health records..." />;
  }

  if (!animal) {
    return (
      <EmptyState
        title="Animal Not Found"
        message="The animal you're looking for doesn't exist or has been deleted."
        actionLabel="Go Back"
        onAction={() => router.back()}
      />
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: `${animal.name}'s Records`,
        headerTitleStyle: { fontWeight: 'bold' },
        headerStyle: {
          backgroundColor: 'transparent',
        },
        headerTintColor: colors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
        )
      }} />
      <View style={styles.header}>
        <View style={styles.animalInfo}>
          <Text style={styles.animalName}>{animal.name}</Text>
          <Text style={styles.animalSpecies}>
            {animal.species}
            {animal.breed ? ` • ${animal.breed}` : ''}
          </Text>
        </View>
      </View>

      {animalRecords.length === 0 ? (
        <EmptyState
          title="No Health Records"
          message="Add health records to track this animal's medical history"
          actionLabel="Add Record"
          onAction={handleAddRecord}
          icon={<FileText size={48} color={colors.primary} />}
          style={styles.emptyState}
        />
      ) : (
        <FlatList
          data={animalRecords.sort((a, b) => b.date - a.date)}
          keyExtractor={item => item.id}
          renderItem={({ item }) => <RecordCard record={item} />}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        />
      )}

      {/* Floating action button */}
      <TouchableOpacity
        style={styles.floatingAddButton}
        onPress={handleAddRecord}
      >
        <Plus size={24} color="white" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.card,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  animalSpecies: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  addButton: {
    backgroundColor: colors.primary,
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listContent: {
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
  },
  floatingAddButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: colors.primary,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
  },
});
