import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Camera, Image as ImageIcon } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';

interface ImageCaptureButtonsProps {
  onTakePhoto: () => void;
  onChooseFromLibrary: () => void;
  disabled?: boolean;
  takePhotoText?: string;
  choosePhotoText?: string;
}

const ImageCaptureButtons: React.FC<ImageCaptureButtonsProps> = ({
  onTakePhoto,
  onChooseFromLibrary,
  disabled = false,
  takePhotoText,
  choosePhotoText,
}) => {
  const themedColors = useThemeColors();
  const { t } = useTranslation();
  const styles = getStyles(themedColors);

  return (
    <View style={styles.imageButtonsContainer}>
      <TouchableOpacity
        style={[styles.cameraButton, disabled && styles.disabledButton]}
        onPress={onTakePhoto}
        disabled={disabled}
      >
        <View style={styles.buttonIconContainer}>
          <Camera size={22} color="white" />
        </View>
        <Text style={styles.cameraButtonText}>
          {takePhotoText || t('animals.takePhoto')}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.galleryButton, disabled && styles.disabledButton]}
        onPress={onChooseFromLibrary}
        disabled={disabled}
      >
        <View style={styles.buttonIconContainer}>
          <ImageIcon size={22} color="white" />
        </View>
        <Text style={styles.galleryButtonText}>
          {choosePhotoText || t('animals.choosePhoto')}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  imageButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 10,
  },
  buttonIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  cameraButton: {
    flex: 1,
    backgroundColor: themedColors.primary,
    borderRadius: 12,
    padding: 10,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  galleryButton: {
    flex: 1,
    backgroundColor: themedColors.primary,
    borderRadius: 12,
    padding: 10,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cameraButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 2,
  },
  galleryButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 2,
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default ImageCaptureButtons;
