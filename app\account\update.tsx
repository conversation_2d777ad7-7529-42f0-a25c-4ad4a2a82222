import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '@/store/auth-store';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import { useTranslation } from '@/hooks/useTranslation';
import { colors } from '@/constants/colors';
import { Mail, Lock, ChevronRight } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

export default function UpdateAccountScreen() {
  const router = useRouter();
  const { user, updateProfile, updatePassword, isLoading, error } = useAuthStore();
  const { playFeedback } = useAudioFeedback();
  const { t } = useTranslation();
  const themedColors = useThemeColors();
  
  const [activeSection, setActiveSection] = useState<'none' | 'email' | 'password'>('none');
  
  // Email update state
  const [currentEmail, setCurrentEmail] = useState(user?.email || '');
  const [newEmail, setNewEmail] = useState('');
  
  // Password update state
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  
  const handleUpdateEmail = async () => {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
      Alert.alert(t('common.error'), t('account.invalidEmail'));
      playFeedback('error');
      return;
    }
    
    try {
      await updateProfile({ email: newEmail });
      playFeedback('success');
      Alert.alert(t('common.success'), t('account.emailUpdated'));
      setActiveSection('none');
      setNewEmail('');
    } catch (err) {
      console.error('Error updating email:', err);
      playFeedback('error');
      Alert.alert(t('common.error'), t('account.updateFailed'));
    }
  };
  
  const handleUpdatePassword = async () => {
    // Validate password
    if (newPassword.length < 6) {
      Alert.alert(t('common.error'), t('account.passwordTooShort'));
      playFeedback('error');
      return;
    }
    
    if (newPassword !== confirmNewPassword) {
      Alert.alert(t('common.error'), t('account.passwordsDontMatch'));
      playFeedback('error');
      return;
    }
    
    try {
      await updatePassword(currentPassword, newPassword);
      playFeedback('success');
      Alert.alert(t('common.success'), t('account.passwordUpdated'));
      setActiveSection('none');
      setCurrentPassword('');
      setNewPassword('');
      setConfirmNewPassword('');
    } catch (err) {
      console.error('Error updating password:', err);
      playFeedback('error');
      Alert.alert(t('common.error'), t('account.updateFailed'));
    }
  };
  
  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>{t('account.updateAccount')}</Text>
        
        {activeSection === 'none' && (
          <View style={styles.optionsList}>
            <TouchableOpacity 
              style={styles.optionItem}
              onPress={() => {
                playFeedback('navigation');
                setActiveSection('email');
              }}
            >
              <View style={styles.optionIconContainer}>
                <Mail size={20} color={themedColors.primary} />
              </View>
              <Text style={styles.optionText}>{t('account.updateEmail')}</Text>
              <ChevronRight size={20} color={themedColors.textSecondary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.optionItem}
              onPress={() => {
                playFeedback('navigation');
                setActiveSection('password');
              }}
            >
              <View style={styles.optionIconContainer}>
                <Lock size={20} color={themedColors.primary} />
              </View>
              <Text style={styles.optionText}>{t('account.updatePassword')}</Text>
              <ChevronRight size={20} color={themedColors.textSecondary} />
            </TouchableOpacity>
          </View>
        )}
        
        {activeSection === 'email' && (
          <View style={styles.formContainer}>
            <Text style={styles.sectionTitle}>{t('account.updateEmail')}</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{t('account.currentEmail')}</Text>
              <TextInput
                style={styles.input}
                value={currentEmail}
                editable={false}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{t('account.newEmail')}</Text>
              <TextInput
                style={styles.input}
                value={newEmail}
                onChangeText={setNewEmail}
                placeholder="<EMAIL>"
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => {
                  playFeedback('navigation');
                  setActiveSection('none');
                  setNewEmail('');
                }}
              >
                <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.saveButton,
                  (!newEmail || isLoading) && styles.disabledButton
                ]}
                onPress={handleUpdateEmail}
                disabled={!newEmail || isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.saveButtonText}>{t('common.save')}</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        )}
        
        {activeSection === 'password' && (
          <View style={styles.formContainer}>
            <Text style={styles.sectionTitle}>{t('account.updatePassword')}</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{t('account.currentPassword')}</Text>
              <TextInput
                style={styles.input}
                value={currentPassword}
                onChangeText={setCurrentPassword}
                secureTextEntry
                placeholder="••••••••"
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{t('account.newPassword')}</Text>
              <TextInput
                style={styles.input}
                value={newPassword}
                onChangeText={setNewPassword}
                secureTextEntry
                placeholder="••••••••"
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{t('account.confirmNewPassword')}</Text>
              <TextInput
                style={styles.input}
                value={confirmNewPassword}
                onChangeText={setConfirmNewPassword}
                secureTextEntry
                placeholder="••••••••"
              />
            </View>
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => {
                  playFeedback('navigation');
                  setActiveSection('none');
                  setCurrentPassword('');
                  setNewPassword('');
                  setConfirmNewPassword('');
                }}
              >
                <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.saveButton,
                  (!currentPassword || !newPassword || !confirmNewPassword || isLoading) && styles.disabledButton
                ]}
                onPress={handleUpdatePassword}
                disabled={!currentPassword || !newPassword || !confirmNewPassword || isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.saveButtonText}>{t('common.save')}</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        )}
        
        {error && (
          <Text style={styles.errorText}>{error}</Text>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollContent: {
    padding: 16,
    flexGrow: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 24,
  },
  optionsList: {
    marginTop: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: themedColors.card,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  optionIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: themedColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    color: themedColors.text,
  },
  formContainer: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 8,
  },
  input: {
    backgroundColor: themedColors.card,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: themedColors.text,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: themedColors.text,
  },
  saveButton: {
    flex: 1,
    paddingVertical: 14,
    backgroundColor: themedColors.primary,
    borderRadius: 8,
    marginLeft: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  errorText: {
    color: themedColors.error,
    marginTop: 16,
    textAlign: 'center',
  },
});