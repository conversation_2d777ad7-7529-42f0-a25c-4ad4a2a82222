{"expo": {"name": "Animal Health Tracker", "slug": "animal-health", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/ll.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/ll.png", "resizeMode": "contain", "backgroundColor": "#1E293B"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.animalhealth.tracker", "runtimeVersion": {"policy": "appVersion"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/ll.png", "backgroundColor": "#1E293B"}, "package": "com.animalhealth.tracker", "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "READ_MEDIA_IMAGES", "READ_MEDIA_VIDEO", "READ_MEDIA_AUDIO", "RECORD_AUDIO", "CAMERA", "READ_EXTERNAL_STORAGE", "READ_MEDIA_IMAGES", "READ_MEDIA_VIDEO", "READ_MEDIA_AUDIO", "RECORD_AUDIO"], "runtimeVersion": "1.0.0"}, "web": {"bundler": "metro", "favicon": "./assets/images/ll.png"}, "plugins": ["expo-router"], "extra": {"router": {"origin": false}, "eas": {"projectId": "3f28c4a3-df6e-4e25-acde-558194f541e9"}}, "owner": "<PERSON><PERSON>_khan", "updates": {"url": "https://u.expo.dev/3f28c4a3-df6e-4e25-acde-558194f541e9"}}}