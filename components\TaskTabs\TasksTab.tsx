import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  RefreshControl
} from 'react-native';
import { Calendar } from 'lucide-react-native';

import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { Task, TaskStatus } from '@/types/task';
import EmptyState from '@/components/EmptyState';
import TaskCard from '@/components/TaskCard';

interface TasksTabProps {
  tasks: Task[];
  filter: 'all' | 'pending' | 'completed';
  onFilterChange: (filter: 'all' | 'pending' | 'completed') => void;
  onTaskPress: (taskId: string) => void;
  onAddTask: () => void;
  refreshing: boolean;
  onRefresh: () => void;
}

export default function TasksTab({
  tasks,
  filter,
  onFilterChange,
  onTaskPress,
  onAddTask,
  refreshing,
  onRefresh
}: TasksTabProps) {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  
  const styles = getStyles(themedColors, language);

  // Safe filtering function
  const getFilteredTasks = () => {
    if (!tasks || !Array.isArray(tasks)) {
      return [];
    }
    
    return tasks.filter(task => {
      if (!task) {
        return false;
      }
      
      try {
        if (filter === 'all') return true;
        if (filter === 'pending') return task.status !== TaskStatus.COMPLETED;
        if (filter === 'completed') return task.status === TaskStatus.COMPLETED;
        return true;
      } catch (err) {
        console.error('Error filtering task:', err, task);
        return false;
      }
    });
  };

  const filteredTasks = getFilteredTasks();

  return (
    <View style={styles.tabContent}>
      <View style={[styles.filterContainer, language === 'ur' && { flexDirection: 'row-reverse' }]}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'all' && styles.activeFilterButton
          ]}
          onPress={() => onFilterChange('all')}
        >
          <Text 
            style={[
              styles.filterText,
              filter === 'all' && styles.activeFilterText
            ]}
          >
            {t('tasks.all')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'pending' && styles.activeFilterButton
          ]}
          onPress={() => onFilterChange('pending')}
        >
          <Text 
            style={[
              styles.filterText,
              filter === 'pending' && styles.activeFilterText
            ]}
          >
            {t('tasks.pending')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'completed' && styles.activeFilterButton
          ]}
          onPress={() => onFilterChange('completed')}
        >
          <Text 
            style={[
              styles.filterText,
              filter === 'completed' && styles.activeFilterText
            ]}
          >
            {t('tasks.completed')}
          </Text>
        </TouchableOpacity>
      </View>
      
      {filteredTasks.length === 0 ? (
        <EmptyState
          icon={<Calendar size={60} color={themedColors.primary} />}
          title={t('tasks.noTasks')}
          message={t('tasks.noTasksMessage')}
          actionLabel={t('tasks.addTask')}
          onAction={onAddTask}
        />
      ) : (
        <FlatList
          data={filteredTasks}
          renderItem={({ item }) => (
            <TaskCard 
              task={item} 
              onPress={() => onTaskPress(item.id!)}
            />
          )}
          keyExtractor={(item) => item.id!}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[themedColors.primary]}
              tintColor={themedColors.primary}
            />
          }
        />
      )}
    </View>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  tabContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: language === 'ur' ? 0 : 8,
    marginLeft: language === 'ur' ? 8 : 0,
    backgroundColor: themedColors.card,
  },
  activeFilterButton: {
    backgroundColor: themedColors.primary,
  },
  filterText: {
    color: themedColors.textSecondary,
    fontWeight: '500',
  },
  activeFilterText: {
    color: themedColors.isDarkMode ? themedColors.textContrast : 'white',
  },
  listContent: {
    paddingBottom: 80, // Space for the floating button
  },
});
