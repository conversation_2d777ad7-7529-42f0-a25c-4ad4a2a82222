import { create } from 'zustand';
import { 
  collection, 
  doc, 
  addDoc, 
  getDoc, 
  getDocs, 
  updateDoc, 
  deleteDoc, 
  query, 
  where,
  Timestamp 
} from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { Expense, ExpenseCategory } from '@/types/expense';
import { useFarmStore } from './farm-store';
import { Farm } from '@/types/farm';

interface ExpenseStore {
  expenses: Expense[];
  isLoading: boolean;
  error: string | null;
  lastFetchTime: number | null;
  lastUserId: string | null;
  establishedCurrency: string | null; // The currency locked for this user

  // Actions
  fetchExpenses: (farmId: string) => Promise<Expense[]>;
  fetchExpensesForUser: (userId: string) => Promise<Expense[]>;
  getExpenseById: (farmId: string, expenseId: string) => Promise<Expense | null>;
  addExpense: (expense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Expense>;
  updateExpense: (farmId: string, expenseId: string, expenseUpdate: Partial<Expense>) => Promise<void>;
  deleteExpense: (farmId: string, expenseId: string) => Promise<void>;
  clearExpenses: () => void;
  getEstablishedCurrency: (userId: string) => Promise<string | null>;
  setEstablishedCurrency: (currency: string) => void;
}

export const useExpenseStore = create<ExpenseStore>((set, get) => ({
  expenses: [],
  isLoading: false,
  error: null,
  lastFetchTime: null,
  lastUserId: null,
  establishedCurrency: null,

  fetchExpenses: async (farmId: string) => {
    set({ isLoading: true, error: null });
    try {
      if (!farmId) {
        throw new Error("Farm ID is required to fetch expenses.");
      }

      // Check if the provided ID is a user ID instead of a farm ID
      const userRef = doc(firestore, 'users', farmId);
      const userDoc = await getDoc(userRef);
      
      if (userDoc.exists()) {
        throw new Error(`Invalid farm ID: ${farmId} (this appears to be a user ID)`);
      }

      // Validate that the farmId exists in Firestore
      const farmRef = doc(firestore, 'farms', farmId);
      const farmDoc = await getDoc(farmRef);

      if (!farmDoc.exists()) {
        throw new Error(`Farm with ID ${farmId} not found`);
      }

      const expensesCollectionRef = collection(firestore, 'farms', farmId, 'expenses');
      const q = query(expensesCollectionRef);

      // Execute the query
      const querySnapshot = await getDocs(q);
      const expenses: Expense[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();

        // Handle Firestore timestamps properly
        const createdAt = data.createdAt instanceof Timestamp
          ? data.createdAt.toMillis()
          : (typeof data.createdAt === 'number' ? data.createdAt : Date.now());

        const updatedAt = data.updatedAt instanceof Timestamp
          ? data.updatedAt.toMillis()
          : (typeof data.updatedAt === 'number' ? data.updatedAt : Date.now());

        // Convert date if it's a Timestamp
        const date = data.date instanceof Timestamp
          ? data.date.toMillis()
          : (typeof data.date === 'number' ? data.date : Date.now());

        expenses.push({
          id: doc.id,
          ...data,
          farmId: farmId, // Ensure farmId is part of the expense object
          date,
          createdAt,
          updatedAt,
        } as Expense);
      });

      // Sort expenses by date (newest first) after fetching
      expenses.sort((a, b) => b.date - a.date);

      set({ expenses, isLoading: false });
      return expenses;
    } catch (error) {
      set({
        error: 'Failed to fetch expenses. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },
  // Simplified function to fetch expenses for all farms of a user
  fetchExpensesForUser: async (userId: string) => {
    set({ isLoading: true, error: null });

    try {
      if (!userId) {
        throw new Error("User ID is required to fetch expenses.");
      }

      // Get farms for this user
      const { farms } = useFarmStore.getState();
      let userFarms = farms;

      // If no farms in store, fetch them
      if (!userFarms || userFarms.length === 0) {
        const farmsRef = collection(firestore, 'farms');
        const q = query(farmsRef, where("ownerId", "==", userId));
        const querySnapshot = await getDocs(q);

        userFarms = [];
        querySnapshot.forEach((doc) => {
          userFarms.push({ id: doc.id, ...doc.data() } as Farm);
        });
      }

      if (userFarms.length === 0) {
        set({ expenses: [], isLoading: false });
        return [];
      }

      // Fetch expenses for each farm
      const allExpenses: Expense[] = [];

      for (const farm of userFarms) {
        try {
          const expensesCollectionRef = collection(firestore, 'farms', farm.id, 'expenses');
          const querySnapshot = await getDocs(expensesCollectionRef);

          querySnapshot.forEach((doc) => {
            const data = doc.data();

            // Handle Firestore timestamps
            const createdAt = data.createdAt instanceof Timestamp
              ? data.createdAt.toMillis()
              : (typeof data.createdAt === 'number' ? data.createdAt : Date.now());

            const updatedAt = data.updatedAt instanceof Timestamp
              ? data.updatedAt.toMillis()
              : (typeof data.updatedAt === 'number' ? data.updatedAt : Date.now());

            const date = data.date instanceof Timestamp
              ? data.date.toMillis()
              : (typeof data.date === 'number' ? data.date : Date.now());

            allExpenses.push({
              id: doc.id,
              ...data,
              farmId: farm.id,
              farmName: farm.name,
              date,
              createdAt,
              updatedAt,
            } as Expense);
          });
        } catch (farmError) {
          // Continue with other farms
        }
      }

      // Sort expenses by date (newest first)
      allExpenses.sort((a, b) => b.date - a.date);

      set({
        expenses: allExpenses,
        isLoading: false
      });
      return allExpenses;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch expenses';
      set({
        error: errorMessage,
        isLoading: false
      });
      throw error;
    }
  },
  getExpenseById: async (farmId: string, expenseId: string) => {
    try {
      // First check if the expense is in the store
      const storeExpense = get().expenses.find(e => e.id === expenseId && e.farmId === farmId);

      if (storeExpense) return storeExpense;

      // If not in store, fetch from Firestore
      const docRef = doc(firestore, 'farms', farmId, 'expenses', expenseId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data();
        return {
          id: docSnap.id,
          ...data,
          createdAt: data.createdAt?.toMillis() || Date.now(),
          updatedAt: data.updatedAt?.toMillis() || Date.now(),
          farmId: farmId, // Ensure farmId is part of the expense object
        } as Expense;
      }

      return null;
    } catch (error) {
      return null;
    }
  },

  addExpense: async (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => {
    set({ isLoading: true, error: null });
    try {
      if (!expenseData.farmId) {
        throw new Error("Farm ID is required to add an expense.");
      }
      // tenantId should also be part of expenseData

      // Set default currency if not provided
      if (!expenseData.currency) {
        expenseData.currency = 'PKR';
      }

      const now = Date.now();
      const expenseWithTimestamps = {
        ...expenseData,
        createdAt: now,
        updatedAt: now,
      };

      const docRef = await addDoc(collection(firestore, 'farms', expenseData.farmId, 'expenses'), expenseWithTimestamps);

      const newExpense: Expense = {
        id: docRef.id,
        ...expenseWithTimestamps,
      };

      set(state => ({
        // Add to local state, ensuring it's sorted or handled correctly if expenses are farm-specific
        expenses: [newExpense, ...state.expenses].sort((a, b) => b.date - a.date),
        isLoading: false,
      }));

      return newExpense;
    } catch (error) {
      set({
        error: 'Failed to add expense. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  updateExpense: async (farmId: string, expenseId: string, expenseUpdate: Partial<Expense>) => {
    set({ isLoading: true, error: null });
    try {
      if (!farmId || !expenseId) {
        throw new Error("Farm ID and Expense ID are required for update.");
      }
      const docRef = doc(firestore, 'farms', farmId, 'expenses', expenseId);

      await updateDoc(docRef, {
        ...expenseUpdate,
        updatedAt: Timestamp.now(), // Using Timestamp.now() for consistency with other parts
      });

      set((state) => ({
        expenses: state.expenses.map((expense) =>
          expense.id === expenseId && expense.farmId === farmId
            ? { ...expense, ...expenseUpdate, updatedAt: Date.now() }
            : expense
        ).sort((a, b) => b.date - a.date),
        isLoading: false,
      }));
    } catch (error) {
      set({
        error: 'Failed to update expense. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  deleteExpense: async (farmId: string, expenseId: string) => {
    set({ isLoading: true, error: null });
    try {
      if (!farmId || !expenseId) {
        throw new Error("Farm ID and Expense ID are required for deletion.");
      }
      await deleteDoc(doc(firestore, 'farms', farmId, 'expenses', expenseId));
      set((state) => ({
        expenses: state.expenses.filter((expense) => !(expense.id === expenseId && expense.farmId === farmId)),
        isLoading: false,
      }));
    } catch (error) {
      set({
        error: 'Failed to delete expense. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  clearExpenses: () => {
    set({
      expenses: [],
      error: null,
      lastFetchTime: null,
      lastUserId: null,
      establishedCurrency: null
    });
  },

  // Get the established currency for a user by checking their first expense
  getEstablishedCurrency: async (userId: string) => {
    try {
      const { expenses } = get();

      // If we already have the established currency cached, return it
      if (get().establishedCurrency && get().lastUserId === userId) {
        return get().establishedCurrency;
      }

      // If we have expenses loaded for this user, find the first one
      if (expenses.length > 0 && get().lastUserId === userId) {
        const sortedExpenses = [...expenses].sort((a, b) => a.createdAt - b.createdAt);
        const firstExpense = sortedExpenses[0];
        if (firstExpense?.currency) {
          set({ establishedCurrency: firstExpense.currency });
          return firstExpense.currency;
        }
      }

      // Otherwise, query Firebase for the user's first expense
      const expensesQuery = query(
        collection(firestore, 'expenses'),
        where('createdBy', '==', userId)
      );

      const querySnapshot = await getDocs(expensesQuery);

      if (querySnapshot.empty) {
        // No expenses found, user can choose any currency
        return null;
      }

      // Find the earliest expense by createdAt timestamp
      let earliestExpense: any = null;
      querySnapshot.forEach((doc) => {
        const expense = { id: doc.id, ...doc.data() };
        if (!earliestExpense || expense.createdAt < earliestExpense.createdAt) {
          earliestExpense = expense;
        }
      });

      const establishedCurrency = earliestExpense?.currency || null;
      set({ establishedCurrency });
      return establishedCurrency;
    } catch (error) {
      console.error('Error getting established currency:', error);
      return null;
    }
  },

  // Set the established currency (called when first expense is added)
  setEstablishedCurrency: (currency: string) => {
    set({ establishedCurrency: currency });
  },
}));

















