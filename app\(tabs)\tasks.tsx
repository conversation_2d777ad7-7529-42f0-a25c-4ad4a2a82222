import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Plus, Calendar, Users, ClipboardList } from 'lucide-react-native';
import { ErrorBoundary } from '@/app/error-boundary';

import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuthStore } from '@/store/auth-store';
import { useTaskStore } from '@/store/task-store';
import { TaskStatus } from '@/types/task';
import { getEmployeesByOwner } from '@/services/employee-service';
import { Employee } from '@/types/employee';
import LoadingIndicator from '@/components/LoadingIndicator';
import CountCard from '@/components/CountCard';
import { TasksTab, StaffTab } from '@/components/TaskTabs';

export default function TasksScreen() {
  return (
    <ErrorBoundary onError={(error, errorInfo) => {
      console.error("Tasks Screen Error:", error);
      console.error("Component Stack:", errorInfo.componentStack);
    }}>
      <TasksScreenContent />
    </ErrorBoundary>
  );
}

function TasksScreenContent() {
  const { t } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();
  const { tasks, loading, fetchTasks } = useTaskStore();
  const themedColors = useThemeColors();
  
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'tasks' | 'staff'>('tasks');
  const [filter, setFilter] = useState<'all' | 'pending' | 'completed'>('all');
  const [staff, setStaff] = useState<Employee[]>([]);
  const [loadingStaff, setLoadingStaff] = useState(false);
  
  const styles = getStyles(themedColors);

  // Use useCallback to memoize the loadTasks function
  const loadTasks = useCallback(async () => {
    if (!user) {
      return;
    }
    
    try {
      await fetchTasks(user.id, user.role);
    } catch (error) {
      console.error('Error in loadTasks:', error);
    }
  }, [user, fetchTasks]);
  
  // Safe filtering function
  const getFilteredTasks = useCallback(() => {
    if (!tasks || !Array.isArray(tasks)) {
      return [];
    }
    
    return tasks.filter(task => {
      if (!task) {
        return false;
      }
      
      try {
        if (filter === 'all') return true;
        if (filter === 'pending') return task.status !== TaskStatus.COMPLETED;
        if (filter === 'completed') return task.status === TaskStatus.COMPLETED;
        return true;
      } catch (err) {
        console.error('Error filtering task:', err, task);
        return false;
      }
    });
  }, [tasks, filter]);
  
  useEffect(() => {
    if (user) {
      // Load both tasks and staff data when component mounts
      loadTasks();
      loadStaff();
    }
  }, [user]);

  // Separate effect for tab changes
  useEffect(() => {
    if (user) {
      if (activeTab === 'tasks') {
        loadTasks();
      } else if (activeTab === 'staff') {
        loadStaff();
      }
    }
  }, [user, activeTab]);
  
  const loadStaff = async () => {
    if (!user) return;
    
    setLoadingStaff(true);
    try {
      
      // Fetch staff based on user role
      if (user.role === 'owner' || user.role === 'admin') {
        // Owners and admins can see all staff
        const staffMembers = await getEmployeesByOwner(user.id);
        
        // If user is admin, filter out other admins and owners
        if (user.role === 'admin') {
          const filteredStaff = staffMembers.filter(
            (member: any) => member.role === 'caretaker'
          );
          setStaff(filteredStaff as Employee[]);
        } else {
          // Owner can see all staff (admins and caretakers)
          setStaff(staffMembers as Employee[]);
        }
        
      } else {
        // Caretakers shouldn't see this tab, but just in case
        setStaff([]);
      }
    } catch (error) {
      console.error('Error loading staff:', error);
    } finally {
      setLoadingStaff(false);
    }
  };
  
  const handleRefresh = async () => {
    setRefreshing(true);
    if (activeTab === 'tasks') {
      await loadTasks();
    } else if (activeTab === 'staff') {
      await loadStaff();
    }
    setRefreshing(false);
  };
  
  const handleAddTask = () => {
    router.push('/tasks/add');
  };
  
  const handleAddStaff = () => {
    router.push('/employees/add');
  };
  
  const handleTaskPress = (taskId: string) => {
    router.push(`/tasks/${taskId}`);
  };
  
  // Removed unused handleAssignStaff function

  if (activeTab === 'tasks' && loading && !refreshing){
    return <LoadingIndicator fullScreen message={t('tasks.loading')} />;
  }else if(activeTab === 'staff' && loadingStaff && !refreshing) {
    return <LoadingIndicator fullScreen message={t('staff.loading')} />;
  }


  const renderCountCards = () => {
    // Count tasks
    const taskCount = tasks?.length || 0;
    
    // Count staff
    const staffCount = staff?.length || 0;
    
    return (
      <View style={styles.countCardsContainer}>
        <CountCard
          count={taskCount}
          label={t('tasks.tasks')}
          icon={<ClipboardList size={24} color={themedColors.primary} />}
          onPress={() => setActiveTab('tasks')}
          isActive={activeTab === 'tasks'}
        />
        <CountCard
          count={staffCount}
          label={t('staff.staff')}
          icon={<Users size={24} color={themedColors.primary} />}
          onPress={() => setActiveTab('staff')}
          isActive={activeTab === 'staff'}
        />
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom', 'left', 'right']}>
      <Stack.Screen
        options={{
          title: t('tasks.title'),
          headerBackTitle: t('common.back')
        }}
      />
      
      {/* Count Cards */}
      {renderCountCards()}
      
      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'tasks' && styles.activeTabButton]}
          onPress={() => setActiveTab('tasks')}
        >
          <Calendar size={20} color={activeTab === 'tasks' ? themedColors.primary : themedColors.textSecondary} />
          <Text style={[styles.tabButtonText, activeTab === 'tasks' && styles.activeTabText]}>
            {t('tasks.tasks')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'staff' && styles.activeTabButton]}
          onPress={() => setActiveTab('staff')}
        >
          <Users size={20} color={activeTab === 'staff' ? themedColors.primary : themedColors.textSecondary} />
          <Text style={[styles.tabButtonText, activeTab === 'staff' && styles.activeTabText]}>
            {t('staff.assignStaff')}
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Tab Content */}
      <View style={styles.tabContentContainer}>
        {activeTab === 'tasks' && (
          <TasksTab
            tasks={tasks || []}
            filter={filter}
            onFilterChange={setFilter}
            onTaskPress={handleTaskPress}
            onAddTask={handleAddTask}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        )}
        {activeTab === 'staff' && (
          <StaffTab
            staff={staff}
            loadingStaff={loadingStaff}
            userRole={user?.role}
            onAddStaff={handleAddStaff}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        )}
      </View>
      
      {/* Add Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={activeTab === 'tasks' ? handleAddTask : handleAddStaff}
      >
        <Plus size={24} color={themedColors.isDarkMode ? themedColors.textContrast : 'white'} />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  countCardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: themedColors.background,
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
    paddingHorizontal: 16,
    justifyContent: 'space-between', // This ensures equal spacing
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    flex: 1, // Make each tab take equal space
    justifyContent: 'center', // Center the content horizontally
  },
  activeTabButton: {
    borderBottomColor: themedColors.primary,
  },
  tabButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.textSecondary,
  },
  activeTabText: {
    color: themedColors.primary,
    fontWeight: 'bold',
  },
  tabContentContainer: {
    flex: 1,
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: themedColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: themedColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});
