import React from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps
} from 'react-native';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import useThemeColors


interface ButtonProps extends TouchableOpacityProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  isLoading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  isLoading = false,
  disabled = false,
  style,
  textStyle,
  leftIcon,
  rightIcon,
  ...rest
}) => {
  const { language } = useTranslation();
  const themedColors = useThemeColors(); // Get themed colors

  // Determine contrast text colors based on theme
  // Assuming primary/secondary are typically darker in light mode and need light text,
  // and might be lighter in dark mode needing dark text, or just use themed text color.
  const primaryContrastText = themedColors.isDarkMode ? themedColors.text : 'white';
  const secondaryContrastText = themedColors.isDarkMode ? themedColors.text : 'white'; // Adjust if secondary needs different logic

  // Generate styles based on theme
  const styles = getStyles(themedColors, language, primaryContrastText, secondaryContrastText);

  const buttonStyles = [
    styles.button,
    styles[`${variant}Button`],
    styles[`${size}Button`],
    (disabled || isLoading) && styles.disabledButton, // Apply disabled style if isLoading too
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    (disabled || isLoading) && styles.disabledText,
    textStyle,
    language === 'ur' ? styles.urduText : null,
  ];

  // Determine icon color based on variant and theme
  let iconColor = themedColors.text; // Default
  if (variant === 'primary') {
    iconColor = primaryContrastText;
  } else if (variant === 'secondary') {
    iconColor = (themedColors as any).secondary ? secondaryContrastText : themedColors.primary;
  } else if (variant === 'outline' || variant === 'text') {
    iconColor = themedColors.primary;
  }
  if (disabled || isLoading) {
    iconColor = themedColors.textSecondary;
  }

  // Determine ActivityIndicator color
  let activityIndicatorColor = themedColors.primary; // Default for outline/text and other fallbacks
  if (variant === 'primary') {
    activityIndicatorColor = primaryContrastText;
  } else if (variant === 'secondary') {
    activityIndicatorColor = (themedColors as any).secondary ? secondaryContrastText : themedColors.primary;
  }

  return (
    <TouchableOpacity
      style={buttonStyles}
      onPress={onPress}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
      {...rest}
    >
      {isLoading ? (
        <ActivityIndicator
          size="small"
          color={activityIndicatorColor}
        />
      ) : (
        <>
          {leftIcon && React.cloneElement(leftIcon as React.ReactElement<any>, { color: iconColor })}
          <Text style={textStyles}>{title}</Text>
          {rightIcon && React.cloneElement(rightIcon as React.ReactElement<any>, { color: iconColor })}
        </>
      )}
    </TouchableOpacity>
  );
};

// Function to generate styles based on theme
const getStyles = (
  themedColorsParam: ReturnType<typeof useThemeColors>, // Renamed to avoid conflict with themedColors in component scope
  language: string, 
  primaryContrastText: string, 
  secondaryContrastText: string
) => StyleSheet.create({
  urduText: {
    fontFamily: 'System', // Consider using a specific Urdu font if available
    textAlign: 'right',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    gap: 8,
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },

  // Variants
  primaryButton: {
    backgroundColor: themedColorsParam.primary,
  },
  primaryText: {
    color: primaryContrastText, // Use the determined contrast color
  },

  secondaryButton: {
    backgroundColor: (themedColorsParam as any).secondary || themedColorsParam.primaryLight, // Fallback if secondary is not in theme
  },
  secondaryText: {
    color: (themedColorsParam as any).secondary ? secondaryContrastText : themedColorsParam.primary, // Adjust text color for fallback
  },

  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: themedColorsParam.primary,
  },
  outlineText: {
    color: themedColorsParam.primary,
  },

  textButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 0, // Keep this for text buttons
  },
  textText: {
    color: themedColorsParam.primary,
  },

  // Sizes
  smallButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  smallText: {
    fontSize: 14,
  },

  mediumButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  mediumText: {
    fontSize: 16,
  },

  largeButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  largeText: {
    fontSize: 18,
  },

  // States
  disabledButton: {
    backgroundColor: (themedColorsParam as any).inactive || '#E0E0E0', // Fallback for inactive
    borderColor: (themedColorsParam as any).inactive || '#E0E0E0',
    opacity: 0.7,
  },
  disabledText: {
    color: themedColorsParam.textSecondary,
  },
});

export default Button;