import React from 'react';
import { View, Switch as RNSwitch, StyleSheet, Platform } from 'react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

interface SwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
}

const Switch: React.FC<SwitchProps> = ({ value, onValueChange, disabled = false }) => {
  const themedColors = useThemeColors();
  
  return (
    <RNSwitch
      value={value}
      onValueChange={onValueChange}
      disabled={disabled}
      trackColor={{ 
        false: themedColors.inactive || '#E5E7EB', 
        true: themedColors.primaryLight || themedColors.primary + '80' 
      }}
      thumbColor={value ? themedColors.primary : Platform.OS === 'ios' ? '#FFFFFF' : themedColors.card}
      ios_backgroundColor={themedColors.inactive || '#E5E7EB'}
    />
  );
};

export default Switch;