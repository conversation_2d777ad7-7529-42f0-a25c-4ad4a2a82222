import React, { useEffect, useState, useMemo, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Animated,
  Easing,
} from 'react-native';
import { useRouter, useFocusEffect } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuthStore } from '@/store/auth-store';
import { useAnimalStore } from '@/store/animal-store';
import { useRecordStore } from '@/store/record-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { useFarmStore } from '@/store/farm-store';
import { useTranslation } from '@/hooks/useTranslation';
import AnimalCard from '@/components/AnimalCard';
import { RecordCard } from '@/components/RecordCard';
import HealthCheckCard from '@/components/HealthCheckCard';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import OfflineBanner from '@/components/OfflineBanner';
import DashboardStats from '@/components/DashboardStats';
import {
  Activity,
  Plus,
  ChevronRight,
  FileText,
  MapPin,
  Cat,
  RefreshCw,
} from 'lucide-react-native';
import GenericDropdown from '@/components/GenericDropdown';
import { useThemeColors } from '@/hooks/useThemeColors';

export default function HomeScreen() {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const { animals, fetchAnimals, isLoading: animalsLoading } = useAnimalStore();
  const { records, fetchRecords, isLoading: recordsLoading } = useRecordStore();
  const { healthChecks, fetchHealthChecks, isLoading: healthChecksLoading } = useHealthCheckStore();
  const { farms } = useFarmStore();
  const router = useRouter();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors);

  const [refreshing, setRefreshing] = useState(false);
  const [isOffline, setIsOffline] = useState(false);
  const [selectedFarmId, setSelectedFarmId] = useState<string | null>(null);
  const rotateAnim = useRef(new Animated.Value(0)).current;


  useEffect(() => {
    if (refreshing) {
      Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      ).start();
    } else {
      rotateAnim.setValue(0);
    }
  }, [refreshing]);

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Create farm dropdown items - only if user has farms
  const farmDropdownItems = useMemo(() => {
    if (farms.length === 0) return [];

    return [
      { id: 'all', label: t('farms.allFarms'), icon: <MapPin size={20} color={themedColors.primary} /> },
      ...farms.map(farm => ({
        id: farm.id,
        label: farm.name,
        description: farm.location,
        icon: <MapPin size={20} color={themedColors.primary} />
      }))
    ];
  }, [farms, t, themedColors.primary]);
  // Filter data based on selected farm
  const filteredData = useMemo(() => {
    // If no farm is selected or user has no farms, return empty data
    if (!selectedFarmId || farms.length === 0) {
      return {
        animals: [],
        healthChecks: [],
        records: []
      };
    }

    if (selectedFarmId === 'all') {
      return {
        animals,
        healthChecks,
        records
      };
    }

    const farmAnimals = animals.filter(animal => animal.farmId === selectedFarmId);
    const farmAnimalIds = new Set(farmAnimals.map(animal => animal.id));

    return {
      animals: farmAnimals,
      healthChecks: healthChecks.filter(check => farmAnimalIds.has(check.animalId)),
      records: records.filter(record => farmAnimalIds.has(record.animalId))
    };
  }, [selectedFarmId, animals, healthChecks, records, farms.length]);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  // Set default selectedFarmId based on available farms
  useEffect(() => {
    if (farms.length > 0 && selectedFarmId === null) {
      setSelectedFarmId('all');
    } else if (farms.length === 0) {
      setSelectedFarmId(null);
    }
  }, [farms, selectedFarmId]);

  const loadData = React.useCallback(async () => {
    if (!user) return;

    try {
      console.log('Dashboard: Loading data for user:', user.id);

      // First fetch animals, then fetch records and health checks that depend on animals
      await fetchAnimals(user.id);
      console.log('Dashboard: Animals fetched, now fetching records and health checks');

      await Promise.all([
        fetchRecords(),  // This depends on animals being loaded first
        fetchHealthChecks() // This also depends on animals being loaded first
      ]);

      console.log('Dashboard: Data loaded successfully');
    } catch (error) {
      console.error('Error loading data:', error);
      setIsOffline(true);
    }
  }, [user, fetchAnimals, fetchRecords, fetchHealthChecks]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      const refreshData = async () => {
        if (user) {
          try {
            console.log('Dashboard: Refreshing data on focus for user:', user.id);
            await loadData();
          } catch (error) {
            console.error('Error refreshing data on focus:', error);
          }
        }
      };

      refreshData();
    }, [user, loadData])
  );

  const handleRefresh = async () => {
    console.log('Dashboard: Manual refresh triggered');
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
    console.log('Dashboard: Manual refresh completed');
  };

  const handleAddAnimal = () => {
    router.push('/animals/add');
  };

  const handleAddRecord = () => {
    router.push('/records/add');
  };

  const handleAddHealthCheck = () => {
    router.push('/health-checks/add?fromDashboard=true');
  };

  const handleViewAllAnimals = () => {
    router.push('/(tabs)/animals');
  };

  const handleViewAllRecords = () => {
    router.push('/health-checks');
  };

  // Function to navigate to alerts page
  // Uncomment when alerts page is implemented
  // const handleViewAlerts = () => {
  //   router.push('/alerts');
  // };

  const getHealthyAnimalsCount = () => {
    return animals.filter(animal => {
      const animalHealthChecks = healthChecks.filter(check => check.animalId === animal.id);
      if (animalHealthChecks.length === 0) return false;

      const latestCheck = animalHealthChecks.sort((a, b) => b.date - a.date)[0];
      return !latestCheck.abnormalities;
    }).length;
  };

  const getAlertsCount = () => {
    // Count animals with abnormalities in their latest health check
    const animalsWithAbnormalities = animals.filter(animal => {
      const animalHealthChecks = healthChecks.filter(check => check.animalId === animal.id);
      if (animalHealthChecks.length === 0) return false;

      const latestCheck = animalHealthChecks.sort((a, b) => b.date - a.date)[0];
      return latestCheck.abnormalities;
    }).length;

    // Count animals with overdue health checks
    const now = Date.now();
    const animalsWithOverdueChecks = animals.filter(animal => {
      return animal.nextHealthCheck && animal.nextHealthCheck < now;
    }).length;

    return animalsWithAbnormalities + animalsWithOverdueChecks;
  };

  const isLoading = animalsLoading || recordsLoading || healthChecksLoading;

  if (isLoading && !refreshing) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  // Calculate stats for dashboard
  getAlertsCount();
  getHealthyAnimalsCount();

  const totalAnimals = filteredData.animals?.length || 0;

  return (
    <SafeAreaView style={styles.container} edges={['bottom', 'left', 'right']}>
      {isOffline && <OfflineBanner />}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[themedColors.primary]}
            tintColor={themedColors.primary}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.welcomeText}>{t('dashboard.welcome')} {user?.name || t('common.user')}</Text>
            <Text style={styles.subtitleText}>
              {totalAnimals > 0
                ? (
                  <>
                    {t('dashboard.monitoring')} <Text style={styles.boldNumber}>{totalAnimals}</Text> {t('dashboard.animals')}
                  </>
                )
                : t('animals.addYourFirstAnimal')}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.syncButton}
            onPress={handleRefresh}
            disabled={refreshing}
          >
            <Animated.View style={{ transform: [{ rotate: spin }] }}>
              <RefreshCw size={16} color={themedColors.primary} />
            </Animated.View>
            <Text style={styles.syncText}>Sync</Text>
          </TouchableOpacity>
        </View>

        {/* Farm Selector - Only show if user has farms */}
        {farms.length > 0 && (
          <View style={styles.farmSelectorContainer}>
            {/* <View style={styles.farmSelectorHeader}>
              <MapPin size={20} color={themedColors.primary} />
              <Text style={styles.farmSelectorLabel}>{t('farms.selectFarm')}</Text>
            </View> */}
            <GenericDropdown
              placeholder={t('farms.selectFarmPlaceholder')}
              items={farmDropdownItems}
              value={selectedFarmId || ''}
              onSelect={setSelectedFarmId}
              modalTitle={t('farms.selectFarm')}
              searchPlaceholder={t('farms.searchFarms')}
              containerStyle={styles.customDropdown}
            />
          </View>
        )}

        {/* Dashboard Stats */}
        <DashboardStats
          filteredAnimals={filteredData.animals}
          filteredHealthChecks={filteredData.healthChecks}
          filteredRecords={filteredData.records}
          onRefresh={loadData}
        />

        {/* Your Animals Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('dashboard.yourAnimals')}.</Text>
            <View style={styles.sectionActions}>
              {filteredData.animals.length > 0 && (
                <TouchableOpacity onPress={handleViewAllAnimals}>
                  <Text style={styles.viewAllText}>{t('dashboard.viewAll')}</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.addButton}
                onPress={handleAddAnimal}
              >
                <Plus size={16} color="white" />
              </TouchableOpacity>
            </View>
          </View>

          {filteredData.animals.length === 0 ? (
            <EmptyState
              title={t('animals.noAnimals')}
              message={!selectedFarmId || selectedFarmId === 'all'
                ? t('animals.addYourFirstAnimal')
                : t('animals.noAnimalsInFarm')}
              actionLabel={t('animals.addAnimal')}
              onAction={handleAddAnimal} // @ts-ignore
              icon={<Cat size={48} color={themedColors.primary} />}
            />
          ) : (
            <View style={styles.animalsContainer}>
              {filteredData.animals.slice(0, 3).map(animal => (
                <AnimalCard
                  key={animal.id}
                  animal={animal}
                  onPress={() => router.push(`/animals/${animal.id}`)}
                  showHealthStatus={true}
                  hasHealthIssues={animal.hasHealthIssues || false}
                />
              ))}
            </View>
          )}
        </View>

        {/* Recent Health Checks Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('dashboard.recentHealthChecks')}</Text>
            <View style={styles.sectionActions}>
              {filteredData.healthChecks.length > 0 && (
                <TouchableOpacity onPress={handleViewAllRecords}>
                  <Text style={styles.viewAllText}>{t('dashboard.viewAll')}</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.addButton}
                onPress={handleAddHealthCheck}
              >
                <Plus size={16} color="white" />
              </TouchableOpacity>
            </View>
          </View>

          {filteredData.healthChecks.length === 0 ? (
            <EmptyState
              title={t('healthChecks.noChecks')}
              message={t('healthChecks.performRegularChecks')}
              actionLabel={t('healthChecks.addCheck')}
              onAction={handleAddHealthCheck}
              icon={<Activity size={48} color={themedColors.primary} />}
            />
          ) : (
            <View style={styles.healthChecksList}>
              {filteredData.healthChecks
                .sort((a, b) => b.date - a.date)
                .slice(0, 3)
                .map(check => (
                  <HealthCheckCard
                    key={check.id}
                    healthCheck={check}
                    showAnimalName
                    animalName={animals.find(a => a.id === check.animalId)?.name}
                  />
                ))}
            </View>
          )}
        </View>

        {/* Recent Health Records Section */}
        <View style={[styles.section, styles.lastSection]}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('dashboard.recentHealthRecords')}</Text>
            <View style={styles.sectionActions}>
              {filteredData.records.length > 0 && (
                <TouchableOpacity onPress={() => router.push('/records')}>
                  <Text style={styles.viewAllText}>{t('dashboard.viewAll')}</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.addButton}
                onPress={handleAddRecord}
              >
                <Plus size={16} color="white" />
              </TouchableOpacity>
            </View>
          </View>

          {filteredData.records.length === 0 ? (
            <EmptyState
              title={t('records.noRecords')}
              message={t('records.addRecordsMessage')}
              actionLabel={t('records.addRecord')}
              onAction={handleAddRecord}
              icon={<FileText size={48} color={themedColors.primary} />}
            />
          ) : (
            <View style={styles.recordsList}>
              {filteredData.records
                .sort((a, b) => b.date - a.date)
                .slice(0, 3)
                .map(record => (
                  <RecordCard key={record.id} record={record} />
                ))}
            </View>
          )}
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push('/health-checks/add?fromDashboard=true')}
          >
            <LinearGradient
              colors={[themedColors.primary, themedColors.primaryDark || themedColors.primary]} // Fallback for primaryDark
              style={styles.actionButtonGradient}
            >
              <Activity size={20} color="white" />
              <Text style={styles.actionButtonText}>{t('dashboard.scheduleCheck')}</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleAddRecord}
          >
            <LinearGradient
              colors={[themedColors.primary, themedColors.primaryDark]} // Use primary colors
              style={styles.actionButtonGradient}
            >
              <FileText size={20} color="white" />
              <Text style={styles.actionButtonText}>{t('records.addRecord')}</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: themedColors.background,
  },
  welcomeText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  subtitleText: {
    fontSize: 13,
    color: themedColors.textSecondary,
  },
  boldNumber: {
    fontWeight: '500',
    color: themedColors.text,
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 6,
    backgroundColor: themedColors.primaryLight,
    gap: 4,
  },
  syncText: {
    color: themedColors.primary,
    fontSize: 13,
    fontWeight: '500',
    marginLeft: 2,
  },
  rotating: {
    transform: [{ rotate: '45deg' }],
  },
  statsContainer: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  statsCard: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: themedColors.isDarkMode ? 1 : 2,
    shadowColor: themedColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: themedColors.isDarkMode ? 0.2 : 0.1,
    shadowRadius: 4,
  },
  statsCardGradient: {
    padding: 16,
    height: 100,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  statsIconCircle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statsNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  statsLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
  statsSubLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  section: {
    marginBottom: 24,
  },
  lastSection: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  sectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  viewAllText: {
    fontSize: 14,
    color: themedColors.primary,
    fontWeight: '500',
  },
  addButton: {
    backgroundColor: themedColors.primary,
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  animalsContainer: {
    paddingHorizontal: 16,
    gap: 12,
  },
  animalCard: {
    width: '100%',
    marginBottom: 12,
  },
  healthChecksList: {
    paddingHorizontal: 16,
  },
  recordsList: {
    paddingHorizontal: 16,
  },
  viewMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
    backgroundColor: themedColors.primaryLight,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  viewMoreText: {
    fontSize: 14,
    fontWeight: '500',
    color: themedColors.primary,
    marginRight: 4,
  },
  healthCheckActions: {
    flexDirection: 'row',
    marginTop: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: themedColors.isDarkMode ? 1 : 2,
    shadowColor: themedColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: themedColors.isDarkMode ? 0.2 : 0.1,
    shadowRadius: 4,
  },
  actionButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  farmSelectorContainer: {
    padding: 16,
    backgroundColor: themedColors.isDarkMode ? "" : themedColors.card, // Use card color for better theme consistency
    marginBottom:themedColors.isDarkMode ? 5 : -2,
    // Remove shadow properties
    shadowColor: 'transparent',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  
  },
  customDropdown: {
    // borderWidth: 2,
    // borderColor: '#FFA500', // Orange border color
    borderRadius: 8,
    backgroundColor: themedColors.card, // Use card background
    height: 48, // Fixed height for the dropdown
  },
  farmSelectorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  farmSelectorLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
  },
});
