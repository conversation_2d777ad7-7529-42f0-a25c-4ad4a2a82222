import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView 
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSettingsStore } from '@/store/settings-store';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook
import { Check } from 'lucide-react-native';

export default function LanguageSelectionScreen() {
  const router = useRouter();
  const { language, setLanguage } = useSettingsStore();
  const { playFeedback } = useAudioFeedback();
  const { t } = useTranslation();
  const themedColors = useThemeColors(); // Use the theme hook
  const styles = getStyles(themedColors); // Generate styles dynamically
  
  const languages = [
    { code: 'en', name: t('language.english') },
    { code: 'ur', name: t('language.urdu') },
    // { code: 'pa', name: t('language.punjabi') },
  ];
  
  const handleLanguageSelect = (code: string) => {
    setLanguage(code);
    playFeedback('success');
    router.back();
  };
  
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
            <Stack.Screen
        options={{
          title: t('language.selectLanguage'), // Use the translated title here
          headerBackTitle: t('common.back'), // Optional: translate back button too
        }}
      />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>{t('language.selectLanguage')}</Text>
        
        <View style={styles.languageList}>
          {languages.map((lang) => (
            <TouchableOpacity
              key={lang.code}
              style={[
                styles.languageItem,
                language === lang.code && styles.selectedLanguageItem
              ]}
              onPress={() => handleLanguageSelect(lang.code)}
            >
              <Text 
                style={[
                  styles.languageName,
                  language === lang.code && styles.selectedLanguageName
                ]}
              >
                {lang.name}
              </Text>
              
              {language === lang.code && (
                <Check size={20} color={themedColors.primary} />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollContent: {
    padding: 16,
    flexGrow: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 24,
  },
  languageList: {
    marginTop: 16,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: themedColors.card,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  selectedLanguageItem: {
    borderColor: themedColors.primary,
    backgroundColor: themedColors.primaryLight,
  },
  languageName: {
    fontSize: 18,
    color: themedColors.text,
  },
  selectedLanguageName: {
    fontWeight: '600',
    color: themedColors.primary,
  },
});