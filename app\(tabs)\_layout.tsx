

import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Tabs, useRouter, Redirect } from 'expo-router';
import { useAuthStore } from '@/store/auth-store';
import { useAnimalStore } from '@/store/animal-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { useFarmStore } from '@/store/farm-store';
import { Home, Settings, Bell, Cat, Building2, DollarSign, Calendar, HeartPulseIcon } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook

export default function TabsLayout() {
  const router = useRouter();

  const { user } = useAuthStore();
  const { animals } = useAnimalStore();
  const { healthChecks } = useHealthCheckStore();
  const { farms, fetchFarms } = useFarmStore();
  const [notificationCount, setNotificationCount] = useState(0);
  const [farmsChecked, setFarmsChecked] = useState(false);
  const [hasRedirected, setHasRedirected] = useState(false);
  const { t } = useTranslation();
  const themedColors = useThemeColors(); // Use the theme hook directly

  useEffect(() => {
    console.log('Tabs layout mounted');
  }, []);

  // Fetch farms when user is available
  useEffect(() => {
    if (user && !farmsChecked) {
      console.log('Fetching farms for user:', user.id);
      fetchFarms(user.id).finally(() => {
        setFarmsChecked(true);
      });
    }
  }, [user, farmsChecked, fetchFarms]);

  // Redirect users without farms to farms tab (only for owners and admins)
  useEffect(() => {
    if (farmsChecked && user && farms.length === 0 && !hasRedirected) {
      const isOwnerOrAdmin = user.role === 'owner' || user.role === 'admin';
      if (isOwnerOrAdmin) {
        console.log('User has no farms - redirecting to farms tab');
        setHasRedirected(true);
        router.replace('/(tabs)/farms');
      } else {
        console.log('User has no farms - tabs will be disabled except Settings');
      }
    }
  }, [farmsChecked, farms.length, user, hasRedirected, router]);

  // Check for overdue health checks
  useEffect(() => {
    if (animals.length === 0 || healthChecks.length === 0) return;

    const now = Date.now();
    let count = 0;

    // Count animals with overdue health checks
    animals.forEach(animal => {
      // Get the latest health check for this animal
      const animalHealthChecks = healthChecks
        .filter(check => check.animalId === animal.id)
        .sort((a, b) => b.date - a.date);

      if (animalHealthChecks.length === 0) {
        // No health checks yet, consider it overdue if animal was added more than 7 days ago
        if (now - animal.createdAt > 7 * 24 * 60 * 60 * 1000) {
          count++;
        }
      } else {
        const latestCheck = animalHealthChecks[0];
        // Check if next check date is in the past
        if (latestCheck.nextCheckDate && latestCheck.nextCheckDate < now) {
          count++;
        }
      }
    });

    // Count animals with abnormalities in their latest health check
    const animalsWithAbnormalities = animals.filter(animal => {
      const animalHealthChecks = healthChecks
        .filter(check => check.animalId === animal.id)
        .sort((a, b) => b.date - a.date);

      if (animalHealthChecks.length === 0) return false;
      return animalHealthChecks[0].abnormalities;
    }).length;

    setNotificationCount(count + animalsWithAbnormalities);
  }, [animals, healthChecks]);

  // Handle notification bell press
  const handleNotificationPress = () => {
    router.push('/alerts');
  };

  // Custom header right component with notification bell
  const NotificationBell = () => {
    return (
      <TouchableOpacity
        style={styles.notificationButton}
        onPress={handleNotificationPress}
      >
        <Bell size={24} color={themedColors.text} />
        {notificationCount > 0 && (
          <View style={[
            styles.notificationBadge, 
            { backgroundColor: themedColors.error || '#DC2626' } // Apply dynamic background color
          ]}>
            <Text style={styles.notificationCount}>
              {notificationCount > 99 ? '99+' : notificationCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Handle tab press - prevent navigation based on user role and farm availability
  const handleTabPress = (tabName: string) => {
    // Prevent navigation to farms for caretakers
    if (tabName === 'farms' && user?.role === 'caretaker') {
      return false;
    }

    // If user has no farms, only allow farms and settings tabs
    if (farmsChecked && farms.length === 0) {
      if (tabName !== 'farms' && tabName !== 'settings') {
        console.log(`Preventing navigation to ${tabName} - user has no farms`);
        Alert.alert(
          t('common.noFarmsTitle') || 'No Farms Available',
          t('common.noFarmsMessage') || 'Please add a farm to continue using this feature.',
          [{ text: t('common.ok') || 'OK' }]
        );
        return false;
      }
    }

    return true;
  };

  // Common screen options for both layouts
  const screenOptions = {
    tabBarActiveTintColor: themedColors.primary,
    tabBarInactiveTintColor: themedColors.textSecondary,
    tabBarStyle: {
      backgroundColor: themedColors.background,
      borderTopColor: themedColors.border,
      borderTopWidth: 1,
    },
    headerStyle: {
      backgroundColor: themedColors.background,
    },
    headerTintColor: themedColors.text,
    headerRight: () => <NotificationBell />,
  };

  // Colors for disabled tabs
  const disabledTabColor = themedColors.isDarkMode ? '#6B7280' : '#9CA3AF'; // Gray-500/400

  // Check if user is authenticated but not loaded yet
  if (!user) {
    console.log('No user in tabs layout, redirecting to auth');
    return <Redirect href="/(auth)" />;
  }

  const isOwnerOrAdmin = user?.role === 'owner' || user?.role === 'admin';

  // Determine if user has farms (used for styling disabled tabs)
  const userHasFarms = farmsChecked && farms.length > 0;

  // Tab layout for all users
  return (
    <Tabs screenOptions={screenOptions}>
      {/* Dashboard tab - always show but disable if no farms */}
      <Tabs.Screen
        name="index"
        options={{
          title: t('tabs.dashboard'),
          tabBarIcon: ({ color, size }) => (
            <Home
              size={size}
              color={userHasFarms ? color : disabledTabColor}
            />
          ),
          tabBarLabelStyle: {
            color: userHasFarms ? undefined : disabledTabColor,
            opacity: userHasFarms ? 1 : 0.3,
          },
          tabBarStyle: {
            backgroundColor: themedColors.background,
            borderTopColor: themedColors.border,
            borderTopWidth: 1,
          },
        }}
        listeners={{
          tabPress: (e) => {
            if (!handleTabPress('index')) {
              e.preventDefault();
            }
          }
        }}
      />
        {/* Always include the Farms tab but hide it for caretakers */}
        <Tabs.Screen
        name="farms"
        options={{
          title: t('farms.title'),
          tabBarIcon: ({ color, size }) => <Building2 size={size} color={color} />,
          // Explicitly set tabBarStyle for non-caretakers to ensure theming
          tabBarStyle: user?.role === 'caretaker'
            ? { display: 'none' }
            : {
                backgroundColor: themedColors.background,
                borderTopColor: themedColors.border,
                borderTopWidth: 1,
              },
          // If needed, you can also hide it from the tab bar completely:
          href: isOwnerOrAdmin ? '/farms' : null,
        }}
        listeners={{
          tabPress: (e) => {
            if (!handleTabPress('farms')) {
              e.preventDefault();
              // Optionally show an alert or message
            }
          }
        }}
      />
      {/* Animals tab - always show but disable if no farms */}
      <Tabs.Screen
        name="animals"
        options={{
          title: t('tabs.animals'),
          tabBarIcon: ({ color, size }) => (
            <Cat
              size={size}
              color={userHasFarms ? color : disabledTabColor}
            />
          ),
          tabBarLabelStyle: {
            color: userHasFarms ? undefined : disabledTabColor,
            opacity: userHasFarms ? 1 : 0.3,
          },
          tabBarStyle: {
            backgroundColor: themedColors.background,
            borderTopColor: themedColors.border,
            borderTopWidth: 1,
          },
        }}
        listeners={{
          tabPress: (e) => {
            if (!handleTabPress('animals')) {
              e.preventDefault();
            }
          }
        }}
      />
       {/* Pregnancy tab - always show but disable if no farms */}
       <Tabs.Screen
         name="pregnancy"
         options={{
           title: t('tabs.pregnancy'),
           tabBarIcon: ({ color }) => (
             <HeartPulseIcon
               color={userHasFarms ? color : disabledTabColor}
               size={24}
             />
           ),
           tabBarLabelStyle: {
             color: userHasFarms ? undefined : disabledTabColor,
             opacity: userHasFarms ? 1 : 0.3,
           },
           tabBarStyle: {
             backgroundColor: themedColors.background,
             borderTopColor: themedColors.border,
             borderTopWidth: 1,
           },
         }}
         listeners={{
           tabPress: (e) => {
             if (!handleTabPress('pregnancy')) {
               e.preventDefault();
             }
           }
         }}
       />

      {/* Expenses tab - always show but disable if no farms */}
      <Tabs.Screen
        name="expenses"
        options={{
          title: t('tabs.expenses'), // Make sure you have a translation for 'tabs.expenses'
          tabBarIcon: ({ color, size }) => (
            <DollarSign
              size={size}
              color={userHasFarms ? color : disabledTabColor}
            />
          ),
          tabBarLabelStyle: {
            color: userHasFarms ? undefined : disabledTabColor,
            opacity: userHasFarms ? 1 : 0.3,
          },
          tabBarStyle: {
            backgroundColor: themedColors.background,
            borderTopColor: themedColors.border,
            borderTopWidth: 1,
          },
        }}
        listeners={{
          tabPress: (e) => {
            if (!handleTabPress('expenses')) {
              e.preventDefault();
            }
          }
        }}
      />

      {/* Tasks tab - always show but disable if no farms */}
      <Tabs.Screen
        name="tasks"
        options={{
          title: t('tasks.title'),
          tabBarIcon: ({ color, size }) => (
            <Calendar
              size={size}
              color={userHasFarms ? color : disabledTabColor}
            />
          ),
          tabBarLabelStyle: {
            color: userHasFarms ? undefined : disabledTabColor,
            opacity: userHasFarms ? 1 : 0.3,
          },
          tabBarStyle: {
            backgroundColor: themedColors.background,
            borderTopColor: themedColors.border,
            borderTopWidth: 1,
          },
        }}
        listeners={{
          tabPress: (e) => {
            if (!handleTabPress('tasks')) {
              e.preventDefault();
            }
          }
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: t('tabs.settings'),
          tabBarIcon: ({ color, size }) => <Settings size={size} color={color} />,
          tabBarStyle: {
            backgroundColor: themedColors.background,
            borderTopColor: themedColors.border,
            borderTopWidth: 1,
          },
        }}
        listeners={{
          tabPress: (e) => {
            if (!handleTabPress('settings')) {
              e.preventDefault();
            }
          }
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  notificationButton: {
    padding: 8,
    marginRight: 8,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 2,
    right: 2, 
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  notificationCount: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
