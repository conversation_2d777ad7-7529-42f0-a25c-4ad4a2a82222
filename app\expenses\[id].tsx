import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  Image, 
  Alert,
  ActivityIndicator,
  StyleSheet
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useExpenseStore } from '@/store/expense-store';
import { useAuthStore } from '@/store/auth-store';
import { Expense, ExpenseCategory, PaymentMethod } from '@/types/expense';
import { format } from 'date-fns';
import {formatDate} from '@/utils/date-utils';
import { 
  ArrowLeft, 
  Edit, 
  Trash, 
  Calendar, 
  DollarSign, 
  Tag, 
  FileText, 
  Building2, 
  CreditCard,
  Cat,
  ExternalLink,
  Wheat,
  Pill,
  Syringe,
  Stethoscope,
  Wrench,
  Lightbulb,
  Users,
  Hammer,
  HelpCircle
} from 'lucide-react-native'; 
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook
import { useTranslation } from '@/hooks/useTranslation';
import { useToast } from '@/contexts/ToastContext';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import { doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { getCurrencySymbol } from '@/utils/currency-utils';

export default function ExpenseDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { user } = useAuthStore();
  const { expenses, deleteExpense, isLoading } = useExpenseStore();
  const { t, language } = useTranslation();
  const { showToast } = useToast();
  const { playFeedback } = useAudioFeedback();
  
  const [expense, setExpense] = useState<Expense | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';

  // Use the theme hook for colors
  const {
    background: screenBackgroundColor,
    card: cardBackgroundColor,
    text: textColor,
    textSecondary: secondaryTextColor,
    primary: primaryColor,
    border: borderColor,
    isDarkMode,
  } = useThemeColors();

  useEffect(() => {
    const fetchExpense = async () => {
      if (!id) return;

      // First check if the expense is in the store
      const storeExpense = expenses.find(e => e.id === id);
      if (storeExpense) {
        console.log('Found expense in store:', storeExpense);
        console.log('Currency:', storeExpense.currency, 'Amount:', storeExpense.amount);
        setExpense(storeExpense);
        setLoading(false);
        return;
      }

      // If not in store, we need to search through all farms to find the expense
      // This is not ideal, but necessary since we don't have the farmId in the URL
      try {
        // Try to find the expense by searching through the user's farms
        if (!user) {
          setError(t('common.notLoggedIn'));
          setLoading(false);
          return;
        }

        // Get user's farms first
        const farmsRef = collection(firestore, 'farms');
        const farmsQuery = query(farmsRef, where('ownerId', '==', user.id));
        const farmsSnapshot = await getDocs(farmsQuery);

        let foundExpense = null;

        // Search through each farm's expenses
        for (const farmDoc of farmsSnapshot.docs) {
          const expenseDoc = await getDoc(doc(firestore, 'farms', farmDoc.id, 'expenses', id));
          if (expenseDoc.exists()) {
            foundExpense = { id: expenseDoc.id, ...expenseDoc.data() } as Expense;
            console.log('Found expense in farm:', farmDoc.id, foundExpense);
            break;
          }
        }

        if (foundExpense) {
          setExpense(foundExpense);
        } else {
          setError(t('expense.notFound'));
        }
      } catch (err) {
        console.error('Error fetching expense:', err);
        setError(t('expense.fetchError'));
      } finally {
        setLoading(false);
      }
    };

    fetchExpense();
  }, [id, expenses]);
  
  const handleEdit = () => {
    router.push(`/expenses/${id}/edit`);
  };
  
  const handleDelete = () => {
    Alert.alert(
      t('expenses.deleteTitle'),
      t('expenses.deleteConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          onPress: async () => {
            try {
              if (!expense || !expense.farmId) {
                throw new Error('Missing expense or farmId');
              }
              
              // Pass both farmId and expenseId to the deleteExpense function
              await deleteExpense(expense.farmId, id);
              
              playFeedback('success');
              showToast({
                type: 'success',
                title: t('expenses.deleteSuccess'),
                message: t('expenses.deleteSuccessDetail'),
              });
              router.back();
            } catch (error) {
              playFeedback('error');
              Alert.alert(t('common.error'), t('expenses.deleteError'));
            }
          },
          style: 'destructive',
        },
      ]
    );
  };
  
  const getCategoryLabel = (category: ExpenseCategory) => {
    const labels = {
      [ExpenseCategory.ANIMAL_PURCHASE]: t('expenses.category.animalPurchase'),
      [ExpenseCategory.FEED]: t('expenses.category.feed'),
      [ExpenseCategory.MEDICATION]: t('expenses.category.medication'),
      [ExpenseCategory.VACCINATION]: t('expenses.category.vaccination'),
      [ExpenseCategory.VETERINARY]: t('expenses.category.veterinary'),
      [ExpenseCategory.EQUIPMENT]: t('expenses.category.equipment'),
      [ExpenseCategory.UTILITIES]: t('expenses.category.utilities'),
      [ExpenseCategory.LABOR]: t('expenses.category.labor'),
      [ExpenseCategory.MAINTENANCE]: t('expenses.category.maintenance'),
      [ExpenseCategory.OTHER]: t('expenses.category.other'),
    };
    return labels[category] || 'Unknown';
  };
  
  const getPaymentMethodLabel = (method: PaymentMethod) => {
    const labels = {
      [PaymentMethod.CASH]: t('expenses.paymentMethod.cash'),
      [PaymentMethod.CARD]: t('expenses.paymentMethod.card'),
      [PaymentMethod.BANK_TRANSFER]: t('expenses.paymentMethod.bankTransfer'),
      [PaymentMethod.OTHER]: t('expenses.paymentMethod.other'),
    };
    return labels[method] || 'Unknown';
  };
  
  const handleViewAnimal = () => {
    if (expense?.animalId) {
      router.push(`/animals/${expense.animalId}`);
    }
  };
  
  const handleViewFarm = () => {
    if (expense?.farmId) {
      router.push(`/farms/${expense.farmId}`);
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.loadingContainer, { backgroundColor: screenBackgroundColor }]}>
        <ActivityIndicator size="large" color={primaryColor} />
      </SafeAreaView>
    );
  }
  
  if (error || !expense) {
    return (
      <SafeAreaView style={[styles.errorContainer, { backgroundColor: screenBackgroundColor }]}>
        <Text style={[styles.errorText, { color: isDarkMode ? '#FF7B7B' : '#D32F2F'  }]}>{error || t('expenses.notFound')}</Text>
        <TouchableOpacity
          style={[styles.goBackButton, { backgroundColor: primaryColor }]}
          onPress={() => router.back()}
        >
          <Text style={styles.goBackButtonText}>{t('common.goBack')}</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: screenBackgroundColor }]}>
      <Stack.Screen
        options={{
          title: t('expenses.expenseDetails'),
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color={textColor} />
            </TouchableOpacity>
          ),
        }}
      />
      
      <ScrollView style={styles.scrollView}>
        {/* Amount Card */}
        <View style={[styles.amountCard, { backgroundColor: cardBackgroundColor }]}>
          <Text style={[styles.metadataText, { color: secondaryTextColor }]}>
            {t('expenses.amount')}
          </Text>
          <Text style={[styles.amountText, { color: textColor }]}>
            {getCurrencySymbol(expense.currency || 'PKR')} {(expense.amount || 0).toFixed(2)}
          </Text>
          <View style={[styles.categoryBadge, { backgroundColor: getCategoryBgColor(expense.category)},language === 'ur' && {flexDirection:'row-reverse'}]}>
            {/* Replace static Tag with dynamic category icon */}
            {expense.category === ExpenseCategory.ANIMAL_PURCHASE ? (
              <Cat size={16} color={getCategoryTextColor(expense.category)} />
            ) : expense.category === ExpenseCategory.FEED ? (
              <Wheat size={16} color={getCategoryTextColor(expense.category)} />
            ) : expense.category === ExpenseCategory.MEDICATION ? (
              <Pill size={16} color={getCategoryTextColor(expense.category)} />
            ) : expense.category === ExpenseCategory.VACCINATION ? (
              <Syringe size={16} color={getCategoryTextColor(expense.category)} />
            ) : expense.category === ExpenseCategory.VETERINARY ? (
              <Stethoscope size={16} color={getCategoryTextColor(expense.category)} />
            ) : expense.category === ExpenseCategory.EQUIPMENT ? (
              <Wrench size={16} color={getCategoryTextColor(expense.category)} />
            ) : expense.category === ExpenseCategory.UTILITIES ? (
              <Lightbulb size={16} color={getCategoryTextColor(expense.category)} />
            ) : expense.category === ExpenseCategory.LABOR ? (
              <Users size={16} color={getCategoryTextColor(expense.category)} />
            ) : expense.category === ExpenseCategory.MAINTENANCE ? (
              <Hammer size={16} color={getCategoryTextColor(expense.category)} />
            ) : (
              <HelpCircle size={16} color={getCategoryTextColor(expense.category)} />
            )}
            <Text style={[styles.categoryText, { color: getCategoryTextColor(expense.category) }, language === 'ur' && {marginRight:5}]}>
              {getCategoryLabel(expense.category)}
            </Text>
          </View>
        </View>
        
        {/* Details Card */}
        <View style={[styles.detailsCard, { backgroundColor: cardBackgroundColor }]}>
        {expense.farmName && (
            <TouchableOpacity 
              style={[
                styles.detailRow,
                language === 'ur' && { flexDirection: 'row-reverse' },
              ]}
              onPress={handleViewFarm}
            >
              <Building2 size={18} color={secondaryTextColor}  />
              <View style={[styles.detailContent, language === 'ur' && {marginRight:8}]}>
                <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>{t('farms.farm')}</Text>
                <View style={[styles.linkContainer, language === 'ur' && { flexDirection: 'row-reverse'}]}>
                  <Text style={[styles.detailText, { color: textColor }, language === 'ur' && styles.urduText]}>
                    {expense.farmName}
                  </Text>
                  <ExternalLink size={16} color={primaryColor} style={styles.linkIcon} />
                </View>
              </View>
            </TouchableOpacity>
          )}
          
          {expense.animalName && (
            <TouchableOpacity 
              style={[
                styles.detailRow,
                language === 'ur' && { flexDirection: 'row-reverse' },
              ]}
              onPress={handleViewAnimal}
            >
              <Cat size={18} color={secondaryTextColor} />
              <View style={[styles.detailContent, language === 'ur' && {marginRight:5}]}>
                <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>{t('animals.animal')}</Text>
                <View style={[styles.linkContainer, language === 'ur' && { justifyContent: 'flex-end'}]}>
                  <Text style={[styles.detailText, { color: textColor }]}>
                    {expense.animalName}
                  </Text>
                  <ExternalLink size={16} color={primaryColor} style={styles.linkIcon} />
                </View>
              </View>
            </TouchableOpacity>
          )}
          <View style={[
                styles.detailRow,
                language === 'ur' && { flexDirection: 'row-reverse' },
              ]}>
            <CreditCard size={18} color={secondaryTextColor} />
            <View style={[styles.detailContent, language === 'ur' && {marginRight:8}]}>
              <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>{t('expenses.paymentMethod.label')}</Text>
              <Text style={[styles.detailText, { color: textColor }, language === 'ur' && styles.urduText]}>
                {getPaymentMethodLabel(expense.paymentMethod)}
              </Text>
            </View>
          </View>
          <View style={[
                styles.detailRow,
                language === 'ur' && { flexDirection: 'row-reverse' },
              ]}>
            <Calendar size={18} color={secondaryTextColor} />
            <View style={[styles.detailContent, language === 'ur' && {marginRight:8}]}>
              <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>{t('expenses.date')}</Text>
              <Text style={[styles.detailText, { color: textColor }, language === 'ur' && styles.urduText]}>
                {formatDate(expense.date, locale)}
              </Text>
            </View>
          </View>
          
          
          
          
          {expense.description && (
            <View style={[
                styles.descriptionContainer,
                language === 'ur' && { alignItems: 'flex-end' },
              ]}>
              <View style={[styles.descriptionHeader, language === 'ur' && { flexDirection: 'row-reverse', gap:7 }]}>
                <FileText size={18} color={secondaryTextColor} />
                <Text style={[styles.descriptionTitle, { color: textColor }]}>
                  {t('common.description')}
                </Text>
              </View>
              <Text style={[styles.descriptionText, { color: secondaryTextColor }]}>
                {expense.description} 
              </Text>
            </View>
          )}
        </View>
        
        {/* Receipt Image */}
        {expense.receiptImage && (
          <View style={[styles.receiptCard, { backgroundColor: cardBackgroundColor }]}>
            <Text style={[
                styles.receiptTitle, { color: textColor }, language === 'ur' && styles.urduText
              ]}>
              {t('expenses.receipt')}
            </Text>
            <Image
              source={{ uri: expense.receiptImage }}
              style={styles.receiptImage}
              resizeMode="contain"
            />
          </View>
        )}
        
        {/* Metadata */}
        <View style={[styles.metadataCard, { backgroundColor: cardBackgroundColor }]}>
          <Text style={[styles.metadataText, { color: secondaryTextColor }, language === 'ur' && styles.urduText]}>
            {t('common.created')}: {formatDate(expense.createdAt, locale)}
          </Text>
          {expense.updatedAt !== expense.createdAt && (
            <Text style={[styles.metadataText, { color: secondaryTextColor }, language === 'ur' && styles.urduText]}>
              {t('common.updated')}: {formatDate(expense.updatedAt, locale)}
            </Text>
          )}
        </View>
      </ScrollView>
      
      {/* Action Buttons */}
      <View style={[styles.actionButtons, { backgroundColor: cardBackgroundColor, borderTopColor: borderColor }]}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton, { backgroundColor: primaryColor }]}
          onPress={handleEdit}
        >
          <Edit size={20} color="white" />
          <Text style={[styles.actionButtonText, language === 'ur' && styles.urduText]}>{t('common.edit')}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={handleDelete} // Delete button color is usually a distinct error color
        >
          <Trash size={20} color="white" />
          <Text style={[styles.actionButtonText, language === 'ur' && styles.urduText]}>{t('common.delete')}</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amountCard: {
    // backgroundColor will be set dynamically
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    alignItems: 'center',
  },
  amountText: {
    fontSize: 32,
    fontWeight: 'bold',
    // color will be set dynamically (textColor)
    marginBottom: 12,
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(231, 76, 60, 0.15)', // Default color for MEDICATION
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  categoryText: { //This style is used for category text in amount card
    marginLeft: 6,
    color: '#E74C3C', // Default color for MEDICATION
    fontSize: 14,
    fontWeight: '600',
  },
  detailsCard: {
    // backgroundColor will be set dynamically
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    paddingVertical: 4,
  },
  detailContent: {
    marginLeft: 12,
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    // color will be set dynamically (secondaryTextColor)
    marginBottom: 2,
  },
  detailText: {
    // color will be set dynamically (textColor)
    fontSize: 16,
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  linkIcon: {
    marginLeft: 4,
  },
  descriptionContainer: {
    marginTop: 8,
  },
  descriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  descriptionTitle: {
    marginLeft: 12,
    // color will be set dynamically (textColor)
    fontSize: 16,
    fontWeight: '600',
  },
  descriptionText: {
    marginLeft: 32,
    // color will be set dynamically (secondaryTextColor)
    fontSize: 15,
    lineHeight: 22,
  },
  receiptCard: {
    // backgroundColor will be set dynamically
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  receiptTitle: {
    // color will be set dynamically (textColor)
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  receiptImage: {
    width: '100%',
    height: 240,
    borderRadius: 8,
  },
  metadataCard: {
    // backgroundColor will be set dynamically
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metadataText: {
    // color will be set dynamically (secondaryTextColor)
    fontSize: 16,
    marginBottom: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    // color will be set dynamically
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  goBackButton: {
    // backgroundColor will be set dynamically
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  goBackButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    // backgroundColor will be set dynamically
    borderTopWidth: 1,
    // borderTopColor will be set dynamically
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 6,
  },
  editButton: {
    // backgroundColor will be set dynamically
  },
  deleteButton: {
    backgroundColor: '#D32F2F', // Standard error red, can be themed if errorColor is in hook
  },
  actionButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
    fontSize: 16,
  },
  urduText: {
    fontFamily: 'Jameel Noori Nastaleeq',
    textAlign: 'right',
  },
});

// Get category background color
const getCategoryBgColor = (category: ExpenseCategory) => {
  switch (category) {
    case ExpenseCategory.ANIMAL_PURCHASE:
      return 'rgba(142, 68, 173, 0.15)';
    case ExpenseCategory.FEED:
      return 'rgba(39, 174, 96, 0.15)';
    case ExpenseCategory.MEDICATION:
      return 'rgba(231, 76, 60, 0.15)';
    case ExpenseCategory.VACCINATION:
      return 'rgba(52, 152, 219, 0.15)';
    case ExpenseCategory.VETERINARY:
      return 'rgba(46, 204, 113, 0.15)';
    case ExpenseCategory.EQUIPMENT:
      return 'rgba(149, 165, 166, 0.15)';
    case ExpenseCategory.UTILITIES:
      return 'rgba(243, 156, 18, 0.15)';
    case ExpenseCategory.LABOR:
      return 'rgba(26, 188, 156, 0.15)';
    case ExpenseCategory.MAINTENANCE:
      return 'rgba(52, 73, 94, 0.15)';
    case ExpenseCategory.OTHER:
    default:
      return 'rgba(127, 140, 141, 0.15)';
  }
};

// Get category text color
const getCategoryTextColor = (category: ExpenseCategory) => {
  switch (category) {
    case ExpenseCategory.ANIMAL_PURCHASE:
      return '#8E44AD';
    case ExpenseCategory.FEED:
      return '#27AE60';
    case ExpenseCategory.MEDICATION:
      return '#E74C3C';
    case ExpenseCategory.VACCINATION:
      return '#3498DB';
    case ExpenseCategory.VETERINARY:
      return '#2ECC71';
    case ExpenseCategory.EQUIPMENT:
      return '#95A5A6';
    case ExpenseCategory.UTILITIES:
      return '#F39C12';
    case ExpenseCategory.LABOR:
      return '#1ABC9C';
    case ExpenseCategory.MAINTENANCE:
      return '#34495E';
    case ExpenseCategory.OTHER:
    default:
      return '#7F8C8D';
  }
};
