
import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import { Platform } from 'react-native';

// Firebase configuration for Testing
// const firebaseConfig = {
//   apiKey: "AIzaSyAbeTfQRwefwaSgRsHsXp8i6KrLwz_3zH4",
//   authDomain: "animal-health-tracker-f8475.firebaseapp.com",
//   projectId: "animal-health-tracker-f8475",
//   storageBucket: "animal-health-tracker-f8475.firebasestorage.app",
//   messagingSenderId: "286954026731",
//   appId: "1:286954026731:web:3c52e8d96fdfc7db12b0ae",
//   measurementId: "G-8WL8GQ3RC4"
// };
// // Firebase configuration For APK 
//gs://animalhealth-d3486.firebasestorage.app
// const firebaseConfig = {
//   apiKey: "AIzaSyAefIViL3Q00FuOUlXBV1_LvefmLyNlXSA",
//   authDomain: "animalhealth-d3486.firebaseapp.com",
//   projectId: "animalhealth-d3486",
//   storageBucket: "animalhealth-d3486.firebasestorage.app",
//   // storageBucket: "animalhealth-d3486.appspot.com",
//   messagingSenderId: "514739897962",
//   appId: "1:514739897962:web:4423bb419eb51a68e9ac13",
//   measurementId: "G-MWSWSVYHB5"
// };
// Firebase configuration by malik bro
const firebaseConfig = {
  apiKey: "AIzaSyDDKOb-qvTF2516cWV_9TEAgk5pukmozjc",
  authDomain: "kissandost-9570f.firebaseapp.com",
  databaseURL: "https://kissandost-9570f-default-rtdb.firebaseio.com",
  projectId: "kissandost-9570f",
  storageBucket: "kissandost-9570f.firebasestorage.app",
  messagingSenderId: "400828673471",
  appId: "1:400828673471:web:ef962bc0150b3bb0e3c50a",
  measurementId: "G-Z91G06JJVD"
};
 
// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = getAuth(app);
const firestore = getFirestore(app);
const storage = getStorage(app);
const functions = getFunctions(app);

// Set up custom action code settings for email verification
// This ensures that email verification and password reset work properly
auth.useDeviceLanguage();

// Configure the action URL for password reset and email verification
// This is needed for proper email verification and password reset functionality
const actionCodeSettings = {
  // URL you want to redirect back to after email verification
  // Must be in the authorized domains list in the Firebase Console
  url: 'https://kissandost-9570f.firebaseapp.com/verify-email.html',
  // This must be true for mobile apps
  handleCodeInApp: true,
  // iOS app settings
  iOS: {
    bundleId: 'com.animalhealth.tracker'
  },
  // Android app settings
  android: {
    packageName: 'com.animalhealth.tracker',
    installApp: true,
    minimumVersion: '12'
  },
  // Dynamic link settings (optional)
  // dynamicLinkDomain: 'animalhealth.page.link'
};

// For development, you can connect to Firebase emulators
// Uncomment these lines if you're using Firebase emulators
// if (__DEV__) {
//   connectAuthEmulator(auth, 'http://localhost:9099');
//   connectFirestoreEmulator(firestore, 'localhost', 8080);
//   connectStorageEmulator(storage, 'localhost', 9199);
//   connectFunctionsEmulator(functions, 'localhost', 5001);
// }

export { app, auth, firestore, storage, functions, actionCodeSettings };