import { useColorScheme } from 'react-native';
import { useSettingsStore } from '@/store/settings-store';
import { colors as appBaseColors } from '@/constants/colors';

export const useThemeColors = () => {
  const systemColorScheme = useColorScheme();
  const { darkMode } = useSettingsStore();
  const isDarkMode = darkMode !== null ? darkMode : systemColorScheme === 'dark';

  const themedColors = {
    isDarkMode,
    primary: isDarkMode ? '#A78B71' : appBaseColors.primary, // Dark gold vs. Light theme primary
    primaryDark: isDarkMode ? '#8B6F56' : (appBaseColors.primaryDark || '#8B6F56'), // Darker shade of primary
    background: isDarkMode ? '#1F2937' : appBaseColors.background, // Dark gray-blue vs. Light theme background
    card: isDarkMode ? '#374151' : appBaseColors.card, // Darker gray-blue vs. Light theme card
    text: isDarkMode ? '#F9FAFB' : appBaseColors.text, // Light gray vs. Light theme text
    textSecondary: isDarkMode ? '#D1D5DB' : appBaseColors.textSecondary, // Lighter gray vs. Light theme secondary text
    border: isDarkMode ? '#4B5563' : appBaseColors.border, // Medium gray vs. Light theme border
    success: isDarkMode ? '#34D399' : (appBaseColors.success || '#16A34A'), // Lighter green for dark, fallback for light
    successLight: isDarkMode ? 'rgba(52, 211, 153, 0.2)' : 'rgba(22, 163, 74, 0.2)', // Light success background
    error: isDarkMode ? '#F87171' : (appBaseColors.error || '#DC2626'),     // Lighter red for dark, fallback for light
    errorLight: isDarkMode ? 'rgba(248, 113, 113, 0.2)' : 'rgba(220, 38, 38, 0.2)', // Light error background
    primaryLight: isDarkMode ? 'rgba(167, 139, 113, 0.2)' : (appBaseColors.primaryLight || 'rgba(79, 70, 229, 0.2)'), // Light version of primary color
    warning: isDarkMode ? '#FBBF24' : '#F59E0B',    // Amber (NEW)
    textContrast: isDarkMode ? '#FFFFFF' : '#000000', // White for dark, black for light
    borderLight: '#2D3748', // Define for dark mode (e.g., a slightly lighter shade than 'border')
  };

  return { 
    ...themedColors, 
    isDarkMode // Return isDarkMode as well, as it's useful for conditional styling
  };
};