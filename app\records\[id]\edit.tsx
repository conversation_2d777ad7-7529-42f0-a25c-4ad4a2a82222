import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAnimalStore } from '@/store/animal-store';
import { useRecordStore } from '@/store/record-store';

import DatePickerInput from '@/components/DatePickerInput';
import Input from '@/components/Input';
import AnimalSearchDropdown from '@/components/AnimalSearchDropdown';
import SpeciesFilterRow from '@/components/SpeciesFilterRow';
import GenericDropdown, { DropdownItem } from '@/components/GenericDropdown';
import { getRecordOptions } from '@/constants/recordOptions';
import {
  Calendar,
  ArrowLeft,
  FileText,
  Stethoscope,
  Syringe,
  Pill,
  Sciss<PERSON>,
  Baby,
  Save,
  FileType,
  Clock,
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { AnimalRecord, RecordType } from '@/types/record';
import { HEALTH_CHECK_INTERVAL, VACCINATION_INTERVAL } from '@/utils/healthStatus';
import LoadingIndicator from '@/components/LoadingIndicator';
import EmptyState from '@/components/EmptyState';

// Record type dropdown items
const getRecordTypeItems = (t: (key: string) => string): DropdownItem[] => [
  {
    id: RecordType.VACCINATION,
    label: t('records.vaccination'),
    icon: <Syringe size={32} color={colors.primary} />,
    description: t('records.vaccinationsDescription')
  },
  {
    id: RecordType.MEDICATION,
    label: t('records.medications'),
    icon: <Pill size={32} color={colors.primary} />,
    description: t('records.medicationsDescription')
  },
  {
    id: RecordType.SURGERY,
    label: t('records.surgery'),
    icon: <Scissors size={32} color={colors.primary} />,
    description: t('records.surgeriesDescription')
  },
  {
    id: RecordType.GENERAL,
    label: t('records.checkup'),
    icon: <Stethoscope size={32} color={colors.primary} />,
    description: t('records.checkupsDescription')
  },
  {
    id: RecordType.BIRTH,
    label: t('records.births'),
    icon: <Baby size={32} color={colors.primary} />,
    description: t('records.birthsDescription')
  },
];

// Helper function to get the appropriate label for record details based on record type
const getRecordDetailsLabel = (recordType: string, t: (key: string) => string): string => {
  switch (recordType) {
    case RecordType.VACCINATION:
      return t('records.vaccineName');
    case RecordType.MEDICATION:
      return t('records.medicineName');
    case RecordType.SURGERY:
      return t('records.surgeryType');
    case RecordType.BIRTH:
      return t('records.birthType');
    case RecordType.GENERAL:
      return t('records.checkupType');
    default:
      return t('records.details');
  }
};

export default function EditRecordScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const id = params.id as string;
  const animalId = params.animalId as string;

  const { animals } = useAnimalStore();
  const { getRecordById, updateRecord, isLoading, fetchRecords } = useRecordStore();
  const { t, language } = useTranslation();

  const [record, setRecord] = useState<AnimalRecord | undefined>();
  const [selectedAnimal, setSelectedAnimal] = useState('');
  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(null);
  const [recordDate, setRecordDate] = useState(new Date());
  const [recordType, setRecordType] = useState<string>('');
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [nextDueDate, setNextDueDate] = useState<Date | null>(null);
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [recordOptions, setRecordOptions] = useState<DropdownItem[]>([]);
  const [recordOptionItems, setRecordOptionItems] = useState<DropdownItem[]>([]);
  const [loading, setLoading] = useState(true);

  // Load record data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // First fetch all records to ensure we have the latest data
        await fetchRecords();

        if (id) {
          const recordData = getRecordById(id);
          if (recordData) {
            setRecord(recordData);
            setSelectedAnimal(recordData.animalId);
            setRecordDate(new Date(recordData.date));
            setRecordType(recordData.type);

            // Extract notes from description
            const description = recordData.description || '';
            setNotes(description);

            // Check if there's a next due date in the description
            if (description.includes('Next vaccination due:') || description.includes('Next check-up due:')) {
              const dateMatch = description.match(/Next (vaccination|check-up) due: ([A-Za-z]+ \d+, \d+)/);
              if (dateMatch && dateMatch[2]) {
                try {
                  const dueDate = new Date(dateMatch[2]);
                  if (!isNaN(dueDate.getTime())) {
                    setNextDueDate(dueDate);
                  }
                } catch (e) {
                  console.error('Error parsing due date:', e);
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error loading record:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, getRecordById, fetchRecords]);

  // Update species when animal changes
  useEffect(() => {
    if (selectedAnimal) {
      const animal = animals.find(a => a.id === selectedAnimal);
      if (animal) {
        setSelectedSpecies(animal.species);
      }
    }
  }, [selectedAnimal, animals]);

  // Update record options when species and record type change
  useEffect(() => {
    if (selectedSpecies && recordType) {
      const options = getRecordOptions(selectedSpecies, recordType);

      // Convert to dropdown items
      const items: DropdownItem[] = options.map(option => ({
        id: option.id,
        label: option.label,
        description: option.description
      }));

      setRecordOptions(items);
      setRecordOptionItems(items);

      // Try to find the selected option from the title
      if (record && record.title) {
        const titleParts = record.title.split(':');
        if (titleParts.length > 1) {
          const optionLabel = titleParts[1].trim();
          const matchingOption = items.find(item => item.label === optionLabel);
          if (matchingOption) {
            setSelectedOption(matchingOption.id);
          }
        }
      }
    }
  }, [selectedSpecies, recordType, record]);

  // Set next due date based on record type
  const handleRecordTypeChange = (type: string) => {
    setRecordType(type);

    // Set default next due date based on record type
    if (type === RecordType.VACCINATION) {
      const nextDate = new Date();
      nextDate.setDate(nextDate.getDate() + VACCINATION_INTERVAL);
      setNextDueDate(nextDate);
    } else if (type === RecordType.GENERAL) {
      const nextDate = new Date();
      nextDate.setDate(nextDate.getDate() + HEALTH_CHECK_INTERVAL);
      setNextDueDate(nextDate);
    } else {
      setNextDueDate(null);
    }
  };

  const handleSubmit = async () => {
    if (!selectedAnimal) {
      Alert.alert('Error', 'Please select an animal');
      return;
    }

    if (!recordType) {
      Alert.alert('Error', 'Please select a record type');
      return;
    }

    setIsSubmitting(true);

    try {
      // Find the selected option details
      const selectedOptionDetails = recordOptions.find(option => option.id === selectedOption);

      // Prepare record data
      const recordData: Partial<AnimalRecord> = {
        animalId: selectedAnimal,
        date: recordDate.getTime(),
        type: recordType as RecordType,
        title: selectedOptionDetails
          ? `${recordType}: ${selectedOptionDetails.label}`
          : `${recordType} record`,
        description: notes,
      };

      // Add next due date information to description if applicable
      if (nextDueDate && (recordType === RecordType.VACCINATION || recordType === RecordType.GENERAL)) {
        const formattedNextDate = nextDueDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        });

        recordData.description += `\n\nNext ${recordType === RecordType.VACCINATION ? 'vaccination' : 'check-up'} due: ${formattedNextDate}`;
      }

      // Update the record
      await updateRecord(id, recordData);

      // Show success message
      Alert.alert(
        'Success',
        'Record updated successfully',
        [
          {
            text: 'OK',
            onPress: () => router.back()
          }
        ]
      );
    } catch (error) {
      console.error('Error updating record:', error);
      Alert.alert('Error', 'Failed to update record');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || loading) {
    return <LoadingIndicator fullScreen message="Loading record..." />;
  }

  if (!record) {
    return (
      <EmptyState
        title="Record Not Found"
        message="The health record you're trying to edit doesn't exist or has been deleted."
        actionLabel="Go Back"
        onAction={() => router.back()}
      />
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('records.editRecord'),
        headerTitleAlign: language === 'ur' ? 'center' : 'left',
        headerTitleStyle: { fontWeight: 'bold' },
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
        )
      }} />

      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          {/* Animal Selection */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={colors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('animals.selectedAnimal')}</Text>
            </View>
            <AnimalSearchDropdown
              placeholder={t('animals.selectAnimal')}
              animals={animals}
              value={selectedAnimal}
              onSelect={setSelectedAnimal}
            />
          </View>

          {/* Record Date */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Calendar size={24} color={colors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('records.date')}</Text>
            </View>
            <DatePickerInput
              value={recordDate}
              onChange={setRecordDate}
              maximumDate={new Date()}
            />
          </View>

          {/* Record Type */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={colors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('records.recordType')}</Text>
            </View>
            <GenericDropdown
              placeholder={t('records.selectRecordType')}
              items={getRecordTypeItems(t)}
              value={recordType}
              onSelect={(id: string) => handleRecordTypeChange(id as RecordType)}
              modalTitle={t('records.selectRecordType')}
              searchPlaceholder={t('records.searchRecordTypes')}
              renderIcon={<FileType size={24} color={colors.textSecondary} />}
            />
          </View>

          {/* Record Options - only shown when both animal type and record type are selected */}
          {selectedSpecies && recordType && recordOptionItems.length > 0 && (
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <FileText size={24} color={colors.primary} />
                <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{getRecordDetailsLabel(recordType, t)}</Text>
              </View>
              <GenericDropdown
                placeholder={t(`records.select${recordType.charAt(0).toUpperCase() + recordType.slice(1)}Details`)}
                items={recordOptionItems.map(item => ({
                  ...item,
                  label: language === 'ur' ? t(`records.${item.id}`) || item.label : item.label,
                  description: language === 'ur' ? t(`records.${item.id}Description`) || item.description : item.description
                }))}
                value={selectedOption}
                onSelect={setSelectedOption}
                modalTitle={t(`records.select${recordType.charAt(0).toUpperCase() + recordType.slice(1)}Details`)}
                searchPlaceholder={t(`records.search${recordType.charAt(0).toUpperCase() + recordType.slice(1)}Details`)}
                renderIcon={<FileText size={24} color={colors.textSecondary} />}
              />
            </View>
          )}

          {/* Next Due Date - only shown for vaccination and general check-up records */}
          {(recordType === RecordType.VACCINATION || recordType === RecordType.GENERAL) && (
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <Clock size={24} color={colors.primary} />
                <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                  {recordType === RecordType.VACCINATION ? t('records.nextVaccinationDue') : t('records.nextCheckupDue')}
                </Text>
              </View>
              <DatePickerInput
                value={nextDueDate || new Date()}
                onChange={setNextDueDate}
                minimumDate={new Date()}
              />
            </View>
          )}

          {/* Notes */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={colors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('records.notes')}</Text>
            </View>
            <Input
              value={notes}
              onChangeText={setNotes}
              placeholder={t('records.addNotes')}
              multiline
              numberOfLines={4}
              style={styles.textArea}
            />
          </View>

          <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <LinearGradient
              colors={[colors.primary, colors.primaryDark]}
              style={styles.saveButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Save size={24} color="white" />
              <Text style={[styles.saveButtonText, language === 'ur' ? styles.urduText : null]}>{t('records.updateRecord')}</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginLeft: 8,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  saveButton: {
    marginTop: 16,
    marginBottom: 32,
    borderRadius: 8,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});
