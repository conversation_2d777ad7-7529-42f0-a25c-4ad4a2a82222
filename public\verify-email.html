<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - Animal Health Tracker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #2563eb;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }
        
        .message {
            font-size: 16px;
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 32px;
        }
        
        .status {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-weight: 500;
        }
        
        .status.loading {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #34d399;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #f87171;
        }
        
        .button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.2s;
        }
        
        .button:hover {
            background: #1d4ed8;
        }
        
        .button.secondary {
            background: #6b7280;
        }
        
        .button.secondary:hover {
            background: #4b5563;
        }
        
        .spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #2563eb;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🐄</div>
        <h1 class="title">Email Verification</h1>
        
        <div id="loading-state">
            <div class="spinner"></div>
            <div class="status loading">
                Verifying your email address...
            </div>
        </div>
        
        <div id="success-state" class="hidden">
            <div class="status success">
                ✅ Email verified successfully!
            </div>
            <p class="message">
                Your email has been verified. You can now set up your password to complete your account setup.
            </p>
            <a href="#" id="setup-password-btn" class="button">Set Up Password</a>
            <br><br>
            <a href="#" id="login-btn" class="button secondary">Back to Login</a>
        </div>
        
        <div id="error-state" class="hidden">
            <div class="status error">
                ❌ Verification failed
            </div>
            <p class="message" id="error-message">
                We were unable to verify your email. The link may have expired or already been used.
            </p>
            <a href="#" id="back-btn" class="button secondary">Back to Login</a>
        </div>
    </div>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, applyActionCode, checkActionCode } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDDKOb-qvTF2516cWV_9TEAgk5pukmozjc",
            authDomain: "kissandost-9570f.firebaseapp.com",
            databaseURL: "https://kissandost-9570f-default-rtdb.firebaseio.com",
            projectId: "kissandost-9570f",
            storageBucket: "kissandost-9570f.firebasestorage.app",
            messagingSenderId: "400828673471",
            appId: "1:400828673471:web:ef962bc0150b3bb0e3c50a",
            measurementId: "G-Z91G06JJVD"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const mode = urlParams.get('mode');
        const oobCode = urlParams.get('oobCode');
        const continueUrl = urlParams.get('continueUrl');

        // DOM elements
        const loadingState = document.getElementById('loading-state');
        const successState = document.getElementById('success-state');
        const errorState = document.getElementById('error-state');
        const errorMessage = document.getElementById('error-message');
        const setupPasswordBtn = document.getElementById('setup-password-btn');
        const loginBtn = document.getElementById('login-btn');
        const backBtn = document.getElementById('back-btn');

        // Handle email verification
        async function handleEmailVerification() {
            try {
                if (!oobCode) {
                    throw new Error('Invalid verification link');
                }

                if (mode === 'verifyEmail') {
                    // Check the action code first to get user info
                    const info = await checkActionCode(auth, oobCode);
                    const userEmail = info.data.email;

                    // Apply the action code to verify the email
                    await applyActionCode(auth, oobCode);

                    // Show success state
                    loadingState.classList.add('hidden');
                    successState.classList.remove('hidden');

                    // Set up button links
                    setupPasswordBtn.href = `animalhealth://setup-password?email=${encodeURIComponent(userEmail)}`;
                    loginBtn.href = 'animalhealth://login';
                } else {
                    throw new Error('Invalid verification mode');
                }
            } catch (error) {
                console.error('Email verification error:', error);
                
                let message = 'We were unable to verify your email. Please try again.';
                
                if (error.code === 'auth/expired-action-code') {
                    message = 'This verification link has expired. Please request a new one.';
                } else if (error.code === 'auth/invalid-action-code') {
                    message = 'This verification link is invalid or has already been used.';
                }

                // Show error state
                loadingState.classList.add('hidden');
                errorState.classList.remove('hidden');
                errorMessage.textContent = message;

                backBtn.href = 'animalhealth://login';
            }
        }

        // Start verification process
        handleEmailVerification();
    </script>
</body>
</html>
