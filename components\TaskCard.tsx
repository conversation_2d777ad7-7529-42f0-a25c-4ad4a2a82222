import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Calendar, Clock, AlertCircle, CheckCircle, MapPin, User } from 'lucide-react-native';
import { Task, TaskStatus, TaskPriority } from '@/types/task';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import {formatDateToYearsandMonths, formatDate} from '../utils/date-utils';

interface TaskCardProps {
  task: Task;
  onPress: () => void;
}

const TaskCard: React.FC<TaskCardProps> = ({ task, onPress }) => {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);
  // Ensure task is valid
  if (!task || typeof task !== 'object') {
    console.error('Invalid task provided to TaskCard:', task);
    return null;
  }
  
  // Safely access task properties with fallbacks
  const taskId = task.id || 'unknown';
  const taskTitle = task.title || 'Untitled Task';
  const taskStatus = task.status || TaskStatus.PENDING;
  const taskDueDate = task.due_date || Date.now();
  const taskPriority = task.priority || TaskPriority.MEDIUM;
  
 
  const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';
  try {
    const isCompleted = taskStatus === TaskStatus.COMPLETED;
    
    // Format date
    // const formatDate = (timestamp: number): string => {
    //   return new Date(timestamp).toLocaleDateString();
    // };
    
    // Get priority color
    const getPriorityColor = (priority: TaskPriority): string => {
      switch (priority) {
        case TaskPriority.HIGH:
          return themedColors.error;
        case TaskPriority.MEDIUM:
          return themedColors.warning;
        case TaskPriority.LOW:
          return themedColors.success;
        default:
          return themedColors.primary;
      }
    };
    
    // Get priority label
    const getPriorityLabel = (priority: TaskPriority): string => {
      switch (priority) {
        case TaskPriority.HIGH:
          return t('tasks.priorities.high');
        case TaskPriority.MEDIUM:
          return t('tasks.priorities.medium');
        case TaskPriority.LOW:
          return t('tasks.priorities.low');
        default:
          return '';
      }
    };
    
    return (
      <TouchableOpacity 
        style={[
          styles.container,
          isCompleted && styles.completedContainer
        ]} 
        onPress={onPress}
        activeOpacity={0.7}
        testID={`task-card-${taskId}`}
      >
        {/* Status indicator */}
        <View 
          style={[
            styles.statusIndicator,
            { backgroundColor: isCompleted ? themedColors.success : getPriorityColor(taskPriority) }
          ]} 
        />
        
        <View style={styles.content}>
          {/* Title and status */}
          <View style={styles.header}>
            <Text 
              style={[
                styles.title,
                isCompleted && styles.completedText
              ]}
              numberOfLines={1}
            >
              {taskTitle}
            </Text>
            
            {isCompleted ? (
              <View style={styles.statusBadge}>
                <CheckCircle size={14} color={themedColors.success} />
                <Text style={[styles.statusText, { color: themedColors.success }]}>
                  {t('tasks.status.completed')}
                </Text>
              </View>
            ) : (
              <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(taskPriority) + '20' }]}>
                <AlertCircle size={14} color={getPriorityColor(taskPriority)} />
                <Text style={[styles.priorityText, { color: getPriorityColor(taskPriority) }]}>
                  {getPriorityLabel(taskPriority)}
                </Text>
              </View>
            )}
          </View>
          
          {/* Description */}
          {task.description && (
            <Text 
              style={[
                styles.description,
                isCompleted && styles.completedText
              ]}
              numberOfLines={2}
            >
              {task.description}
            </Text>
          )}
          
          {/* Details */}
          <View style={[styles.detailsContainer, language === 'ur' && { flexDirection: 'row-reverse' }]}>
            {/* Due date */}
            <View style={[styles.detailItem, language === 'ur' && { flexDirection: 'row-reverse', gap: 5 }]}>
              <Calendar size={14} color={isCompleted ? themedColors.textSecondary : themedColors.textSecondary} />
              <Text 
                style={[
                  styles.detailText,
                  isCompleted && styles.completedText
                ]}
              >
                {formatDate(taskDueDate, locale)}
              </Text>   
            </View>
            
            {/* Farm */}
            {task.farmName && (
              <View style={[styles.detailItem, language === 'ur' && { flexDirection: 'row-reverse', gap: 5 }]}>
                <MapPin size={14} color={isCompleted ? themedColors.textSecondary : themedColors.textSecondary} />
                <Text 
                  style={[
                    styles.detailText,
                    isCompleted && styles.completedText
                  ]}
                  numberOfLines={1}
                >
                  {task.farmName}
                </Text>
              </View>
            )}
            
            {/* Assignee */}
            {task.assigneeName && (
               <View style={[styles.detailItem, language === 'ur' && { flexDirection: 'row-reverse', gap: 5 }]}>
                <User size={14} color={isCompleted ? themedColors.textSecondary : themedColors.textSecondary} />
                <Text 
                  style={[
                    styles.detailText,
                    isCompleted && styles.completedText
                  ]}
                  numberOfLines={1}
                >
                  {task.assigneeName}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  } catch (error) {
    console.error('Error rendering TaskCard:', error);
    return null;
  }
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: themedColors.card,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: themedColors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  completedContainer: {
    opacity: 0.7,
  },
  statusIndicator: {
    width: 4,
    height: '100%',
  },
  content: {
    flex: 1,
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: themedColors.text,
    flex: 1,
    marginRight: 8,
  },
  completedText: {
    color: themedColors.textSecondary, // Assuming textLight is a lighter version of textSecondary
    textDecorationLine: 'line-through',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.success + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  priorityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  description: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 12,
  },
  detailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  detailText: {
    fontSize: 12,
    color: themedColors.textSecondary,
    marginLeft: 4,
  },
});

export default TaskCard;
