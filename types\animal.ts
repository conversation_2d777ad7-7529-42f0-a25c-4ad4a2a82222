export interface Animal {
  id: string;
  name: string;
  species: string;
  breed?: string;
  age?: number;
  weight?: number;
  gender: 'male' | 'female' | 'unknown';
  tagId?: string;
  imageUri?: string;
  ownerId: string; // ID of the user who created the animal

  nextHealthCheck?: number;
  lastHealthCheck?: number;
  healthCheckInterval?: number; // in days, default 7
  lastVaccination?: number;
  nextVaccination?: number;
  hasHealthIssues?: boolean;
  birthDate?: number;
  createdAt: number;
  updatedAt: number;

  // Purchase information
  purchasePrice?: number;
  purchaseDate?: number;
  purchaseNotes?: string;
  purchaseReceiptImage?: string;

  // Multi-tenancy fields
  tenantId: string; // ID of the owner (tenant) who owns the farm this animal is associated with
  farmId?: string; // ID of the farm this animal belongs to
  farmName?: string;
  farm?: {
    name: string;
    id: string;
  };
  fatherId?: string; // ID of the father animal or 'BOUGHT_EXTERNAL'
  motherId?: string; // ID of the mother animal or 'BOUGHT_EXTERNAL'

  // Pregnancy information
  pregnancyStatus?: 'confirmed' | 'suspected' | 'not_pregnant' | null;
  pregnancyId?: string | null;
}

export interface Disease {
  id: string;
  name: string;
  description: string;
  symptoms: string[];
  severity: 'low' | 'medium' | 'high';
  zoonotic: boolean;
  treatment?: string;
  prevention?: string;
}

export interface AnimalRecord {
  id: string;
  animalId: string;
  date: number;
  symptoms: string[];
  diagnosis?: string;
  treatment?: string;
  notes?: string;
  predictedDiseases?: Disease[];
  confirmedDisease?: Disease;
  createdBy: string;
  createdAt: number;
  updatedAt: number;

  // Multi-tenancy fields
  tenantId: string; // ID of the owner (tenant) who owns the farm this record is associated with
}

