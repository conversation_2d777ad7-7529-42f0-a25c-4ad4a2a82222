import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView 
} from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSettingsStore } from '@/store/settings-store';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import { useTranslation } from '@/hooks/useTranslation';
import { colors } from '@/constants/colors';
import { Check } from 'lucide-react-native';

export default function TextSizeScreen() {
  const router = useRouter();
  const { textSize, setTextSize } = useSettingsStore();
  const { playFeedback } = useAudioFeedback();
  const { t } = useTranslation();
  
  const textSizes = [
    { value: 'small', label: 'Small' },
    { value: 'medium', label: 'Medium' },
    { value: 'large', label: 'Large' },
  ];
  
  const handleTextSizeSelect = (size: 'small' | 'medium' | 'large') => {
    setTextSize(size);
    playFeedback('success');
    router.back();
  };
  
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>Text Size</Text>
        
        <View style={styles.sizeList}>
          {textSizes.map((size) => (
            <TouchableOpacity
              key={size.value}
              style={[
                styles.sizeItem,
                textSize === size.value && styles.selectedSizeItem
              ]}
              onPress={() => handleTextSizeSelect(size.value as 'small' | 'medium' | 'large')}
            >
              <Text 
                style={[
                  styles.sizeName,
                  { fontSize: size.value === 'small' ? 16 : size.value === 'large' ? 20 : 18 },
                  textSize === size.value && styles.selectedSizeName
                ]}
              >
                {size.label}
              </Text>
              
              {textSize === size.value && (
                <Check size={20} color={colors.primary} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>Preview</Text>
          <View style={styles.previewCard}>
            <Text style={[styles.previewHeading, getPreviewTextSize('heading')]}>
              Heading Text
            </Text>
            <Text style={[styles.previewBody, getPreviewTextSize('body')]}>
              This is how your text will appear throughout the app. Choose a size that is comfortable for you to read.
            </Text>
            <Text style={[styles.previewSmall, getPreviewTextSize('small')]}>
              Smaller text like this will also adjust based on your selection.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
  
  function getPreviewTextSize(type: 'heading' | 'body' | 'small') {
    const sizeMultiplier = textSize === 'small' ? 0.9 : textSize === 'large' ? 1.2 : 1;
    
    switch (type) {
      case 'heading':
        return { fontSize: 20 * sizeMultiplier };
      case 'body':
        return { fontSize: 16 * sizeMultiplier };
      case 'small':
        return { fontSize: 14 * sizeMultiplier };
    }
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
    flexGrow: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 24,
  },
  sizeList: {
    marginTop: 16,
  },
  sizeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: colors.card,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedSizeItem: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  sizeName: {
    color: colors.text,
  },
  selectedSizeName: {
    fontWeight: '600',
    color: colors.primary,
  },
  previewContainer: {
    marginTop: 32,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  previewCard: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  previewHeading: {
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 12,
  },
  previewBody: {
    color: colors.text,
    marginBottom: 16,
    lineHeight: 22,
  },
  previewSmall: {
    color: colors.textSecondary,
  },
});