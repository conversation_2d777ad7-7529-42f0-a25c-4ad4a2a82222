import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { usePregnancyStore, Pregnancy } from '@/store/pregnancy-store';
import { Calendar, FileText, Utensils, Stethoscope } from 'lucide-react-native';
import DietTab from '@/components/Pregnancy/DietTab';
import ReportTab from '@/components/Pregnancy/ReportTab';
import HealthTab from '@/components/Pregnancy/HealthTab'

export default function PregnancyDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const colors = useThemeColors();
  const { t, language } = useTranslation();
  const { getPregnancy } = usePregnancyStore();
  
  const [pregnancy, setPregnancy] = useState<Pregnancy | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('report');
  
  useEffect(() => {
    const fetchPregnancy = async () => {
      try {
        if (typeof id !== 'string') return;
        
        const pregnancyData = await getPregnancy(id);
        if (pregnancyData) {
          setPregnancy(pregnancyData);
        } else {
          console.error('Pregnancy not found');
          // Handle not found case
        }
      } catch (error) {
        console.error('Error fetching pregnancy:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchPregnancy();
  }, [id]);
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    tabsContainer: {
      flexDirection: 'row',
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.card,
    },
    tab: {
      flex: 1,
      paddingVertical: 12,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: colors.primary,
    },
    tabText: {
      fontSize: 14,
      color: colors.textSecondary,
      marginLeft: 4,
    },
    activeTabText: {
      color: colors.primary,
      fontWeight: '500',
    },
    tabIcon: {
      marginRight: 4,
    },
    urduText: {
      fontFamily: 'UrduFont',
    },
  });
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }
  
  if (!pregnancy) {
    return (
      <View style={styles.loadingContainer}>
        <Text>{t('pregnancy.notFound')}</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: pregnancy.animalName,
          headerBackTitle: t('common.back')
        }} 
      />
      
      <View style={styles.tabsContainer}>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'report' && styles.activeTab]} 
          onPress={() => setActiveTab('report')}
        >
          <FileText 
            size={20} 
            color={activeTab === 'report' ? colors.primary : colors.textSecondary} 
            style={styles.tabIcon} 
          />
          <Text 
            style={[
              styles.tabText, 
              activeTab === 'report' && styles.activeTabText,
              language === 'ur' ? styles.urduText : null
            ]}
          >
            {t('pregnancy.report', { defaultValue: 'Report' })}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'diet' && styles.activeTab]} 
          onPress={() => setActiveTab('diet')}
        >
          <Utensils 
            size={20} 
            color={activeTab === 'diet' ? colors.primary : colors.textSecondary} 
            style={styles.tabIcon} 
          />
          <Text 
            style={[
              styles.tabText, 
              activeTab === 'diet' && styles.activeTabText,
              language === 'ur' ? styles.urduText : null
            ]}
          >
            {t('pregnancy.dietTab', { defaultValue: 'Diet' })}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'health' && styles.activeTab]} 
          onPress={() => setActiveTab('health')}
        >
          <Stethoscope 
            size={20} 
            color={activeTab === 'health' ? colors.primary : colors.textSecondary} 
            style={styles.tabIcon} 
          />
          <Text 
            style={[
              styles.tabText, 
              activeTab === 'health' && styles.activeTabText,
              language === 'ur' ? styles.urduText : null
            ]}
          >
            {t('pregnancy.healthTab', { defaultValue: 'Health' })}
          </Text>
        </TouchableOpacity>
      </View>
      
      {activeTab === 'report' && <ReportTab pregnancy={pregnancy} colors={colors} />}
      {activeTab === 'diet' && <DietTab pregnancy={pregnancy} colors={colors} />}
      {activeTab === 'health' && <HealthTab pregnancy={pregnancy} colors={colors} />}
    </View>
  );
}


