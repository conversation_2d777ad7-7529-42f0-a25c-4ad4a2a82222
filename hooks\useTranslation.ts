import { useSettingsStore } from '@/store/settings-store';
import { en } from '@/locales/en';
import { ur } from '@/locales/ur';
import { pa } from '@/locales/pa';

export function useTranslation() {
  const { language } = useSettingsStore();

  const translations = {
    en,
    ur,
    pa,
  };

  const currentTranslations = translations[language as keyof typeof translations] || en;

  const t = (key: string, variables?: Record<string, any>) => {
    if (!key) return '';

    try {
      const keys = key.split('.');
      let value: any = currentTranslations;

      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k as keyof typeof value];
        } else {
          // If translation not found, return the last part of the key as fallback
          return keys[keys.length - 1];
        }
      }

      // Check if the value is an object (nested translation)
      if (value && typeof value === 'object') {
        // Return a placeholder instead of the object itself
        return keys[keys.length - 1];
      }

      let result = value as string;

      // Replace variables in the translation string if provided
      if (variables && typeof result === 'string') {
        Object.entries(variables).forEach(([varName, varValue]) => {
          result = result.replace(new RegExp(`\{\{${varName}\}\}`, 'g'), String(varValue));
        });
      }

      return result;
    } catch (error) {
      // If any error occurs, return the last part of the key as fallback
      const parts = key.split('.');
      return parts[parts.length - 1];
    }
  };

  return { t, language };
}