import AsyncStorage from '@react-native-async-storage/async-storage';

const OPENAI_API_KEY = 'openai_api_key';

export async function saveOpenAIApiKey(apiKey: string): Promise<void> {
  try {
    await AsyncStorage.setItem(OPENAI_API_KEY, apiKey);
  } catch (error) {
    console.error('Error saving OpenAI API key:', error);
    throw error;
  }
}

export async function getOpenAIApiKey(): Promise<string | null> {
  try {
    return await AsyncStorage.getItem(OPENAI_API_KEY);
  } catch (error) {
    console.error('Error getting OpenAI API key:', error);
    return null;
  }
}

export async function removeOpenAIApiKey(): Promise<void> {
  try {
    await AsyncStorage.removeItem(OPENAI_API_KEY);
  } catch (error) {
    console.error('Error removing OpenAI API key:', error);
    throw error;
  }
}