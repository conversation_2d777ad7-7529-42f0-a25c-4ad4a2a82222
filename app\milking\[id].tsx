import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { useMilkingStore } from '@/store/milking-store';
import { MilkingRecord, MilkQuality } from '@/types/milking';
import { Milk, Calendar, Droplets, Edit, Trash2, Sun, Sunset, Moon, Award, ThumbsUp, Meh, ThumbsDown } from 'lucide-react-native';
import LoadingIndicator from '@/components/LoadingIndicator';
import { formatDate } from '@/utils/date-utils';

export default function MilkingDetailScreen() {
  const { t, language } = useTranslation();
  const router = useRouter();
  const themedColors = useThemeColors();
  const { id } = useLocalSearchParams<{ id: string }>();
  
  const { getRecordById, deleteRecord, isLoading } = useMilkingStore();
  const [record, setRecord] = useState<MilkingRecord | null>(null);

  const styles = getStyles(themedColors, language);

  useEffect(() => {
    if (id) {
      const milkingRecord = getRecordById(id);
      setRecord(milkingRecord || null);
    }
  }, [id]);

  const getQualityColor = (quality: MilkQuality): string => {
    switch (quality) {
      case MilkQuality.EXCELLENT:
        return '#10B981';
      case MilkQuality.GOOD:
        return '#3B82F6';
      case MilkQuality.FAIR:
        return '#F59E0B';
      case MilkQuality.POOR:
        return '#EF4444';
      default:
        return themedColors.textSecondary;
    }
  };

  const getSessionIcon = (session: string) => {
    switch (session) {
      case 'morning':
        return <Sun size={20} color={themedColors.primary} />;
      case 'afternoon':
        return <Sunset size={20} color={themedColors.primary} />;
      case 'evening':
        return <Moon size={20} color={themedColors.primary} />;
      default:
        return <Sun size={20} color={themedColors.primary} />;
    }
  };

  const getQualityIcon = (quality: MilkQuality) => {
    switch (quality) {
      case MilkQuality.EXCELLENT:
        return <Award size={20} color="#10B981" />;
      case MilkQuality.GOOD:
        return <ThumbsUp size={20} color="#3B82F6" />;
      case MilkQuality.FAIR:
        return <Meh size={20} color="#F59E0B" />;
      case MilkQuality.POOR:
        return <ThumbsDown size={20} color="#EF4444" />;
      default:
        return <ThumbsUp size={20} color="#3B82F6" />;
    }
  };

  const handleEdit = () => {
    router.push(`/milking/edit/${id}`);
  };

  const handleDelete = () => {
    Alert.alert(
      t('common.confirm'),
      t('milking.deleteConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteRecord(id);
              Alert.alert(t('common.success'), t('milking.deleteSuccess'));
              router.back();
            } catch (error) {
              Alert.alert(t('common.error'), t('common.errorOccurred'));
            }
          },
        },
      ]
    );
  };

  if (isLoading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  if (!record) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <Stack.Screen
          options={{
            title: t('milking.recordDetails'),
            headerBackTitle: t('common.back'),
          }}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{t('common.recordNotFound')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('milking.recordDetails'),
          headerBackTitle: t('common.back'),
          headerRight: () => (
            <View style={styles.headerActions}>
              <TouchableOpacity onPress={handleEdit} style={styles.headerButton}>
                <Edit size={20} color={themedColors.primary} />
              </TouchableOpacity>
              <TouchableOpacity onPress={handleDelete} style={styles.headerButton}>
                <Trash2 size={20} color={themedColors.error} />
              </TouchableOpacity>
            </View>
          ),
        }}
      />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Main Info Card */}
          <View style={styles.mainCard}>
            <View style={[styles.cardHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
              <View style={[styles.animalInfo, language === 'ur' && { alignItems: 'flex-end' }]}>
                <Text style={[styles.animalName, language === 'ur' && styles.urduText]}>
                  {record.animalName}
                </Text>
                <Text style={[styles.animalSpecies, language === 'ur' && styles.urduText]}>
                  {record.animalSpecies}
                </Text>
              </View>
              <View style={[styles.quantityDisplay, language === 'ur' && { alignItems: 'flex-end' }]}>
                <Text style={[styles.quantityValue, language === 'ur' && styles.urduText]}>
                  {record.quantity.toFixed(1)}
                </Text>
                <Text style={[styles.quantityUnit, language === 'ur' && styles.urduText]}>
                  {t('milking.liters')}
                </Text>
              </View>
            </View>
          </View>

          {/* Details Grid */}
          <View style={styles.detailsGrid}>
            {/* Date & Session */}
            <View style={styles.detailCard}>
              <View style={[styles.detailHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
                <Calendar size={20} color={themedColors.primary} />
                <Text style={[styles.detailTitle, language === 'ur' && styles.urduText]}>
                  {t('milking.date')}
                </Text>
              </View>
              <Text style={[styles.detailValue, language === 'ur' && styles.urduText]}>
                {formatDate(record.date, language)}
              </Text>
            </View>

            <View style={styles.detailCard}>
              <View style={[styles.detailHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
                {getSessionIcon(record.session)}
                <Text style={[styles.detailTitle, language === 'ur' && styles.urduText]}>
                  {t('milking.session')}
                </Text>
              </View>
              <Text style={[styles.detailValue, language === 'ur' && styles.urduText]}>
                {t(`milking.session.${record.session}`)}
              </Text>
            </View>

            {/* Quality */}
            <View style={styles.detailCard}>
              <View style={[styles.detailHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
                {getQualityIcon(record.quality)}
                <Text style={[styles.detailTitle, language === 'ur' && styles.urduText]}>
                  {t('milking.quality')}
                </Text>
              </View>
              <Text style={[
                styles.detailValue,
                { color: getQualityColor(record.quality) },
                language === 'ur' && styles.urduText
              ]}>
                {t(`milking.quality.${record.quality}`)}
              </Text>
            </View>

            {/* Optional Fields */}
            {record.fat && (
              <View style={styles.detailCard}>
                <Text style={[styles.detailTitle, language === 'ur' && styles.urduText]}>
                  {t('milking.fat')}
                </Text>
                <Text style={[styles.detailValue, language === 'ur' && styles.urduText]}>
                  {record.fat.toFixed(1)}%
                </Text>
              </View>
            )}

            {record.protein && (
              <View style={styles.detailCard}>
                <Text style={[styles.detailTitle, language === 'ur' && styles.urduText]}>
                  {t('milking.protein')}
                </Text>
                <Text style={[styles.detailValue, language === 'ur' && styles.urduText]}>
                  {record.protein.toFixed(1)}%
                </Text>
              </View>
            )}

            {record.temperature && (
              <View style={styles.detailCard}>
                <Text style={[styles.detailTitle, language === 'ur' && styles.urduText]}>
                  {t('milking.temperature')}
                </Text>
                <Text style={[styles.detailValue, language === 'ur' && styles.urduText]}>
                  {record.temperature.toFixed(1)}°C
                </Text>
              </View>
            )}
          </View>

          {/* Notes */}
          {record.notes && (
            <View style={styles.notesCard}>
              <Text style={[styles.notesTitle, language === 'ur' && styles.urduText]}>
                {t('milking.notes')}
              </Text>
              <Text style={[styles.notesText, language === 'ur' && styles.urduText]}>
                {record.notes}
              </Text>
            </View>
          )}

          {/* Farm Info */}
          <View style={styles.farmCard}>
            <Text style={[styles.farmTitle, language === 'ur' && styles.urduText]}>
              {t('farms.farm')}
            </Text>
            <Text style={[styles.farmName, language === 'ur' && styles.urduText]}>
              {record.farmName}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: themedColors.background,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: 16,
    },
    headerActions: {
      flexDirection: 'row',
      gap: 12,
    },
    headerButton: {
      padding: 4,
    },
    mainCard: {
      backgroundColor: themedColors.card,
      borderRadius: 16,
      padding: 20,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: themedColors.border,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    cardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    animalInfo: {
      flex: 1,
    },
    animalName: {
      fontSize: 20,
      fontWeight: 'bold',
      color: themedColors.text,
      marginBottom: 4,
    },
    animalSpecies: {
      fontSize: 14,
      color: themedColors.textSecondary,
    },
    quantityDisplay: {
      alignItems: 'center',
    },
    quantityValue: {
      fontSize: 32,
      fontWeight: 'bold',
      color: themedColors.primary,
    },
    quantityUnit: {
      fontSize: 14,
      color: themedColors.textSecondary,
      marginTop: 4,
    },
    detailsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      marginBottom: 16,
    },
    detailCard: {
      backgroundColor: themedColors.card,
      borderRadius: 12,
      padding: 16,
      width: '48%',
      borderWidth: 1,
      borderColor: themedColors.border,
    },
    detailHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      marginBottom: 8,
    },
    detailTitle: {
      fontSize: 14,
      color: themedColors.textSecondary,
      fontWeight: '500',
    },
    detailValue: {
      fontSize: 16,
      color: themedColors.text,
      fontWeight: '600',
    },
    notesCard: {
      backgroundColor: themedColors.card,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: themedColors.border,
    },
    notesTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: themedColors.text,
      marginBottom: 8,
    },
    notesText: {
      fontSize: 14,
      color: themedColors.textSecondary,
      lineHeight: 20,
    },
    farmCard: {
      backgroundColor: themedColors.card,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: themedColors.border,
    },
    farmTitle: {
      fontSize: 14,
      color: themedColors.textSecondary,
      marginBottom: 4,
    },
    farmName: {
      fontSize: 16,
      fontWeight: '600',
      color: themedColors.text,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorText: {
      fontSize: 16,
      color: themedColors.error,
      textAlign: 'center',
    },
    urduText: {
      textAlign: 'right',
    },
  });
