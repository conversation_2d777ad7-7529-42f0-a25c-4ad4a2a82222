import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useThemeColors } from '../../hooks/useThemeColors'; // Adjust path
import { useTranslation } from '@/hooks/useTranslation';
import { usePregnancyStore } from '@/store/pregnancy-store';
import { Baby, Eye } from 'lucide-react-native';
import { router } from 'expo-router';
import { formatDate } from '@/utils/date-utils';
import { differenceInDays, addDays } from 'date-fns';
import { gestationPeriods } from '@/constants/breeds';
import { useFarmStore } from '@/store/farm-store';

interface PregnancyStatusProps {
  animalId: string;
  farmId?: string;
}
const PregnancyStatus = ({ animalId, farmId }: PregnancyStatusProps) => {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);
  const { pregnancies, fetchPregnancies } = usePregnancyStore();
  const { farms } = useFarmStore();

  // Get farmId from props or try to find it from farms
  const effectiveFarmId = farmId || (farms.length > 0 ? farms[0].id : null);

  // Fetch pregnancies when component mounts
  React.useEffect(() => {
    if (effectiveFarmId) {
      fetchPregnancies(effectiveFarmId);
    }
  }, [fetchPregnancies, effectiveFarmId]);

  // Get pregnancies for this animal
  const animalPregnancies = pregnancies.filter(p => p.animalId === animalId);


  // Sort pregnancies by creation date (newest first) to get the most recent one
  const sortedPregnancies = animalPregnancies.sort((a, b) => {
    const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
    const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
    return dateB - dateA;
  });

  // Get current pregnancy (first confirmed/suspected pregnancy, or most recent if none active)
  const currentPregnancy = sortedPregnancies.find(p => p.status === 'confirmed' || p.status === 'suspected') || sortedPregnancies[0];
  const pregnancyCount = animalPregnancies.length;



  // If no current pregnancy, show summary
  if (!currentPregnancy) {
    return (
      <View style={styles.container}>
        <View style={styles.statusRow}>
          <Text style={styles.header}>{t('pregnancy.status')}</Text>
          <Text style={styles.statusBadge}>{t('pregnancy.notPregnant', { defaultValue: 'Not Pregnant' })}</Text>
        </View>

        <View style={styles.infoGrid}>
          <View style={styles.infoItem}>
            <Text style={styles.label}>{t('pregnancy.totalPregnancies', { defaultValue: 'Total Pregnancies' })}</Text>
            <Text style={styles.value}>{pregnancyCount}</Text>
          </View>
        </View>

        {pregnancyCount > 0 && (
          <TouchableOpacity style={styles.viewAllButton} onPress={() => handleViewAllPregnancies()}>
            <Eye size={16} color={themedColors.primary} />
            <Text style={styles.viewAllText}>{t('pregnancy.viewAllPregnancies', { defaultValue: 'View All Previous Pregnancies' })}</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  // Calculate pregnancy details
  const calculatePregnancyDetails = () => {
    if (!currentPregnancy.conceptionDate) return null;

    const conceptionDate = new Date(currentPregnancy.conceptionDate);
    const today = new Date();
    const daysPassed = differenceInDays(today, conceptionDate);
    const gestationPeriod = gestationPeriods[currentPregnancy.species?.toLowerCase() || 'default'] || 280;
    const expectedDate = addDays(conceptionDate, gestationPeriod);
    const daysRemaining = Math.max(0, differenceInDays(expectedDate, today));
    const progress = Math.min(100, Math.round((daysPassed / gestationPeriod) * 100));

    // Determine stage
    let stage = 'early';
    if (progress > 66) stage = 'late';
    else if (progress > 33) stage = 'mid';

    return {
      expectedDate: formatDate(expectedDate.getTime()),
      daysRemaining,
      progress,
      stage: t(`pregnancy.stages.${stage}`, { defaultValue: stage })
    };
  };

  const pregnancyDetails = calculatePregnancyDetails();

  const handleViewPregnancy = () => {
    if (currentPregnancy) {
      router.push(`/pregnancy/details/${currentPregnancy.id}`);
    }
  };

  const handleViewAllPregnancies = () => {
    // Navigate to pregnancy listing screen with farm and animal filters
    const targetFarmId = effectiveFarmId || currentPregnancy?.farmId;
    if (targetFarmId) {
      router.push({
        pathname: '/(tabs)/pregnancy',
        params: {
          farmId: targetFarmId,
          animalId: animalId
        }
      });
    } else {
      // Fallback to just pregnancy tab
      router.push('/(tabs)/pregnancy');
    }
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handleViewPregnancy} testID="pregnancy-status-container">
      <View style={styles.statusRow}>
        <Text style={styles.header}>{t('pregnancy.statusT')}</Text>
        <Text style={styles.statusBadge}>{t(`pregnancy.statuses.${currentPregnancy.status}`, { defaultValue: currentPregnancy.status })}</Text>
      </View>

      <View style={styles.infoGrid}>
        <View style={styles.infoItem}>
          <Text style={styles.label}>{t('pregnancy.expectedDate')}</Text>
          <Text style={styles.value}>{pregnancyDetails?.expectedDate}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.label}>{t('pregnancy.daysRemaining')}</Text>
          <Text style={styles.value}>{pregnancyDetails?.daysRemaining} {t('common.days', { defaultValue: 'days' })}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.label}>{t('pregnancy.stage')}</Text>
          <Text style={styles.value}>{pregnancyDetails?.stage}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.label}>{t('pregnancy.progress')}</Text>
          <Text style={styles.value}>{pregnancyDetails?.progress}%</Text>
        </View>
      </View>

      <View style={styles.pregnancyCountRow}>
        <View style={styles.pregnancyCountItem}>
          <Baby size={16} color={themedColors.primary} />
          <Text style={styles.pregnancyCountText}>
            {t('pregnancy.totalPregnancies', { defaultValue: 'Total Pregnancies: {{count}}', count: pregnancyCount })}
          </Text>
        </View>
      </View>

      <View style={styles.clickHint}>
        <Eye size={14} color={themedColors.textSecondary} />
        <Text style={styles.clickHintText}>{t('pregnancy.clickToViewDetails', { defaultValue: 'Click to view pregnancy details' })}</Text>
      </View>

      {pregnancyCount > 1 && (
        <TouchableOpacity style={styles.viewAllButton} onPress={(e) => {
          e.stopPropagation();
          handleViewAllPregnancies();
        }}>
          <Text style={styles.viewAllText}>{t('pregnancy.viewAllPregnancies', { defaultValue: 'View All Previous Pregnancies' })}</Text>
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 20,
    margin: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: themedColors.isDarkMode ? 0.35 : 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    fontSize: 18,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 5,
  },
  statusRow: {
    flexDirection: language === 'ur' ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    backgroundColor: themedColors.primary,
    color: '#fff',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 20,
    fontSize: 12,
    fontWeight: '600',
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 15,
    justifyContent: 'space-between',
  },
  infoItem: {
    width: '47%',
    marginBottom: 15,
  },
  label: {
    fontSize: 12,
    color: themedColors.textSecondary,
    marginBottom: 3,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  value: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  pregnancyCountRow: {
    flexDirection: language === 'ur' ? 'row-reverse' : 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: themedColors.border,
  },
  pregnancyCountItem: {
    flexDirection: language === 'ur' ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  pregnancyCountText: {
    fontSize: 14,
    color: themedColors.primary,
    fontWeight: '600',
    marginLeft: language === 'ur' ? 0 : 6,
    marginRight: language === 'ur' ? 6 : 0,
  },

  clickHint: {
    flexDirection: language === 'ur' ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
  clickHintText: {
    fontSize: 12,
    color: themedColors.textSecondary,
    marginLeft: language === 'ur' ? 0 : 4,
    marginRight: language === 'ur' ? 4 : 0,
  },
  viewAllButton: {
    marginTop: 10,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: themedColors.primaryLight || `${themedColors.primary}20`,
    borderRadius: 8,
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: 12,
    color: themedColors.primary,
    fontWeight: '600',
  },

});

export default PregnancyStatus;
