import { useEffect } from 'react';
import { Platform } from 'react-native';
import { useSettingsStore } from '@/store/settings-store';
import * as Haptics from 'expo-haptics';

type FeedbackType = 'success' | 'error' | 'warning' | 'toggle' | 'navigation' | 'logout';

export function useAudioFeedback() {
  const { audioFeedbackEnabled } = useSettingsStore();

  const playFeedback = async (type: FeedbackType) => {
    // Skip if audio feedback is disabled or on web
    if (!audioFeedbackEnabled || Platform.OS === 'web') {
      return;
    }

    try {
      // Use haptics instead of audio for feedback
      switch (type) {
        case 'success':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case 'error':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
        case 'warning':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case 'toggle':
          await Haptics.selectionAsync();
          break;
        case 'navigation':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'logout':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        default:
          await Haptics.selectionAsync();
      }
    } catch (error) {
      console.error('Error playing haptic feedback:', error);
    }
  };

  return { playFeedback };
}