import React, { useState } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
} from 'react-native';
import { colors } from '@/constants/colors';
import { ChevronDown, Search, X, Plus } from 'lucide-react-native';

interface BreedDropdownProps {
  label: string;
  placeholder: string;
  options: string[];
  value: string;
  onSelect: (value: string) => void;
  error?: string;
  allowCustomBreed?: boolean;
  onAddNewBreed?: (breed: string) => void;
}

const BreedDropdown: React.FC<BreedDropdownProps> = ({
  label,
  placeholder,
  options,
  value,
  onSelect,
  error,
  allowCustomBreed = false,
  onAddNewBreed,
}) => {
  const { t, language } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newBreedName, setNewBreedName] = useState('');

  // Get translated option text
  const getTranslatedOption = (option: string): string => {
    if (language === 'ur') {
      // Try to get the translation using the array notation format
      const translationKey = `animals[${JSON.stringify(option)}]`;
      const translation = t(translationKey);

      // If translation is not found, try the dot notation format
      if (translation === translationKey) {
        return t(`animals.${option}`) || option;
      }

      return translation;
    }
    return option;
  };

  // Filter options based on search query
  const filteredOptions = options.filter(option => {
    const translatedOption = getTranslatedOption(option);
    return translatedOption.toLowerCase().includes(searchQuery.toLowerCase()) ||
           option.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Handle option selection
  const handleSelect = (option: string) => {
    onSelect(option);
    setModalVisible(false);
    setSearchQuery('');
  };

  // Close modal
  const handleClose = () => {
    setModalVisible(false);
    setSearchQuery('');
  };

  const handleAddNewBreed = () => {
    if (newBreedName.trim()) {
      onAddNewBreed?.(newBreedName.trim());
      setModalVisible(false);
      setIsAddingNew(false);
      setNewBreedName('');
      setSearchQuery('');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{label}</Text>

      <TouchableOpacity
        style={[
          styles.dropdownButton,
          error ? styles.dropdownButtonError : null,
        ]}
        onPress={() => setModalVisible(true)}
      >
        <Text style={[
          styles.dropdownButtonText,
          !value && styles.placeholderText,
          language === 'ur' ? styles.urduText : null
        ]}>
          {value ? getTranslatedOption(value) : placeholder}
        </Text>
        <ChevronDown size={20} color={colors.textSecondary} />
      </TouchableOpacity>

      {error && <Text style={[styles.errorText, language === 'ur' ? styles.urduText : null]}>{error}</Text>}

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={handleClose}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, language === 'ur' ? styles.urduText : null]}>{t('animals.breedPlaceholder')}</Text>
              <TouchableOpacity onPress={handleClose}>
                <X size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={[styles.searchContainer, language === 'ur' ? styles.searchContainerRtl : null]}>
              <Search size={20} color={colors.textSecondary} style={[styles.searchIcon, language === 'ur' ? styles.searchIconRtl : null]} />
              <TextInput

                placeholder={t('animals.searchBreeds')}
                style={[styles.searchInput, language === 'ur' ? styles.urduInput : null]}
                textAlign={language === 'ur' ? 'right' : 'left'}
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity
                  onPress={() => setSearchQuery('')}
                  style={language === 'ur' ? { marginRight: 8 } : undefined}
                >
                  <X size={20} color={colors.textSecondary} />
                </TouchableOpacity>
              )}
            </View>

            <FlatList
              data={filteredOptions}
              keyExtractor={(item) => item}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.optionItem,
                    value === item && styles.selectedOption
                  ]}
                  onPress={() => handleSelect(item)}
                >
                  <Text style={[
                    styles.optionText,
                    value === item && styles.selectedOptionText,
                    language === 'ur' ? styles.urduText : null
                  ]}>
                    {getTranslatedOption(item)}
                  </Text>
                </TouchableOpacity>
              )}
              ListFooterComponent={() => 
                allowCustomBreed ? (
                  isAddingNew ? (
                    <View style={styles.addNewContainer}>
                      <TextInput
                        style={styles.addNewInput}
                        value={newBreedName}
                        onChangeText={setNewBreedName}
                        placeholder={t('animals.enterNewBreed')}
                        autoFocus
                      />
                      <TouchableOpacity
                        style={styles.addNewButton}
                        onPress={handleAddNewBreed}
                      >
                        <Text style={styles.addNewButtonText}>{t('common.add')}</Text>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.addNewOption}
                      onPress={() => setIsAddingNew(true)}
                    >
                      <Plus size={20} color={colors.primary} />
                      <Text style={styles.addNewText}>{t('animals.addNewBreed')}</Text>
                    </TouchableOpacity>
                  )
                ) : null
              }
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={[styles.emptyText, language === 'ur' ? styles.urduText : null]}>
                    {t('animals.noBreeds')} "{searchQuery}"
                  </Text>
                </View>
              }
              style={styles.optionsList}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  urduInput: {
    textAlign: 'right',
  },
  searchContainerRtl: {
    flexDirection: 'row-reverse',
  },
  searchIconRtl: {
    marginRight: 0,
    marginLeft: 8,
  },
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: colors.text,
    fontWeight: '500',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 50,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.card,
  },
  dropdownButtonError: {
    borderColor: colors.error,
  },
  dropdownButtonText: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
  },
  placeholderText: {
    color: colors.textSecondary,
  },
  errorText: {
    color: colors.error,
    fontSize: 12,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.background,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.card,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
  },
  optionsList: {
    maxHeight: 400,
  },
  optionItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  selectedOption: {
    backgroundColor: colors.primaryLight,
  },
  optionText: {
    fontSize: 16,
    color: colors.text,
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: '500',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  addNewOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    gap: 8,
  },
  addNewText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: '500',
  },
  addNewContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    flexDirection: 'row',
    gap: 8,
  },
  addNewInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 8,
    fontSize: 16,
  },
  addNewButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    justifyContent: 'center',
  },
  addNewButtonText: {
    color: 'white',
    fontWeight: '500',
  },
});

export default BreedDropdown;


