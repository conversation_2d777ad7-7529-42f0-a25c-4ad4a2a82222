import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { Pregnancy } from '@/store/pregnancy-store';
import { gestationPeriods } from '@/constants/breeds'; // Assuming gestationPeriods is available
import { Sparkles } from 'lucide-react-native';
import GenericDropdown from '@/components/GenericDropdown';
import { addDays, differenceInDays } from 'date-fns';

interface HealthTabProps {
  pregnancy: Pregnancy;
  colors: any;
}

interface HealthRecommendation {
  week: number;
  stage: 'early' | 'mid' | 'late';
  title: string;
  description: string;
  checkups?: string[];
  treatments?: string[];
}

interface GroupedHealthRecommendations {
  early?: HealthRecommendation[];
  mid?: HealthRecommendation[];
  late?: HealthRecommendation[];
  [key: string]: HealthRecommendation[] | undefined;
}

const HealthTab = ({ pregnancy, colors }: HealthTabProps) => {
  const { t, language } = useTranslation();

  // State for week filter
  const [selectedWeek, setSelectedWeek] = useState<string>('all');
  
  const styles = StyleSheet.create({
    container: {
      flexGrow: 1, // Ensure ScrollView takes full height
      padding: 16,
    },
    section: {
      marginBottom: 20,
      backgroundColor: colors.card,
      borderRadius: 8,
      padding: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 10,
      color: colors.text,
    },
    recommendationItem: { // Renamed from checkItem
      marginBottom: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      paddingBottom: 12,
    },
    recommendationTitle: { // Renamed from checkTitle
      fontSize: 16,
      fontWeight: '600', // Slightly bolder title
      color: colors.text,
      marginBottom: 4,
    },
    recommendationDesc: { // Corrected from checkDesc
      fontSize: 14,
      color: colors.textSecondary,
      lineHeight: 20,
    },
    urduText: {
      // Assuming 'UrduFont' is loaded and available
      fontFamily: 'UrduFont',
    },
    weekTitle: {
      fontSize: 15, // Slightly smaller than section title
      fontWeight: '600',
      color: colors.primary,
      marginBottom: 8,
      marginTop: 4, // Add some space above week title
    },
    stageHeader: {
      fontSize: 17,
      fontWeight: '700',
      color: colors.primary,
      marginBottom: 12,
      marginTop: 4,
      backgroundColor: colors.backgroundSecondary, // Use a subtle background
      padding: 8,
      borderRadius: 6,
      overflow: 'hidden', // Ensure background respects border radius
    },
    currentWeekIndicator: {
      backgroundColor: colors.primary + '15', // Add transparency
      borderLeftWidth: 3,
      borderLeftColor: colors.primary,
      paddingLeft: 12, // Add padding to the left
      marginLeft: -16, // Offset padding from section
      paddingVertical: 8, // Add vertical padding
      marginBottom: 12, // Adjust margin bottom
      borderRadius: 0, // Remove border radius for this item
    },
    aiGeneratedBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primaryLight || `${colors.primary}20`,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      alignSelf: 'flex-start',
      marginTop: 4,
      marginBottom: 8,
    },
    aiGeneratedText: {
      fontSize: 12,
      color: colors.primary,
      marginLeft: 4,
    },
    // Styles for bullet points, similar to DietTab
    bulletPoint: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 4,
      marginLeft: 8, // Indent bullet points slightly
    },
    bulletPointRTL: {
      flexDirection: 'row-reverse',
      alignItems: 'flex-start',
      marginBottom: 4,
      marginRight: 8, // Indent bullet points slightly for RTL
    },
    bullet: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: colors.textSecondary, // Use a more subtle color for health bullets
      marginTop: 7, // Align with text
      marginRight: 8,
    },
    bulletRTL: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: colors.textSecondary, // Use a more subtle color for health bullets
      marginTop: 7, // Align with text
      marginLeft: 8,
      marginRight: 0,
    },
    bulletText: {
      flex: 1,
      fontSize: 14,
      color: colors.textSecondary,
    },
    titleRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    weekDropdownContainer: {
      width: '100%',
      marginTop: 5,
    },

  });

  // Use AI-generated health plan if available
  const useAIGeneratedPlan = pregnancy.useAIPlans && pregnancy.aiGeneratedHealthPlan;

  // Debug logging
  console.log('HealthTab Debug:');
  console.log('- pregnancy.useAIPlans:', pregnancy.useAIPlans);
  console.log('- pregnancy.aiGeneratedHealthPlan:', pregnancy.aiGeneratedHealthPlan);
  console.log('- useAIGeneratedPlan:', useAIGeneratedPlan);

  // Calculate current week of pregnancy
  const calculateCurrentWeek = () => {
    if (!pregnancy.conceptionDate) return 1;

    try {
      const conceptionDate = new Date(pregnancy.conceptionDate);
      const today = new Date();
      const diffDays = differenceInDays(today, conceptionDate);
      const currentWeek = Math.max(1, Math.floor(diffDays / 7) + 1); // Ensure week is at least 1
      return currentWeek;
    } catch (error) {
      console.error('Error calculating current week:', error);
      return 1;
    }
  };

  const currentWeek = calculateCurrentWeek();

  // Get gestation period based on species
  const getGestationPeriod = () => {
    const species = pregnancy.species?.toLowerCase() || 'default';
    // Use gestationPeriods from constants, fallback to a default if species not found
    const period = gestationPeriods[species] || gestationPeriods.default || 280; // Default to 280 days if constants fail
    return period;
  };

  const gestationPeriod = getGestationPeriod();
  const totalWeeks = Math.ceil(gestationPeriod / 7);

  // Helper functions for species-specific health content
  // These functions should return translated strings based on species, stage, and week
  // For now, using placeholder text structure. Replace with actual translation keys.
  // Or, use hardcoded defaults like DietTab if translations are not immediately available.
  const getSpeciesSpecificHealthTitle = (species: string, stage: string, week: number): string => {
    const s = species.toLowerCase();
    const stageTranslated = t(`pregnancy.stages.${stage}`, { defaultValue: stage.charAt(0).toUpperCase() + stage.slice(1) });
    const speciesTranslated = t(`animals.${s}`, { defaultValue: s.charAt(0).toUpperCase() + s.slice(1) });

    return t('pregnancy.health.weekTitle', {
      defaultValue: `Week {{week}}: {{stage}} Health Focus ({{species}})`,
      week,
      stage: stageTranslated,
      species: speciesTranslated
    });
  };

  const getSpeciesSpecificHealthDescription = (species: string, stage: string, week: number): string => {
    const s = species.toLowerCase();
    const stageTranslated = t(`pregnancy.stages.${stage}`, { defaultValue: stage });
    const speciesTranslated = t(`animals.${s}`, { defaultValue: s });

    return t('pregnancy.health.generalDescription', {
      defaultValue: `General health recommendations for a {{species}} in {{stage}} stage, week {{week}}. Monitor well-being and consult a vet for concerns.`,
      week,
      stage: stageTranslated,
      species: speciesTranslated
    });
  };

  const getSpeciesSpecificCheckups = (species: string, stage: string, week: number): string[] => {
    const s = species.toLowerCase();

    switch (s) {
      case 'cow':
        if (stage === 'early') {
          return [
            t('pregnancy.health.cow.early.checkup1', {
              defaultValue: `Veterinary confirmation of pregnancy (ultrasound around day 30-60, week {{week}})`,
              week
            }),
            t('pregnancy.health.cow.early.checkup2', { defaultValue: 'Body condition scoring' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.health.cow.mid.checkup1', { defaultValue: 'Monitor for signs of abortion' }),
            t('pregnancy.health.cow.mid.checkup2', {
              defaultValue: `Fetal heart rate check (if equipment available, week {{week}})`,
              week
            })
          ];
        } else {
          return [
            t('pregnancy.health.cow.late.checkup1', { defaultValue: 'Check for udder development and colostrum' }),
            t('pregnancy.health.cow.late.checkup2', {
              defaultValue: `Pelvic ligament relaxation assessment (week {{week}})`,
              week
            })
          ];
        }
      case 'goat':
        if (stage === 'early') {
          return [
            t('pregnancy.health.goat.early.checkup1', {
              defaultValue: `Pregnancy diagnosis (ultrasound around day 25-45, week {{week}})`,
              week
            }),
            t('pregnancy.health.goat.early.checkup2', { defaultValue: 'Parasite load assessment (fecal egg count)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.health.goat.mid.checkup1', { defaultValue: 'Monitor for ketosis/pregnancy toxemia signs' }),
            t('pregnancy.health.goat.mid.checkup2', {
              defaultValue: `Body condition check (week {{week}})`,
              week
            })
          ];
        } else {
          return [
            t('pregnancy.health.goat.late.checkup1', { defaultValue: 'Kidding kit preparation check' }),
            t('pregnancy.health.goat.late.checkup2', {
              defaultValue: `Observation for signs of impending labor (week {{week}})`,
              week
            })
          ];
        }
      default:
        return [
          t('pregnancy.health.general.checkup1', {
            defaultValue: `General health observation (week {{week}})`,
            week
          }),
          t('pregnancy.health.general.checkup2', { defaultValue: 'Consult vet for specific concerns' })
        ];
    }
  };

  const getSpeciesSpecificTreatments = (species: string, stage: string, week: number): string[] => {
    const s = species.toLowerCase();

    switch (s) {
      case 'cow':
        if (stage === 'early') {
          return [
            t('pregnancy.health.cow.early.treatment1', {
              defaultValue: `Clostridial vaccination (if due, consult vet, week {{week}})`,
              week
            }),
            t('pregnancy.health.cow.early.treatment2', { defaultValue: 'Deworming (based on fecal test and vet advice)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.health.cow.mid.treatment1', {
              defaultValue: `Booster vaccinations (e.g., ScourGuard, consult vet, week {{week}})`,
              week
            }),
            t('pregnancy.health.cow.mid.treatment2', { defaultValue: 'Fly control measures' })
          ];
        } else {
          return [
            t('pregnancy.health.cow.late.treatment1', {
              defaultValue: `Vitamin E/Selenium injection (if deficient, 2-3 weeks pre-calving, week {{week}})`,
              week
            }),
            t('pregnancy.health.cow.late.treatment2', { defaultValue: 'Ensure clean, dry calving environment' })
          ];
        }
      case 'goat':
        if (stage === 'early') {
          return [
            t('pregnancy.health.goat.early.treatment1', {
              defaultValue: `CD&T vaccination (if not up-to-date, consult vet, week {{week}})`,
              week
            }),
            t('pregnancy.health.goat.early.treatment2', { defaultValue: 'Strategic deworming (if indicated)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.health.goat.mid.treatment1', {
              defaultValue: `Booster CD&T vaccination (4 weeks pre-kidding, week {{week}})`,
              week
            }),
            t('pregnancy.health.goat.mid.treatment2', { defaultValue: 'Lice/mite treatment (if needed)' })
          ];
        } else {
          return [
            t('pregnancy.health.goat.late.treatment1', {
              defaultValue: `Selenium/Vitamin E supplement (if in deficient area, week {{week}})`,
              week
            }),
            t('pregnancy.health.goat.late.treatment2', { defaultValue: 'Prepare kidding supplies (disinfectant, towels)' })
          ];
        }
      default:
        return [
          t('pregnancy.health.general.treatment1', {
            defaultValue: `Follow routine health protocols (week {{week}})`,
            week
          }),
          t('pregnancy.health.general.treatment2', { defaultValue: 'Administer vet-prescribed treatments only' })
        ];
    }
  };

  // Determine stage based on week and total weeks
  const getStageFromWeek = (week: number, totalWeeks: number): 'early' | 'mid' | 'late' => {
    const earlyEndWeek = Math.floor(totalWeeks / 3);
    const midEndWeek = Math.floor(totalWeeks * 2 / 3);

    if (week <= earlyEndWeek) {
      return 'early';
    } else if (week <= midEndWeek) {
      return 'mid';
    } else {
      return 'late';
    }
  };



  // Generate health recommendations by week
  const generateHealthRecommendations = (): HealthRecommendation[] => {
    console.log('generateHealthRecommendations called');
    console.log('useAIGeneratedPlan:', useAIGeneratedPlan);

    // If we have AI-generated plans, use those instead
    if (useAIGeneratedPlan) {
      console.log('Using AI-generated health plan');
      console.log('AI health plan data:', pregnancy.aiGeneratedHealthPlan);

      // Since the AI health plan now has the same format as diet plan, we can use it directly
      return (pregnancy.aiGeneratedHealthPlan || []).map(item => ({
        week: item.week,
        stage: item.stage,
        title: item.title,
        description: item.description,
        checkups: item.checkups?.map((checkup: string) =>
          checkup.replace(/^AI-recommended\s+/i, '')
        ) || [],
        treatments: item.treatments?.map((treatment: string) =>
          treatment.replace(/^AI-recommended\s+/i, '')
        ) || []
      }));
    }

    console.log('Using default health recommendations');

    const recommendations: HealthRecommendation[] = [];
    const species = pregnancy.species?.toLowerCase() || 'default';

    for (let week = 1; week <= totalWeeks; week++) {
      const stage = getStageFromWeek(week, totalWeeks);
      recommendations.push({
        week,
        stage,
        title: getSpeciesSpecificHealthTitle(species, stage, week),
        description: getSpeciesSpecificHealthDescription(species, stage, week),
        checkups: getSpeciesSpecificCheckups(species, stage, week),
        treatments: getSpeciesSpecificTreatments(species, stage, week),
      });
    }

    return recommendations;
  };

  // Group recommendations by stage
  const groupHealthRecommendationsByStage = (recommendations: HealthRecommendation[]): GroupedHealthRecommendations => {
    const grouped: GroupedHealthRecommendations = {};
    
    recommendations.forEach(rec => {
      if (!grouped[rec.stage]) {
        grouped[rec.stage] = [];
      }
      grouped[rec.stage]?.push(rec);
    });
    
    return grouped;
  };

  const healthRecommendations = generateHealthRecommendations();
  const groupedHealthRecommendations = groupHealthRecommendationsByStage(healthRecommendations);

  // Generate week options for dropdown
  const generateWeekOptions = () => {
    const options = [{ id: 'all', label: t('common.all', { defaultValue: 'All' }) }];

    for (let week = 1; week <= totalWeeks; week++) {
      options.push({
        id: week.toString(),
        label: t('pregnancy.weekNumber', { defaultValue: 'Week {{week}}', week })
      });
    }

    return options;
  };

  const weekOptions = generateWeekOptions();

  // Filter recommendations based on selected week
  const filterRecommendationsByWeek = (recs: HealthRecommendation[]) => {
    if (selectedWeek === 'all') return recs;
    const weekNumber = parseInt(selectedWeek);
    return recs.filter(rec => rec.week === weekNumber);
  };

  // Get stage display text (can be translated)
  const getStageDisplayText = (stage: string): string => {
    return t(`pregnancy.stages.${stage}`, { defaultValue: stage.charAt(0).toUpperCase() + stage.slice(1) });
  };

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.section}>
        <View style={styles.titleRow}>
          <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>
            {t('pregnancy.healthChecks', { defaultValue: 'Health Checks' })}
          </Text>
          {useAIGeneratedPlan && (
            <View style={styles.aiGeneratedBadge}>
              <Sparkles size={14} color={colors.primary} />
              <Text style={styles.aiGeneratedText}>{t('pregnancy.aiGenerated', { defaultValue: 'AI Generated' })}</Text>
            </View>
          )}
        </View>

        <Text style={styles.recommendationDesc}>
          {t('pregnancy.healthSummary', {
            defaultValue: `This ${pregnancy.species || 'animal'} pregnancy is currently in week ${currentWeek} of approximately ${totalWeeks} weeks.`,
            species: pregnancy.species || t('common.animal', { defaultValue: 'animal' }),
            currentWeek: currentWeek,
            totalWeeks: totalWeeks,
          })}
        </Text>

        <View style={styles.weekDropdownContainer}>
          <GenericDropdown
            placeholder={t('pregnancy.selectWeek', { defaultValue: 'Select Week' })}
            items={weekOptions}
            value={selectedWeek}
            onSelect={setSelectedWeek}
            modalTitle={t('pregnancy.selectWeek', { defaultValue: 'Select Week' })}
            searchPlaceholder={t('common.search', { defaultValue: 'Search...' })}
            height={40}
          />
        </View>
      </View>

      {Object.entries(groupedHealthRecommendations).map(([stage, recs]) => {
        const filteredRecs = filterRecommendationsByWeek(recs || []);

        if (filteredRecs.length === 0) return null;

        return (
          <View key={stage} style={styles.section}>
            <Text style={[styles.stageHeader, language === 'ur' ? styles.urduText : null]}>
              {getStageDisplayText(stage)}
            </Text>

            {filteredRecs.map((rec, index) => (
            <View
              key={index}
              style={[
                styles.recommendationItem, 
                rec.week === currentWeek ? styles.currentWeekIndicator : null,
                index === filteredRecs.length - 1 ? { borderBottomWidth: 0, paddingBottom: 0 } : null // Remove border/padding for last item
              ]}
            >
              <Text style={styles.weekTitle}>
                {t('pregnancy.weekNumber', { defaultValue: 'Week {{week}}', week: rec.week })}
                {rec.week === currentWeek ? ` (${t('pregnancy.current', { defaultValue: 'Current' })})` : ''}
              </Text>

              <Text style={styles.recommendationTitle}>{rec.title}</Text>
              <Text style={styles.recommendationDesc}>{rec.description}</Text>

              {rec.checkups && rec.checkups.length > 0 && (
                <>
                  <Text style={[styles.recommendationTitle, { marginTop: 10, fontSize: 15 }]}>
                    {t('pregnancy.health.recommendedCheckups', { defaultValue: 'Recommended Checkups' })}
                  </Text>
                  {rec.checkups.map((checkup, i) => (
                    <View key={`checkup-${i}`} style={language === 'ur' ? styles.bulletPointRTL : styles.bulletPoint}>
                      <View style={language === 'ur' ? styles.bulletRTL : styles.bullet} />
                      <Text style={[styles.bulletText, language === 'ur' ? styles.urduText : null]}>{checkup}</Text>
                    </View>
                  ))}
                </>
              )}

              {rec.treatments && rec.treatments.length > 0 && (
                <>
                  <Text style={[styles.recommendationTitle, { marginTop: 10, fontSize: 15 }]}>
                    {t('pregnancy.health.recommendedTreatments', { defaultValue: 'Recommended Treatments' })}
                  </Text>
                  {rec.treatments.map((treatment, i) => (
                    <View key={`treatment-${i}`} style={language === 'ur' ? styles.bulletPointRTL : styles.bulletPoint}>
                      <View style={language === 'ur' ? styles.bulletRTL : styles.bullet} />
                      <Text style={[styles.bulletText, language === 'ur' ? styles.urduText : null]}>
                        {treatment.replace(/^AI-recommended\s+/i, '')}
                      </Text>
                    </View>
                  ))}
                </>
              )}
            </View>
          ))}
        </View>
        );
      })}
    </ScrollView>
  );
};

export default HealthTab;
