import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Tabs, useRouter } from 'expo-router';
import { useAuthStore } from '@/store/auth-store';
import { useAnimalStore } from '@/store/animal-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { Home, AlertTriangle, Settings, Bell, Cat, Building2 } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/hooks/useTranslation';

export default function TabsLayout() {
  const router = useRouter();

  const { checkAuthState, user } = useAuthStore();
  const { animals } = useAnimalStore();
  const { healthChecks } = useHealthCheckStore();
  const [notificationCount, setNotificationCount] = useState(0);
  const { t } = useTranslation();

  useEffect(() => {
    console.log('Tabs layout mounted, checking auth state...');
    checkAuthState();
  }, []);

  // Check user role
  useEffect(() => {
    // User role checking logic can be added here if needed
  }, [user]);



  // Check for overdue health checks
  useEffect(() => {
    if (animals.length === 0 || healthChecks.length === 0) return;

    const now = Date.now();
    let count = 0;

    // Count animals with overdue health checks
    animals.forEach(animal => {
      // Get the latest health check for this animal
      const animalHealthChecks = healthChecks
        .filter(check => check.animalId === animal.id)
        .sort((a, b) => b.date - a.date);

      if (animalHealthChecks.length === 0) {
        // No health checks yet, consider it overdue if animal was added more than 7 days ago
        if (now - animal.createdAt > 7 * 24 * 60 * 60 * 1000) {
          count++;
        }
      } else {
        const latestCheck = animalHealthChecks[0];
        // Check if next check date is in the past
        if (latestCheck.nextCheckDate && latestCheck.nextCheckDate < now) {
          count++;
        }
      }
    });

    // Count animals with abnormalities in their latest health check
    const animalsWithAbnormalities = animals.filter(animal => {
      const animalHealthChecks = healthChecks
        .filter(check => check.animalId === animal.id)
        .sort((a, b) => b.date - a.date);

      if (animalHealthChecks.length === 0) return false;
      return animalHealthChecks[0].abnormalities;
    }).length;

    setNotificationCount(count + animalsWithAbnormalities);
  }, [animals, healthChecks]);

  // Handle notification bell press
  const handleNotificationPress = () => {
    router.push('/alerts');
  };

  // Custom header right component with notification bell
  const NotificationBell = () => {
    return (
      <TouchableOpacity
        style={styles.notificationButton}
        onPress={handleNotificationPress}
      >
        <Bell size={24} color={colors.text} />
        {notificationCount > 0 && (
          <View style={styles.notificationBadge}>
            <Text style={styles.notificationCount}>
              {notificationCount > 99 ? '99+' : notificationCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Handle tab press - always allow navigation
  const handleTabPress = () => {
    // Always allow navigation to all tabs
    return true;
  };

  // Common screen options for both layouts
  const screenOptions = {
    tabBarActiveTintColor: colors.primary,
    tabBarInactiveTintColor: colors.textSecondary,
    tabBarStyle: {
      backgroundColor: colors.background,
      borderTopColor: colors.border,
    },
    headerStyle: {
      backgroundColor: colors.background,
    },
    headerTintColor: colors.text,
    headerRight: () => <NotificationBell />,
  };

  // Common tab press listener
  const tabPressListener = {
    tabPress: (e: { preventDefault: () => void }) => {
      if (!handleTabPress()) {
        e.preventDefault();
      }
    },
  };

  // Tab layout for all users
  return (
    <Tabs screenOptions={screenOptions}>
      <Tabs.Screen
        name="index"
        options={{
          title: t('tabs.dashboard'),
          tabBarIcon: ({ color, size }) => <Home size={size} color={color} />,
        }}
        listeners={tabPressListener}
      />
      <Tabs.Screen
        name="animals"
        options={{
          title: t('tabs.animals'),
          tabBarIcon: ({ color, size }) => <Cat size={size} color={color} />,
        }}
        listeners={tabPressListener}
      />
      <Tabs.Screen
        name="symptoms"
        options={{
          title: t('tabs.symptoms'),
          tabBarIcon: ({ color, size }) => <AlertTriangle size={size} color={color} />,
        }}
        listeners={tabPressListener}
      />

      {/* Only show Farms tab for Owner and Admin roles */}
      {(() => {
        const isOwnerOrAdmin = user?.role === 'owner' || user?.role === 'admin';
        console.log('Farms tab check - User role:', user?.role, 'Is Owner or Admin:', isOwnerOrAdmin);

        // Only render the Farms tab if the user is an Owner or Admin
        if (isOwnerOrAdmin) {
          return (
            <Tabs.Screen
              name="farms"
              options={{
                title: t('farms.title'),
                tabBarIcon: ({ color, size }) => <Building2 size={size} color={color} />
              }}
              listeners={tabPressListener}
            />
          );
        }
        return null;
      })()}

      <Tabs.Screen
        name="settings"
        options={{
          title: t('tabs.settings'),
          tabBarIcon: ({ color, size }) => <Settings size={size} color={color} />,
        }}
        listeners={tabPressListener}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  notificationButton: {
    padding: 8,
    marginRight: 8,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  notificationCount: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
