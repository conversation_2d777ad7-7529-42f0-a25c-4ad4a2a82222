import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

interface CountCardProps {
  count: number;
  label: string;
  icon: React.ReactNode;
  onPress?: () => void;
  isActive?: boolean;
}

const CountCard: React.FC<CountCardProps> = ({
  count,
  label,
  icon,
  onPress,
  isActive = false,
}) => {
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors);
  return (
    <TouchableOpacity
      style={[
        styles.card,
        isActive && styles.activeCard
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.iconContainer}>
        {icon}
      </View>
      <Text style={styles.count}>{count}</Text>
      <Text style={styles.label}>{label}</Text>
    </TouchableOpacity>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  card: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    margin: 8,
    shadowColor: themedColors.text, // Or a hardcoded color like '#000'
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    minHeight: 100,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  activeCard: {
    borderColor: themedColors.primary,
    borderWidth: 1,
  },
  iconContainer: {
    marginBottom: 8,
  },
  count: {
    fontSize: 22,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 4,
  },
  label: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
});

export default CountCard;
