import { Animal } from '@/types/animal';

// Calculate next health check date (3 days after last check or today if no check)
const calculateNextHealthCheck = (lastCheck?: number) => {
  if (!lastCheck) return Date.now() + 1000 * 60 * 60 * 24 * 3; // 3 days from now
  return lastCheck + 1000 * 60 * 60 * 24 * 3; // 3 days after last check
};

export const animals: Animal[] = [
  {
    id: '1',
    name: '<PERSON>',
    species: 'Cow',
    breed: 'Holstein Friesian',
    age: 4,
    weight: 550,
    gender: 'female',
    imageUri: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1740&auto=format&fit=crop',
    ownerId: 'user1',
    tagId: '12345',
    nextHealthCheck: calculateNextHealthCheck(Date.now() - 1000 * 60 * 60 * 24 * 2),
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 100, // 100 days ago
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 5, // 5 days ago
  },
  {
    id: '2',
    name: '<PERSON>',
    species: 'Cow',
    breed: '<PERSON><PERSON><PERSON>',
    age: 5,
    weight: 650,
    gender: 'male',
    imageUri: 'https://images.unsplash.com/photo-1527153857715-3908f2bae5e8?q=80&w=1738&auto=format&fit=crop',
    ownerId: 'user1',
    tagId: '23456',
    nextHealthCheck: calculateNextHealthCheck(Date.now() - 1000 * 60 * 60 * 24 * 5),
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 80, // 80 days ago
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 2, // 2 days ago
  },
  {
    id: '3',
    name: 'Daisy',
    species: 'Goat',
    breed: 'Beetal',
    age: 2,
    weight: 45,
    gender: 'female',
    imageUri: 'https://images.unsplash.com/photo-1533318087102-b3ad366ed041?q=80&w=1740&auto=format&fit=crop',
    ownerId: 'user1',
    tagId: '34567',
    nextHealthCheck: calculateNextHealthCheck(Date.now() - 1000 * 60 * 60 * 24 * 10),
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 60, // 60 days ago
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 10, // 10 days ago
  },
  {
    id: '4',
    name: 'Charlie',
    species: 'Poultry',
    breed: 'Rhode Island Red',
    age: 1,
    weight: 2,
    gender: 'male',
    imageUri: 'https://images.unsplash.com/photo-**********-2bdb3c5beed7?q=80&w=1740&auto=format&fit=crop',
    ownerId: 'user1',
    tagId: '45678',
    nextHealthCheck: calculateNextHealthCheck(Date.now() - 1000 * 60 * 60 * 24 * 15),
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 40, // 40 days ago
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 1, // 1 day ago
  },
  {
    id: '5',
    name: 'Nemo',
    species: 'Fish',
    breed: 'Rohu',
    age: 1,
    weight: 0.5,
    gender: 'male',
    imageUri: 'https://images.unsplash.com/photo-1524704654690-b56c05c78a00?q=80&w=1740&auto=format&fit=crop',
    ownerId: 'user1',
    tagId: '56789',
    nextHealthCheck: calculateNextHealthCheck(Date.now() - 1000 * 60 * 60 * 24 * 7),
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 30, // 30 days ago
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 3, // 3 days ago
  },
];