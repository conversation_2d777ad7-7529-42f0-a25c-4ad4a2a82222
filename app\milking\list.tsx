import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { useMilkingStore } from '@/store/milking-store';
import { MilkingRecord, MilkQuality } from '@/types/milking';
import { Milk, Droplets, Plus } from 'lucide-react-native';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { formatDate } from '@/utils/date-utils';

export default function MilkingListScreen() {
  const { t, language } = useTranslation();
  const router = useRouter();
  const themedColors = useThemeColors();
  const { farmId, animalId } = useLocalSearchParams<{ farmId?: string; animalId?: string }>();
  
  const {
    records,
    isLoading,
    error,
    fetchRecordsByFarm,
    fetchRecordsByAnimal,
    clearError,
  } = useMilkingStore();

  const [filteredRecords, setFilteredRecords] = useState<MilkingRecord[]>([]);

  const styles = getStyles(themedColors, language);

  useEffect(() => {
    if (farmId) {
      fetchRecordsByFarm(farmId);
    } else if (animalId) {
      fetchRecordsByAnimal(animalId);
    }
  }, [farmId, animalId]);

  useEffect(() => {
    setFilteredRecords(records);
  }, [records]);

  const getQualityColor = (quality: MilkQuality): string => {
    switch (quality) {
      case MilkQuality.EXCELLENT:
        return '#10B981';
      case MilkQuality.GOOD:
        return '#3B82F6';
      case MilkQuality.FAIR:
        return '#F59E0B';
      case MilkQuality.POOR:
        return '#EF4444';
      default:
        return themedColors.textSecondary;
    }
  };

  const getQualityLabel = (quality: MilkQuality): string => {
    return t(`milking.quality.${quality}`, { defaultValue: quality });
  };

  const getSessionLabel = (session: string): string => {
    return t(`milking.session.${session}`, { defaultValue: session });
  };

  const handleAddMilking = () => {
    if (farmId) {
      router.push(`/milking/add?farmId=${farmId}`);
    } else if (animalId) {
      // Get the farm ID from the first record or navigate to animal's farm
      const firstRecord = records[0];
      if (firstRecord) {
        router.push(`/milking/add?farmId=${firstRecord.farmId}`);
      }
    }
  };

  const renderMilkingRecord = ({ item }: { item: MilkingRecord }) => (
    <TouchableOpacity
      style={[styles.recordCard, language === 'ur' && styles.urduCard]}
      onPress={() => router.push(`/milking/${item.id}`)}
    >
      <View style={[styles.recordHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
        <View style={[styles.animalInfo, language === 'ur' && { alignItems: 'flex-end' }]}>
          <Text style={[styles.animalName, language === 'ur' && styles.urduText]}>
            {item.animalName}
          </Text>
          <Text style={[styles.recordDate, language === 'ur' && styles.urduText]}>
            {formatDate(item.date, language)} • {getSessionLabel(item.session)}
          </Text>
        </View>
        <View style={[
          styles.qualityBadge,
          { backgroundColor: getQualityColor(item.quality) + '20' }
        ]}>
          <Text style={[
            styles.qualityBadgeText,
            { color: getQualityColor(item.quality) },
            language === 'ur' && styles.urduText
          ]}>
            {getQualityLabel(item.quality)}
          </Text>
        </View>
      </View>
      
      <View style={[styles.recordBody, language === 'ur' && { flexDirection: 'row-reverse' }]}>
        <View style={[styles.quantityContainer, language === 'ur' && { alignItems: 'flex-end' }]}>
          <View style={styles.quantityRow}>
            <Droplets size={20} color={themedColors.primary} />
            <Text style={[styles.quantityValue, language === 'ur' && styles.urduText]}>
              {item.quantity.toFixed(1)} {t('milking.liters')}
            </Text>
          </View>
        </View>
        
        {item.notes && (
          <View style={[styles.notesContainer, language === 'ur' && { alignItems: 'flex-end' }]}>
            <Text style={[styles.notesText, language === 'ur' && styles.urduText]} numberOfLines={2}>
              {item.notes}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  if (error) {
    // Check if it's a Firebase indexing error
    const isIndexError = error.includes('index') || error.includes('composite') || error.includes('query requires');

    if (isIndexError) {
      // Show empty state for indexing errors instead of technical error
      return (
        <SafeAreaView style={styles.container} edges={['bottom']}>
          <Stack.Screen
            options={{
              title: t('milking.title'),
              headerBackTitle: t('common.back'),
            }}
          />
          <View style={styles.emptyContainer}>
            <EmptyState
              title={t('milking.noRecords')}
              message={t('milking.addYourFirstRecord')}
              actionLabel={t('milking.addRecord')}
              onAction={handleAddMilking}
              icon={<Milk size={48} color={themedColors.primary} />}
            />
          </View>
        </SafeAreaView>
      );
    }

    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <Stack.Screen
          options={{
            title: t('milking.title'),
            headerBackTitle: t('common.back'),
          }}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              clearError();
              if (farmId) {
                fetchRecordsByFarm(farmId);
              } else if (animalId) {
                fetchRecordsByAnimal(animalId);
              }
            }}
          >
            <Text style={styles.retryButtonText}>{t('common.retry')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (filteredRecords.length === 0) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <Stack.Screen
          options={{
            title: t('milking.title'),
            headerBackTitle: t('common.back'),
          }}
        />
        <View style={styles.emptyContainer}>
          <EmptyState
            title={t('milking.noRecords')}
            message={t('milking.addYourFirstRecord')}
            actionLabel={t('milking.addRecord')}
            onAction={handleAddMilking}
            icon={<Milk size={48} color={themedColors.primary} />}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('milking.title'),
          headerBackTitle: t('common.back'),
        }}
      />
      
      <View style={styles.content}>
        {/* Header Stats */}
        <View style={styles.statsHeader}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, language === 'ur' && styles.urduText]}>
              {filteredRecords.length}
            </Text>
            <Text style={[styles.statLabel, language === 'ur' && styles.urduText]}>
              {t('milking.records', { defaultValue: 'Records' })}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, language === 'ur' && styles.urduText]}>
              {filteredRecords.reduce((sum, record) => sum + record.quantity, 0).toFixed(1)}L
            </Text>
            <Text style={[styles.statLabel, language === 'ur' && styles.urduText]}>
              {t('milking.totalProduction')}
            </Text>
          </View>
        </View>

        {/* Records List */}
        <FlatList
          data={filteredRecords}
          keyExtractor={item => item.id}
          renderItem={renderMilkingRecord}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      </View>

      {/* Add Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={handleAddMilking}
      >
        <Plus size={24} color="white" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: themedColors.background,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    statsHeader: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      justifyContent: 'space-around',
      backgroundColor: themedColors.card,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: themedColors.border,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: 24,
      fontWeight: 'bold',
      color: themedColors.primary,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      color: themedColors.textSecondary,
      textAlign: 'center',
    },
    recordCard: {
      backgroundColor: themedColors.card,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: themedColors.border,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    urduCard: {
      alignItems: 'flex-end',
    },
    recordHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    animalInfo: {
      flex: 1,
    },
    animalName: {
      fontSize: 16,
      fontWeight: 'bold',
      color: themedColors.text,
      marginBottom: 4,
    },
    recordDate: {
      fontSize: 14,
      color: themedColors.textSecondary,
    },
    qualityBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    qualityBadgeText: {
      fontSize: 12,
      fontWeight: '600',
    },
    recordBody: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    quantityContainer: {
      flex: 1,
    },
    quantityRow: {
      flexDirection: language === 'ur' ? 'row-reverse' : 'row',
      alignItems: 'center',
      gap: 8,
    },
    quantityValue: {
      fontSize: 16,
      fontWeight: '600',
      color: themedColors.text,
    },
    notesContainer: {
      flex: 2,
      marginLeft: language === 'ur' ? 0 : 16,
      marginRight: language === 'ur' ? 16 : 0,
    },
    notesText: {
      fontSize: 14,
      color: themedColors.textSecondary,
      fontStyle: 'italic',
    },
    listContent: {
      paddingBottom: 80,
    },
    addButton: {
      position: 'absolute',
      bottom: 20,
      right: 20,
      backgroundColor: themedColors.primary,
      width: 56,
      height: 56,
      borderRadius: 28,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorText: {
      fontSize: 16,
      color: themedColors.error,
      textAlign: 'center',
      marginBottom: 20,
    },
    retryButton: {
      backgroundColor: themedColors.primary,
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 8,
    },
    retryButtonText: {
      color: 'white',
      fontWeight: '600',
    },
    urduText: {
      textAlign: 'right',
    },
  });
