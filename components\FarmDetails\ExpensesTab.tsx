import React from 'react';
import { View, Text, Dimensions, TouchableOpacity } from 'react-native';
import { DollarSign } from 'lucide-react-native';
import { LineChart } from 'react-native-chart-kit';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { getCurrencySymbol } from '@/utils/currency-utils';

interface ExpensesTabProps {
  expensesByCategory: { category: string; amount: number }[];
  isLoadingExpenseChart: boolean;
  onAddExpense: () => void;
  onViewAllExpenses: () => void;
  styles: any;
}

export default function ExpensesTab({
  expensesByCategory,
  isLoadingExpenseChart,
  onAddExpense,
  onViewAllExpenses,
  styles
}: ExpensesTabProps) {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();

  if (isLoadingExpenseChart) {
    return (
      <View style={styles.tabContent}>
        <LoadingIndicator message={t('common.loading')} />
      </View>
    );
  }

  if (expensesByCategory.length === 0) {
    return (
      <View style={styles.tabContent}>
        <EmptyState
          title={t("expenses.noExpenses")}
          actionLabel={t('expenses.addExpense')}
          message={t("expenses.addYourFirstExpense")}     
          onAction={onAddExpense}       
          icon={<DollarSign size={48} color={themedColors.primary} />}
        />
      </View>
    );
  }

  // Calculate screen width with more padding to prevent overflow
  const screenWidth = Dimensions.get('window').width - 30;

  // Prepare data for bar chart
  const chartData = {
    labels: expensesByCategory.map(item => item.category.substring(0, 6)), // Shorter labels
    datasets: [
      {
        data: expensesByCategory.map(item => item.amount),
        color: (opacity = 1) => themedColors.primary || `rgba(71, 117, 234, ${opacity})`,
        strokeWidth: 2
      }
    ],
    legend: ["Expense Amount"]
  };

  // Calculate total expenses
  const totalExpenseAmount = expensesByCategory.reduce((sum, item) => sum + item.amount, 0);

  return (
    <View style={styles.tabContent}>
      {/* Header with title and View All button */}

      <View style={[styles.expensesHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
        <Text style={[styles.chartTitle, language === 'ur' && styles.urduText]}>
          {t('farms.expensesByCategory')}
        </Text>
        <TouchableOpacity
          style={styles.viewAllButton}
          onPress={onViewAllExpenses}
        >
          <Text style={[styles.viewAllText, language === 'ur' && styles.urduText]}>
            {t('common.viewAll')}
          </Text>
        </TouchableOpacity>
      </View>
      <View style={styles.chartContainer}>
        <LineChart
          data={chartData}
          width={screenWidth}
          height={220}
          yAxisLabel={getCurrencySymbol('PKR')}
          chartConfig={{
            backgroundColor: themedColors.card,
            backgroundGradientFrom: themedColors.card,
            backgroundGradientTo: themedColors.card,
            decimalPlaces: 0,
            color: (opacity = 1) => themedColors.primary || `rgba(71, 117, 234, ${opacity})`,
            labelColor: (opacity = 1) => themedColors.text || `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 16
            },
            propsForLabels: {
              fontSize: 10,
            }
          }}
          style={{
            marginVertical: 8,
            borderRadius: 16,
          }}
          fromZero={true}
        />
      </View>

      <View style={styles.expenseSummary}>
        <Text style={styles.summaryTitle}>{t('farms.expenseDetails')}</Text>
        {expensesByCategory.map((item, index) => (
          <View key={index} style={[
            styles.expenseCategoryItem,
            language === 'ur' && styles.urduExpenseCategoryItem
          ]}>
            <View style={[styles.categoryDot, { 
              backgroundColor: themedColors.primary || `rgba(71, 117, 234, ${0.7 + (index * 0.05)})` 
            }]} />
            <Text style={[
              styles.categoryName,
              language === 'ur' && styles.urduText
            ]}>
              {t(`expenses.category.${item.category.toLowerCase().replace(/\s+/g, '')}`, { 
                defaultValue: item.category 
              })}
            </Text>
            <Text style={styles.categoryAmount}>{getCurrencySymbol('PKR')} {item.amount.toFixed(2)}</Text>
          </View>
        ))}
        <View style={[styles.totalRow, language === 'ur' && styles.urduExpenseCategoryItem]}>
          <Text style={styles.totalLabel}>{t('expenses.totalExpenses')}</Text>
          <Text style={styles.totalAmount} numberOfLines={1} ellipsizeMode="tail">
            {getCurrencySymbol('PKR')} {totalExpenseAmount.toFixed(2)}
          </Text>
        </View>
      </View>
    </View>
  );
}
