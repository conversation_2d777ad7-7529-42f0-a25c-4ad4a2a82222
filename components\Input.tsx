import React, { useState } from 'react';
import { View, TextInput, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Eye, EyeOff } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook

interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  secureTextEntry?: boolean;
  isPassword?: boolean;
  error?: string;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad' | 'number-pad' | 'decimal-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  multiline?: boolean;
  numberOfLines?: number;
  style?: any;
  helperText?: string;
  disabled?: boolean; // Added disabled prop
  editable?: boolean; // Add editable prop
  textStyle?: any; // Added for RTL text styling
  onBlur?: () => void; // Add onBlur prop
}

const Input: React.FC<InputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  secureTextEntry = false,
  isPassword = false,
  error,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  leftIcon,
  rightIcon,
  multiline = false,
  numberOfLines = 1,
  style,
  helperText,
  disabled = false, // Default to false
  editable = true, // Default to true
  textStyle,
  onBlur,
  ...props
}) => {
  const { t, language } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const themedColors = useThemeColors(); // Use the theme hook
  
  // Define errorColor with a fallback if not in themedColors
  const errorColor = themedColors.error || (themedColors.isDarkMode ? '#F87171' : '#DC2626');

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <View style={[styles.container, style]}>
      {label && (
        <Text style={[
          styles.label, 
          { color: themedColors.text }, 
          language === 'ur' ? styles.urduText : null
        ]}>
          {label}
        </Text>
      )}

      <View style={[
        styles.inputContainer,
        { 
          backgroundColor: themedColors.card, 
          borderColor: isFocused 
            ? (themedColors.isDarkMode ? '#60A5FA' : themedColors.primary) // Example: Light blue for dark mode focus
            : (error ? errorColor : themedColors.border) 
        },       
         isFocused && { borderWidth: 2 }, // Keep focused border width increase
        disabled && styles.inputDisabled
      ]}>
        {leftIcon && (
          <View style={[
            styles.iconContainer,
            multiline && styles.multilineIconContainer
          ]}>
            {leftIcon}
          </View>
        )}

        <TextInput
          style={[
            styles.input,
            leftIcon && styles.inputWithLeftIcon,
            (rightIcon || isPassword) && styles.inputWithRightIcon,
            multiline && styles.multilineInput,
            { color: themedColors.text },
            (disabled || !editable) && { color: themedColors.textSecondary }, // Apply disabled style if not editable
            language === 'ur' ? styles.urduInput : null,
            textStyle
          ]}
          placeholder={placeholder || ''}
          placeholderTextColor={themedColors.textSecondary}
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={isPassword ? !showPassword : secureTextEntry}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          multiline={multiline}
          numberOfLines={multiline ? numberOfLines : 1}
          onFocus={() => setIsFocused(true)}
          onBlur={() => {
            setIsFocused(false);
            if (onBlur) {
              onBlur();
            }
          }}
          editable={editable && !disabled} // Respect both editable and disabled props
          textAlignVertical={multiline ? "top" : "center"}
          {...props}
        />

        {isPassword ? (
          <TouchableOpacity
            style={styles.iconContainer}
            onPress={togglePasswordVisibility}
          >
            {showPassword ? (
              <EyeOff size={20} color={themedColors.textSecondary} />
            ) : (
              <Eye size={20} color={themedColors.textSecondary} />
            )}
          </TouchableOpacity>
        ) : rightIcon ? (
          <View style={styles.iconContainer}>{rightIcon}</View>
        ) : null}
      </View>

      {helperText && !error && (
        <Text style={[styles.helperText, { color: themedColors.textSecondary }, language === 'ur' ? styles.urduText : null]}>
          {t(helperText)}
        </Text>
      )}

      {error && (
        <Text style={[styles.errorText, { color: errorColor }, language === 'ur' ? styles.urduText : null]}>
          {t(error)}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  urduInput: {
    textAlign: 'right',
  },
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    // color will be set dynamically
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    // borderColor will be set dynamically
    borderRadius: 8,
    // backgroundColor will be set dynamically
  },
  inputFocused: {
    // borderColor will be set dynamically
    borderWidth: 2,
  },
  inputDisabled: {
    backgroundColor: '#f5f5f5',
    opacity: 0.7,
  },
  iconContainer: {
    paddingHorizontal: 12,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  multilineIconContainer: {
    alignSelf: 'flex-start',
    paddingTop: 12,
  },
  input: {
    flex: 1,
    height: 50,
    paddingHorizontal: 16,
    fontSize: 16,
    // color will be set dynamically
  },
  inputWithLeftIcon: {
    paddingLeft: 8,
  },
  inputWithRightIcon: {
    paddingRight: 8,
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
    paddingBottom: 12,
  },
  errorText: {
    // color will be set dynamically
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  helperText: {
    // color will be set dynamically
    marginTop: 4,
    marginLeft: 4,
  },
});

export default Input;
