import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
  Image,
  StyleProp,
  ViewStyle,
  ImageSourcePropType
} from 'react-native';
import { ChevronDown, Search, X, Plus } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook
import {useTranslation} from '@/hooks/useTranslation';

export interface DropdownItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  imageUri?: ImageSourcePropType ;
  description?: string;
}

interface GenericDropdownProps {
  placeholder: string;
  items: DropdownItem[];
  value: string | undefined | null;
  onSelect: (itemId: string) => void;
  onChangeText?: (text: string) => void;
  modalTitle?: string;
  searchPlaceholder?: string;
  renderIcon?: React.ReactNode;
  allowCustomOption?: boolean;
  customOptionLabel?: string;
  onAddCustomOption?: (value: string) => void;
  error?: string;
  showErrorText?: boolean; // Option to hide error text and only show red border
  containerStyle?: StyleProp<ViewStyle>;
  disabled?: boolean;
  isDarkMode?: boolean; // Allow parent to pass dark mode state
  height?: number; // Custom height for the dropdown button
}

const GenericDropdown: React.FC<GenericDropdownProps> = ({
  placeholder,
  items,
  value,
  onSelect,
  onChangeText,
  modalTitle = "Select an option",
  searchPlaceholder = "Search...",
  renderIcon,
  allowCustomOption = false,
  customOptionLabel = "Add new",
  onAddCustomOption,
  error,
  showErrorText = true, // Default to showing error text
  containerStyle,
  disabled = false,
  isDarkMode: isDarkModeProp, // Renaming to avoid conflict with hook's isDarkMode
  height = 48, // Default height
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItem, setSelectedItem] = useState<DropdownItem | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newItemName, setNewItemName] = useState('');
  const { t } = useTranslation();

  // Use the theme hook for colors
  const themeHookColors = useThemeColors();
  // Prioritize prop if passed, otherwise use hook's value
  const isDarkMode = isDarkModeProp !== undefined ? isDarkModeProp : themeHookColors.isDarkMode;
  
  // Define specific colors that might not be in the core theme hook, using fallbacks
  const primaryLightColor = isDarkMode ? '#A5B4FC' : '#E0E7FF';
  const errorColor = themeHookColors.error || (isDarkMode ? '#F87171' : '#DC2626');
  const imageBorderColor = isDarkMode ? themeHookColors.border : '#E5E7EB';

  useEffect(() => {
    // Find the selected item when value changes
    if (value) {
      const item = items?.find(i => i.id === value);
      setSelectedItem(item || null);
    } else {
      setSelectedItem(null);
    }
  }, [value]); // Removed items from dependency array to prevent infinite loops

  // Filter items based on search query
  const filteredItems = items?.filter(item =>
    item.label?.toLowerCase().includes(searchQuery?.toLowerCase()) ||
    (item.description && item.description?.toLowerCase().includes(searchQuery?.toLowerCase()))
  );

  // Handle item selection
  const handleSelect = (item: DropdownItem) => {
    onSelect(item.id);
    setSelectedItem(item);
    setModalVisible(false);
    setSearchQuery('');
  };

  // Handle search input change
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    if (onChangeText) {
      onChangeText(text);
    }
  };

  // Close modal
  const handleClose = () => {
    setModalVisible(false);
    setSearchQuery('');
  };

  // Clear selection
  const handleClear = () => {
    onSelect('');
    setSelectedItem(null);
    setSearchQuery('');
  };

  const handleAddNewItem = () => {
    if (newItemName.trim() && onAddCustomOption) {
      onAddCustomOption(newItemName.trim());
      setNewItemName('');
      setIsAddingNew(false);
      setModalVisible(false);
    }
  };

  const renderFooter = () => {
    if (!allowCustomOption) return null;

    if (isAddingNew) {
      return (
        <View style={styles.addNewContainer}>
          <TextInput
            style={[styles.addNewInput, { color: themeHookColors.text, borderColor: themeHookColors.border }]}
            value={newItemName}
            onChangeText={setNewItemName}
            placeholder="Enter name..."
            placeholderTextColor={themeHookColors.textSecondary} // Theme placeholder
            autoFocus
          />
          <TouchableOpacity
            style={[styles.addNewButton, { backgroundColor: themeHookColors.primary }]}
            onPress={handleAddNewItem} // This button's background is already themed via styles.addNewButton
          >
            <Text style={styles.addNewButtonText}>Add</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <TouchableOpacity
        style={styles.addNewOption}
        onPress={() => setIsAddingNew(true)}
      >
        <Plus size={20} color={themeHookColors.primary} />
        <Text style={[styles.addNewText, { color: themeHookColors.primary }]}>{customOptionLabel}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Dropdown Button */}
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          { backgroundColor: themeHookColors.card }, // Apply themed background
          error ? { borderColor: errorColor } : { borderColor: themeHookColors.border }, // Apply error or normal border
          disabled ? styles.disabledButton : null,
        ]}
        onPress={() => !disabled && setModalVisible(true)}
        disabled={disabled}
      >
        <View style={[styles.searchContainer, { height }]}>
          {!selectedItem && (
            <View style={styles.iconContainer}>
              {renderIcon || <Search size={20} color={themeHookColors.textSecondary} />} 
            </View>
          )}

          {selectedItem ? (
            <View style={styles.selectedItemContainer}>
              {selectedItem.imageUri && (
                <Image
                  source={selectedItem.imageUri }
                  style={[styles.selectedItemImage, { borderColor: imageBorderColor }]}
                  resizeMode="cover"
                />
              )}
              {selectedItem.icon && (
                <View style={[styles.selectedItemIconContainer, { borderColor: themeHookColors.border, backgroundColor: themeHookColors.background }]}>
                  {selectedItem.icon}
                </View>
              )}
              <Text style={[styles.selectedItemText, { color: themeHookColors.text }]}>
                {selectedItem.label}
              </Text>
              <TouchableOpacity onPress={handleClear} style={styles.clearButton}>
                <X size={16} color={themeHookColors.textSecondary} />
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.placeholderContainer}>
              <Text style={[styles.placeholderText, { color: themeHookColors.textSecondary }]}>{placeholder}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>

      {/* Error message - positioned absolutely to avoid layout disruption */}
      {error && showErrorText && (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: errorColor }]}>{error}</Text>
        </View>
      )}

      {/* Modal for dropdown */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={handleClose}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: themeHookColors.background }]}>
            <View style={[styles.modalHeader, { borderBottomColor: themeHookColors.border }]}>
              <Text style={[styles.modalTitle, { color: themeHookColors.text }]}>{modalTitle}</Text>
              <TouchableOpacity onPress={handleClose}>
                <X size={24} color={themeHookColors.text} />
              </TouchableOpacity>
            </View>

            <View style={[styles.searchModalContainer, { backgroundColor: themeHookColors.card, borderBottomColor: themeHookColors.border }]}>
              <Search size={20} color={themeHookColors.textSecondary} style={styles.searchIcon} />
              <TextInput
                style={[styles.searchInput, { color: themeHookColors.text }]}
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChangeText={handleSearchChange}
                autoCapitalize="none"
                autoCorrect={false}
                placeholderTextColor={themeHookColors.textSecondary} // Theme placeholder
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <X size={20} color={themeHookColors.textSecondary} />
                </TouchableOpacity>
              )}
            </View>

            <FlatList
              data={filteredItems}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.item,
                    { borderBottomColor: themeHookColors.border }, // Theme border
                    value === item.id && [styles.selectedItem, { backgroundColor: primaryLightColor }]
                  ]}
                  onPress={() => handleSelect(item)}
                >
                  {item.imageUri && (
                    <Image
                      source={ item.imageUri }
                      style={[styles.itemImage, { borderColor: imageBorderColor, backgroundColor: themeHookColors.background }]}
                      resizeMode="cover"
                    />
                  )}
                  {item.icon && (
                    <View style={[styles.itemIconContainer, { borderColor: themeHookColors.border, backgroundColor: themeHookColors.background }]}>
                      {item.icon}
                    </View>
                  )}
                  <View style={styles.itemInfo}>
                    <Text style={[
                      styles.itemName,
                      { color: themeHookColors.text }, 
                      value === item.id && [styles.selectedItemName, { color: themeHookColors.primary }]
                    ]}>
                      {item.label}
                    </Text>
                    {item.description && (
                      <Text style={[styles.itemDescription, { color: themeHookColors.textSecondary }]}>
                        {item.description}
                      </Text>
                    )}
                  </View>
                </TouchableOpacity>
              )}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={[styles.emptyText, { color: themeHookColors.textSecondary }]}>
                    No items found matching "{searchQuery}"
                  </Text>
                </View>
              }
              ListFooterComponent={renderFooter}
              style={styles.itemsList}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative', // Enable absolute positioning for error text
  },
  dropdownButton: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    // borderColor and backgroundColor are applied dynamically in the JSX
    // to ensure they use the themed 'colors' object.
  },
  disabledButton: {
    opacity: 0.5,
    // backgroundColor will be set by theme
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 0,
    // height will be set dynamically
  },
  selectedItemContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
    height: '100%', // Ensure it fills the button height
  },
  iconContainer: {
    width: 24, // Reduced size to match the image
    height: 24, // Reduced size to match the image
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8, // Add spacing between icon and text
  },
  selectedItemImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 8,
    borderWidth: 1, // Ensure border is visible
    // borderColor is applied dynamically
  },
  selectedItemIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
    borderWidth: 1, // Ensure border is visible
    // borderColor and backgroundColor are applied dynamically
  },
  selectedItemText: { 
    flex: 1,
    fontSize: 18,
    // color will be set by theme
    textAlignVertical: 'center',
    lineHeight: 24,
  },
  clearButton: {
    padding: 4,
  },
  placeholderContainer: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
    alignItems: 'flex-start', // Change to left alignment
  },
  placeholderText: {
    fontSize: 16,
    // color will be set by theme
    textAlign: 'left', // Ensure text is left-aligned
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    // backgroundColor will be set by theme
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1, 
    // borderBottomColor will be set by theme
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    // color will be set by theme
  },
  searchModalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    // borderBottomColor will be set by theme
    // backgroundColor will be set by theme
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    // color will be set by theme
  },
  itemsList: {
    maxHeight: 400,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    paddingVertical: 16,
    borderBottomWidth: 1,
    // borderBottomColor will be set by theme (applied dynamically)
  },
  selectedItem: {
    // backgroundColor will be set by theme
  },
  itemImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    borderWidth: 1,
    // borderColor and backgroundColor are applied dynamically
  },
  itemIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    borderWidth: 1,
    // borderColor and backgroundColor are applied dynamically
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 18,
    fontWeight: '600',
    // color will be set by theme
    marginBottom: 2,
  },
  selectedItemName: {
    // color will be set by theme
  },
  itemDescription: {
    fontSize: 14,
    // color will be set by theme
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    // color will be set by theme
    textAlign: 'center',
  },
  addNewContainer: {
    flexDirection: 'row',
    padding: 12,
    borderTopWidth: 1,
    // borderTopColor will be set by theme (applied dynamically)
    alignItems: 'center',
  },
  addNewInput: {
    flex: 1,
    padding: 8,
    borderWidth: 1,
    // borderColor will be set by theme
    borderRadius: 4, 
    marginRight: 8,
    // color will be set by theme (applied directly in the TextInput style prop)
  },
  addNewButton: {
    // backgroundColor will be set dynamically
    padding: 8,
    borderRadius: 4,
  },
  addNewButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  addNewOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderTopWidth: 1,
    // borderTopColor will be set by theme (applied dynamically)
  },
  addNewText: {
    marginLeft: 8,
    // color will be set dynamically
    fontWeight: 'bold',
  },
  errorContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingTop: 2,
  },
  errorText: {
    // color will be set by theme
    fontSize: 11,
    lineHeight: 14,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
});

export default GenericDropdown;
