export interface MilkingRecord {
  id: string;
  farmId: string;
  farmName: string;
  animalId: string;
  animalName: string;
  animalSpecies: string;
  date: number; // timestamp
  session: 'morning' | 'afternoon' | 'evening';
  quantity: number; // in liters
  quality: MilkQuality;
  fat?: number; // fat percentage
  protein?: number; // protein percentage
  temperature?: number; // milk temperature in celsius
  notes?: string;
  milkedBy?: string; // staff member who performed milking
  milkedByName?: string;
  createdBy: string;
  tenantId: string;
  createdAt: number;
  updatedAt: number;
}

export enum MilkQuality {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor',
}

export interface MilkingSession {
  id: string;
  farmId: string;
  date: number;
  session: 'morning' | 'afternoon' | 'evening';
  totalQuantity: number;
  animalCount: number;
  averageQuality: MilkQuality;
  records: MilkingRecord[];
  createdBy: string;
  tenantId: string;
  createdAt: number;
  updatedAt: number;
}

export interface MilkingStats {
  totalQuantity: number;
  averageDaily: number;
  averagePerAnimal: number;
  qualityDistribution: Record<MilkQuality, number>;
  topProducers: Array<{
    animalId: string;
    animalName: string;
    totalQuantity: number;
    averageDaily: number;
  }>;
  monthlyTrend: Array<{
    month: string;
    quantity: number;
  }>;
}
