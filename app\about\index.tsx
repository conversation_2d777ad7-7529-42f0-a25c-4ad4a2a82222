import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Linking,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import {
  Heart,
  Mail,
  Globe,
  Shield,
  FileText,
  HelpCircle,
  Github,
  Twitter
} from 'lucide-react-native';
import { Stack } from 'expo-router';
export default function AboutScreen() {
  const { t } = useTranslation();
  const themedColors = useThemeColors();

  const handleOpenLink = (url: string) => {
    Linking.openURL(url).catch(err => console.error("Couldn't open link", err));
  };

  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('about.aboutTitle'), // Or t('settings.language') if that's your key
          headerBackTitle: t('common.back'),    // Optional: for the back button text
        }}
      />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Image
            source={{ uri: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1170&auto=format&fit=crop' }}
            style={styles.logo}
          />
          <Text style={styles.appName}>{t('about.appName')}</Text>
          <Text style={styles.version}>{t('about.version')} 1.0.0</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('about.aboutTitle')}</Text>
          <Text style={styles.description}>
            {t('about.description')}
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('about.featuresTitle')}</Text>
          <View style={styles.featureList}>
            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Heart size={20} color={themedColors.primary} />
              </View>
              <Text style={styles.featureText}>{t('about.featureHealthTracking')}</Text>
            </View>
            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <HelpCircle size={20} color={themedColors.primary} />
              </View>
              <Text style={styles.featureText}>{t('about.featureSymptomId')}</Text>
            </View>
            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Shield size={20} color={themedColors.primary} />
              </View>
              <Text style={styles.featureText}>{t('about.featureOfflineAccess')}</Text>
            </View>
            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Globe size={20} color={themedColors.primary} />
              </View>
              <Text style={styles.featureText}>{t('about.featureLanguageSupport')}</Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('about.contactTitle')}</Text>
          <TouchableOpacity
            style={styles.contactItem}
            onPress={() => handleOpenLink('mailto:<EMAIL>')}
          >
            <View style={styles.contactIcon}>
              <Mail size={20} color={themedColors.primary} />
            </View>
            <Text style={styles.contactText}><EMAIL></Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.contactItem}
            onPress={() => handleOpenLink('https://www.livestocktracker.com')}
          >
            <View style={styles.contactIcon}>
              <Globe size={20} color={themedColors.primary} />
            </View>
            <Text style={styles.contactText}>www.livestocktracker.com</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('about.legalTitle')}</Text>
          <TouchableOpacity
            style={styles.legalItem}
            onPress={() => handleOpenLink('https://www.livestocktracker.com/privacy')}
          >
            <View style={styles.legalIcon}>
              <Shield size={20} color={themedColors.primary} />
            </View>
            <Text style={styles.legalText}>{t('settings.privacyPolicy')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.legalItem}
            onPress={() => handleOpenLink('https://www.livestocktracker.com/terms')}
          >
            <View style={styles.legalIcon}>
              <FileText size={20} color={themedColors.primary} />
            </View>
            <Text style={styles.legalText}>{t('settings.termsOfService')}</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.socialLinks}>
          <TouchableOpacity
            style={styles.socialButton}
            onPress={() => handleOpenLink('https://github.com/livestocktracker')}
          >
            <Github size={24} color={themedColors.text} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.socialButton}
            onPress={() => handleOpenLink('https://twitter.com/livestocktracker')}
          >
            <Twitter size={24} color={themedColors.text} />
          </TouchableOpacity>
        </View>

        <Text style={styles.copyright}>
          © {new Date().getFullYear()} {t('about.copyright')}
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 4,
  },
  version: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: themedColors.text,
    lineHeight: 24,
  },
  featureList: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: themedColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  featureText: {
    fontSize: 16,
    color: themedColors.text,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  contactIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: themedColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contactText: {
    fontSize: 16,
    color: themedColors.primary,
    textDecorationLine: 'underline',
  },
  legalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  legalIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: themedColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  legalText: {
    fontSize: 16,
    color: themedColors.primary,
    textDecorationLine: 'underline',
  },
  socialLinks: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
  },
  socialButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: themedColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  copyright: {
    textAlign: 'center',
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 16,
  },
});