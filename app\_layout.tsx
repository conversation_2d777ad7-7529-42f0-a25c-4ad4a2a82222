import { useEffect, useState } from 'react';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useColorScheme } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';

import { useSettingsStore } from '@/store/settings-store';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { View, ActivityIndicator, StyleSheet, Image, Text } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ToastProvider } from '@/contexts/ToastContext';
import { useTranslation } from '@/hooks/useTranslation';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const systemColorScheme = useColorScheme();
  const { darkMode } = useSettingsStore();
  const [isAppReady, setIsAppReady] = useState(false);
  const [splashAnimationComplete, setSplashAnimationComplete] = useState(false);
  const { t } = useTranslation();

  // Use the app's dark mode setting, falling back to system preference
  const colorScheme = darkMode !== null ? (darkMode ? 'dark' : 'light') : systemColorScheme;

  // Initialize app
  useEffect(() => {
    console.log('RootLayout mounted, initializing app...');

    // Set app as ready after a short delay
    const timer = setTimeout(() => {
      console.log('App ready, hiding splash screen');
      setIsAppReady(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Handle splash screen hiding
  useEffect(() => {
    if (isAppReady) {
      // Start splash screen exit animation
      const timer = setTimeout(() => {
        setSplashAnimationComplete(true);
        SplashScreen.hideAsync().catch(e => console.warn("Error hiding splash screen:", e));
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isAppReady]);

  // Show custom splash screen while loading
  if (!isAppReady || !splashAnimationComplete) {
    return <CustomSplashScreen />;
  }

  // Set theme colors based on color scheme
  const appColors = colorScheme === 'dark'
    ? {
        ...colors,
        primary: '#A78B71',
        primaryLight: 'rgba(167, 139, 113, 0.2)',
        background: '#1F2937',
        card: '#374151',
        cardLight: '#4B5563',
        text: '#F9FAFB',
        textSecondary: '#D1D5DB',
        border: '#4B5563',
      }
    : {
        ...colors,
        primary: '#A78B71',
        primaryLight: 'rgba(167, 139, 113, 0.2)',
        background: '#FFFFFF',
        card: '#F9F9F9',
        cardLight: '#F5F5F5',
        text: '#333333',
        textSecondary: '#666666',
        border: '#E0E0E0',
      };

  return (
    <SafeAreaProvider>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <ToastProvider>
          <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
          <Stack
            screenOptions={{
              headerStyle: {
                backgroundColor: appColors.card,
              },
              headerTintColor: appColors.text,
              headerTitleStyle: {
                fontWeight: '600',
              },
              contentStyle: {
                backgroundColor: appColors.background,
              },
            }}
          >
            <Stack.Screen name="index" options={{ headerShown: false }} />
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen name="(auth)" options={{ headerShown: false }} />
            <Stack.Screen name="modal" options={{ presentation: 'modal' }} />
            <Stack.Screen name="health-checks/add" options={{ presentation: 'modal' }} />
            <Stack.Screen name="profile" options={{ title: t('profile.title') }} />
            <Stack.Screen name="account/update" options={{ title: t('account.updateAccount') }} />
            <Stack.Screen name="language-selection" options={{ title: t('language.selectLanguage') }} />
            <Stack.Screen name="text-size" options={{ title: t('settings.textSize') }} />
            <Stack.Screen name="about" options={{ title: t('settings.about') }} />
            <Stack.Screen name="symptoms-checker" options={{ title: t('symptoms.title') }} />
          </Stack>
        </ToastProvider>
      </GestureHandlerRootView>
    </SafeAreaProvider>
  );
}

// Custom splash screen component
function CustomSplashScreen() {
  // Use English for splash screen as it loads before language settings
  // This is fine since splash screen is very brief
  return (
    <LinearGradient
      colors={['#2C3E50', '#4A5568']}
      style={styles.container}
    >
      <View style={styles.content}>
        <Image
          source={require('../assets/images/ll.png')}
          style={styles.logo}
        />
        <Text style={styles.title}>Livestock Tracker</Text>
        <Text style={styles.subtitle}>Manage your farm efficiently</Text>
        <ActivityIndicator
          size="large"
          color="#A78B71"
          style={styles.loader}
        />
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 3,
    borderColor: '#A78B71',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#E0E0E0',
    marginBottom: 32,
  },
  loader: {
    marginTop: 20,
  },
});
