import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  Alert, 
  Image,
  ActivityIndicator,
  StyleSheet
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '@/store/auth-store';
import { useExpenseStore } from '@/store/expense-store';
import { Expense, ExpenseCategory, PaymentMethod } from '@/types/expense';
import * as ImagePicker from 'expo-image-picker';
import { 
  ArrowLeft, 
  Save, 
  Calendar, 
  DollarSign, 
  Tag, 
  FileText, 
  Building2, 
  Camera, 
  Image as ImageIcon,
  CreditCard,
  Cat,
  Wheat,
  Pill,
  Syringe,
  Stethoscope,
  Wrench,
  Lightbulb,
  Users,
  Hammer,
  HelpCircle,
  Banknote,
  Building,
  CircleDollarSign,
  X,
  CheckCircle2,
} from 'lucide-react-native';
import { colors } from '@/constants/colors';
import Input from '@/components/Input';
import Button from '@/components/Button';
import DatePickerInput from '@/components/DatePickerInput';
import GenericDropdown, { DropdownItem } from '@/components/GenericDropdown';
import { useTranslation } from '@/hooks/useTranslation';
import { useToast } from '@/contexts/ToastContext';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import FarmDropdown from '@/components/FarmDropdown';
import AnimalSearchDropdown from '@/components/AnimalSearchDropdown';
import { doc, getDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { useThemeColors } from '@/hooks/useThemeColors';
import ImageCaptureButtons from '@/components/ImageCaptureButtons';

export default function EditExpenseScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { user } = useAuthStore();
  const { expenses, updateExpense, isLoading } = useExpenseStore();
  const { t } = useTranslation();
  const { showToast } = useToast();
  const { playFeedback } = useAudioFeedback();
  
  // Form state
  const [amount, setAmount] = useState('');
  const [date, setDate] = useState(new Date());
  const [category, setCategory] = useState<ExpenseCategory | null>(null);
  const [description, setDescription] = useState('');
  const [farmId, setFarmId] = useState<string | null>(null);
  const [farmName, setFarmName] = useState<string | null>(null);
  const [animalId, setAnimalId] = useState<string | null>(null);
  const [animalName, setAnimalName] = useState<string | null>(null);
  const [receiptImage, setReceiptImage] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
  
  // Loading state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Use the theme hook for colors
  const themedColors = useThemeColors();

  // Define specific colors with fallbacks if not directly in themedColors
  const primaryLightColor = themedColors.primaryLight || (themedColors.isDarkMode ? '#A5B4FC' : '#E0E7FF');
  const errorColor = themedColors.error || (themedColors.isDarkMode ? '#F87171' : '#DC2626');
  const successColor = themedColors.success || (themedColors.isDarkMode ? '#34D399' : '#16A34A');


  // Category options
  const categoryOptions: DropdownItem[] = [
    { id: ExpenseCategory.ANIMAL_PURCHASE, label: t('expense.category.animalPurchase'), icon: <Cat size={20} color={themedColors.primary} /> },
    { id: ExpenseCategory.FEED, label: t('expense.category.feed'), icon: <Wheat size={20} color={themedColors.primary} /> },
    { id: ExpenseCategory.MEDICATION, label: t('expense.category.medication'), icon: <Pill size={20} color={themedColors.primary} /> },
    { id: ExpenseCategory.VACCINATION, label: t('expense.category.vaccination'), icon: <Syringe size={20} color={themedColors.primary} /> },
    { id: ExpenseCategory.VETERINARY, label: t('expense.category.veterinary'), icon: <Stethoscope size={20} color={themedColors.primary} /> },
    { id: ExpenseCategory.EQUIPMENT, label: t('expense.category.equipment'), icon: <Wrench size={20} color={themedColors.primary} /> },
    { id: ExpenseCategory.UTILITIES, label: t('expense.category.utilities'), icon: <Lightbulb size={20} color={themedColors.primary} /> },
    { id: ExpenseCategory.LABOR, label: t('expense.category.labor'), icon: <Users size={20} color={themedColors.primary} /> },
    { id: ExpenseCategory.MAINTENANCE, label: t('expense.category.maintenance'), icon: <Hammer size={20} color={themedColors.primary} /> },
    { id: ExpenseCategory.OTHER, label: t('expense.category.other'), icon: <HelpCircle size={20} color={themedColors.primary} /> },
  ];
  
  // Payment method options
  const paymentMethodOptions: DropdownItem[] = [
    { id: PaymentMethod.CASH, label: t('expense.paymentMethod.cash'), icon: <Banknote size={20} color={themedColors.primary} /> },
    { id: PaymentMethod.CARD, label: t('expense.paymentMethod.card'), icon: <CreditCard size={20} color={themedColors.primary} /> },
    { id: PaymentMethod.BANK_TRANSFER, label: t('expense.paymentMethod.bankTransfer'), icon: <Building size={20} color={themedColors.primary} /> },
    { id: PaymentMethod.OTHER, label: t('expense.paymentMethod.other'), icon: <CircleDollarSign size={20} color={themedColors.primary} /> },
  ];
  
  // Load expense data
  useEffect(() => {
    const fetchExpense = async () => {
      if (!id) return;
      
      // First check if the expense is in the store
      const storeExpense = expenses.find(e => e.id === id);
      if (storeExpense) {
        populateForm(storeExpense);
        setLoading(false);
        return;
      }
      
      // If not in store, fetch from Firestore
      try {
        const expenseDoc = await getDoc(doc(firestore, 'expenses', id));
        if (expenseDoc.exists()) {
          const expenseData = { id: expenseDoc.id, ...expenseDoc.data() } as Expense;
          populateForm(expenseData);
        } else {
          setError(t('expense.notFound'));
        }
      } catch (err) {
        console.error('Error fetching expense:', err);
        setError(t('expense.fetchError'));
      } finally {
        setLoading(false);
      }
    };
    
    fetchExpense();
  }, [id, expenses]);
  
  // Populate form with expense data
  const populateForm = (expense: Expense) => {
    setAmount(expense.amount.toString());
    setDate(new Date(expense.date));
    setCategory(expense.category);
    setDescription(expense.description || '');
    setFarmId(expense.farmId);
    setFarmName(expense.farmName || null);
    setAnimalId(expense.animalId || null);
    setAnimalName(expense.animalName || null);
    setReceiptImage(expense.receiptImage || null);
    setPaymentMethod(expense.paymentMethod);
  };
  
  // Handle farm selection
  const handleFarmSelect = (id: string, name: string) => {
    setFarmId(id);
    setFarmName(name);
    // Reset animal selection when farm changes
    if (id !== farmId) {
      setAnimalId(null);
      setAnimalName(null);
    }
  };
  
  // Handle animal selection
  const handleAnimalSelect = (id: string, name: string) => {
    setAnimalId(id);
    setAnimalName(name);
  };
  
  // Handle taking a photo of receipt
  const handleTakePhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setReceiptImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert(t('common.error'), t('expense.photoError'));
    }
  };
  
  // Handle picking an image from gallery
  const handlePickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setReceiptImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t('common.error'), t('expense.imagePickError'));
    }
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = t('expense.validation.amount');
    }
    
    if (!category) {
      newErrors.category = t('expense.validation.category');
    }
    
    if (!farmId) {
      newErrors.farmId = t('expense.validation.farm');
    }
    
    if (!paymentMethod) {
      newErrors.paymentMethod = t('expense.validation.paymentMethod');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      playFeedback('error');
      return;
    }
    
    if (!user) {
      Alert.alert(t('common.error'), t('common.notLoggedIn'));
      return;
    }
    
    try {
      const expenseData = {
        amount: parseFloat(amount),
        date: date.getTime(),
        category: category!,
        description,
        farmId: farmId!,
        farmName: farmName || undefined,
        animalId: animalId || undefined,
        animalName: animalName || undefined,
        receiptImage: receiptImage || undefined,
        paymentMethod: paymentMethod!,
        updatedBy: user.id,
      };
      
      await updateExpense(farmId!, id, expenseData);
      
      playFeedback('success');
      showToast({
        type: 'success',
        title: t('expense.updateSuccess'),
        message: t('expense.updateSuccessDetail'),
      });
      
      router.back();
    } catch (error) {
      playFeedback('error');
      Alert.alert(t('common.error'), t('expense.updateError'));
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.loadingContainer, { backgroundColor: themedColors.background }]}>
        <ActivityIndicator size="large" color={themedColors.primary} />
      </SafeAreaView>
    );
  }
  
  if (error) {
    return (
      <SafeAreaView style={[styles.errorContainer, { backgroundColor: themedColors.background }]}>
        <Text style={[styles.errorText, { color: errorColor }]}>
          {error}
        </Text>
        <Button
          title={t('common.goBack')}
          onPress={() => router.back()}
        />
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themedColors.background }]}>
      <Stack.Screen
        options={{
          title: t('expenses.editExpense'),
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color={themedColors.text} />
            </TouchableOpacity>
          ),
        }}
      />
      
      <ScrollView style={{ flex: 1, padding: 16 }}>
        {/* Receipt Image - Moved to top */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: themedColors.text }]}>{t('expenses.receipt')} ({t('common.optional')})</Text>
          
          {receiptImage ? (
            <View style={styles.imageUploadCard}>
              <View style={styles.imageContainer}>
                <Image
                  source={{ uri: receiptImage }}
                  style={styles.image}
                  resizeMode="cover"
                />
                {/* <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => setReceiptImage(null)}
                >
                  <X size={20} color="white" />
                </TouchableOpacity> */}
                
                <View style={styles.successIndicator}>
                  <CheckCircle2 size={20} color={successColor} />
                  <Text style={styles.successText}>{t('expenses.receiptAdded')}</Text>
                </View>
              </View>
            </View>
          ) : (
            <View style={styles.imageUploadCard}>
              <View style={[styles.imagePlaceholder, { backgroundColor: themedColors.background, borderColor: themedColors.border }]}>
                <Camera size={40} color={colors.textSecondary} />
                <Text style={styles.imagePlaceholderText}>{t('expenses.addReceipt')}*</Text>
              </View>
              
              <ImageCaptureButtons
                onTakePhoto={handleTakePhoto}
                onChooseFromLibrary={handlePickImage}
                takePhotoText={t('expenses.takePhoto')}
                choosePhotoText={t('expenses.chooseFromGallery')}
              />
            </View>
          )}
        </View>

        {/* Amount */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: themedColors.text }]}>{t('expenses.amount')} *</Text>
          <Input
            value={amount}
            onChangeText={setAmount}
            placeholder="0.00"
            keyboardType="numeric"
            leftIcon={<DollarSign size={20} color={themedColors.text} />}
            error={errors.amount}
          />
        </View>
        
        {/* Date */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: themedColors.text }]}>{t('expenses.date')}</Text>
          <DatePickerInput
            value={date}
            onChange={setDate}
            icon={<Calendar size={20} color={themedColors.text} />}
          />
        </View>
        
        {/* Category */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: themedColors.text }]}>{t('expenses.category.label')} *</Text>
          <GenericDropdown
            items={categoryOptions}
            placeholder={t('expenses.category.select')}
            value={category || ''}
            onSelect={(value) => setCategory(value as ExpenseCategory)}
            error={errors.category}
            isDarkMode={themedColors.isDarkMode}
          />
        </View>
        
        {/* Farm */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: themedColors.text }]}>{t('farms.selectFarm')} *</Text>
          <FarmDropdown
            onFarmSelect={handleFarmSelect}
            selectedFarmId={farmId || undefined}
            error={errors.farmId}
          />
        </View>
        
        {/* Animal (optional) */}
        {farmId && (
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: themedColors.text }]}>{t('animals.selectAnimal')} ({t('common.optional')})</Text>
            <AnimalSearchDropdown
              farmId={farmId}
              value={animalId || ''}
              onSelect={(id) => setAnimalId(id)}
              onAnimalSelect={handleAnimalSelect}
              placeholder={t('animals.selectAnimal')}
            />
          </View>
        )}
        
        {/* Payment Method */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: themedColors.text }]}>{t('expenses.paymentMethod.label')} *</Text>
          <GenericDropdown
            items={paymentMethodOptions}
            placeholder={t('expenses.paymentMethod.select')}
            value={paymentMethod || ''}
            onSelect={(value) => setPaymentMethod(value as PaymentMethod)}
            error={errors.paymentMethod}
            isDarkMode={themedColors.isDarkMode}
          />
        </View>
        
        {/* Description */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: themedColors.text }]}>{t('expenses.description')}</Text>
          <Input
            value={description}
            onChangeText={setDescription}
            placeholder={t('expenses.descriptionPlaceholder')}
            multiline
            numberOfLines={3}
            leftIcon={<FileText size={20} color={themedColors.text} />}
          />
        </View>
        
        {/* Submit Button */}
        <Button
          title={t('expenses.updateExpense')}
          onPress={handleSubmit}
          disabled={isLoading}
          isLoading={isLoading}
          leftIcon={<Save size={20} color="white" />}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor will be set dynamically
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    // backgroundColor will be set dynamically
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    // color will be set dynamically in JSX
    marginBottom: 8,
  },
  imageUploadCard: {
    // backgroundColor will be set dynamically by Input component or here if needed
    borderRadius: 12,
    padding: 10,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
    borderWidth: 1,
    // borderColor will be set dynamically
  },
  image: {
    width: '100%',
    height: '100%',
  },
  removeImageButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    padding: 8,
  },
  successIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingVertical: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  successText: {
    marginLeft: 6,
    color: colors.success,
    // color will be set dynamically
    fontSize: 16,
  },
  imagePlaceholder: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    backgroundColor: colors.background,
    // backgroundColor will be set dynamically
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    // borderColor will be set dynamically
    borderStyle: 'dashed',
  },
  imagePlaceholderText: {
    marginTop: 8,
    color: colors.textSecondary,
    fontSize: 14,
  },

  errorText: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    // color will be set dynamically
  },
});
