import { HealthCheck } from '@/types/animal';

export const healthChecks: HealthCheck[] = [
  {
    id: 'hc1',
    animalId: '1',
    date: Date.now() - 1000 * 60 * 60 * 24 * 2, // 2 days ago
    temperature: 38.5,
    appetite: 'normal',
    hydration: 'normal',
    breathingRate: 24,
    gait: 'normal',
    fecalCondition: 'normal',
    coatAndSkin: 'normal',
    eyesAndNose: 'normal',
    earHealth: 'normal',
    weight: 550,
    notes: 'Regular health check, all parameters normal',
    abnormalities: false,
    createdBy: 'user1',
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 2,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 2,
  },
  {
    id: 'hc2',
    animalId: '2',
    date: Date.now() - 1000 * 60 * 60 * 24 * 5, // 5 days ago
    temperature: 39.2,
    appetite: 'reduced',
    hydration: 'normal',
    breathingRate: 28,
    gait: 'normal',
    fecalCondition: 'diarrhea',
    coatAndSkin: 'normal',
    eyesAndNose: 'discharge',
    earHealth: 'normal',
    weight: 34.5,
    notes: 'Slight fever and reduced appetite. Monitor closely.',
    abnormalities: true,
    createdBy: 'user1',
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 5,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 5,
  },
  {
    id: 'hc3',
    animalId: '3',
    date: Date.now() - 1000 * 60 * 60 * 24 * 10, // 10 days ago
    temperature: 39.0,
    appetite: 'normal',
    hydration: 'normal',
    breathingRate: 22,
    gait: 'limping',
    fecalCondition: 'normal',
    coatAndSkin: 'normal',
    eyesAndNose: 'normal',
    earHealth: 'normal',
    weight: 45,
    notes: 'Slight limping on front right leg. No visible injury.',
    abnormalities: true,
    createdBy: 'user1',
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 10,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 10,
  },
  {
    id: 'hc4',
    animalId: '4',
    date: Date.now() - 1000 * 60 * 60 * 24 * 15, // 15 days ago
    temperature: 41.1,
    appetite: 'none',
    hydration: 'dehydrated',
    breathingRate: 32,
    gait: 'stiff',
    fecalCondition: 'normal',
    coatAndSkin: 'normal',
    eyesAndNose: 'normal',
    earHealth: 'normal',
    weight: 1.8,
    notes: 'High fever and not eating. Administered antibiotics.',
    abnormalities: true,
    createdBy: 'healthworker1',
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 15,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 15,
  },
  {
    id: 'hc5',
    animalId: '1',
    date: Date.now() - 1000 * 60 * 60 * 24 * 20, // 20 days ago
    temperature: 38.6,
    appetite: 'normal',
    hydration: 'normal',
    breathingRate: 25,
    gait: 'normal',
    fecalCondition: 'normal',
    coatAndSkin: 'normal',
    eyesAndNose: 'normal',
    earHealth: 'normal',
    weight: 548,
    notes: 'Regular health check, all parameters normal',
    abnormalities: false,
    createdBy: 'user1',
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 20,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 20,
  },
];