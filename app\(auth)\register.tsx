import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ImageBackground,
  Image,
  TextInput,
} from 'react-native';
import { useRouter, Redirect } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import Button from '@/components/Button';
import { useAuthStore } from '@/store/auth-store';
import { Mail, Lock, User, ArrowLeft, AlertCircle, CheckCircle } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemeColors } from '@/hooks/useThemeColors';

export default function RegisterScreen() {
  const router = useRouter();
  const themedColors = useThemeColors();
  const { register, loginWithGoogle, error, isLoading, isAuthenticated, user } = useAuthStore();

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [validationErrors, setValidationErrors] = useState<{
    name?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
  }>({});
  const [registrationSuccess, setRegistrationSuccess] = useState(false);

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Redirect href="/(tabs)" />;
  }

  // Redirect to login after successful registration
  useEffect(() => {
    if (registrationSuccess) {
      const timer = setTimeout(() => {
        router.replace('/(auth)/login');
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [registrationSuccess]);

  const validateForm = () => {
    const errors: {
      name?: string;
      email?: string;
      password?: string;
      confirmPassword?: string;
    } = {};

    if (!name) {
      errors.name = 'Name is required';
    }

    if (!email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'Email is invalid';
    }

    if (!password) {
      errors.password = 'Password is required';
    } else if (password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleRegister = async () => {
    if (validateForm()) {
      try {
        console.log('Registering user with:', { name, email });

        // Register the user
        await register(email, password, name);

        // Show success message
        setRegistrationSuccess(true);
      } catch (err) {
        console.error('Registration error:', err);
        // Error is already handled in the store
      }
    }
  };

  const handleGoogleSignUp = async () => {
    try {
      console.log('Signing up with Google');
      await loginWithGoogle();
    } catch (err) {
      console.error('Google sign-up error:', err);
      // Error is already handled in the store
    }
  };

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1516467508483-a7212febe31a?q=80&w=2073&auto=format&fit=crop' }}
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      <LinearGradient
        colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0.85)']}
        style={styles.gradient}
      >
        <SafeAreaView style={styles.container}>
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
          >
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              keyboardShouldPersistTaps="handled"
            >
              <TouchableOpacity style={styles.backButton} onPress={handleBack}>
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>

              <View style={styles.logoContainer}>
                <Image
                  source={require('../../assets/images/ll.png')}
                  style={styles.logoImage}
                />
              </View>

              <View style={styles.header}>
                <Text style={styles.title}>Create Account</Text>
                <Text style={styles.subtitle}>Join the livestock management community</Text>
              </View>

              {registrationSuccess ? (
                <View style={styles.successContainer}>
                  <CheckCircle size={20} color={colors.success} />
                  <Text style={styles.successText}>
                    Registration successful! Please check your email to verify your account.
                    Redirecting to login...
                  </Text>
                </View>
              ) : error ? (
                <View style={styles.errorContainer}>
                  <AlertCircle size={20} color={colors.error} />
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              ) : null}

              <View style={styles.form}>
                <CustomInput
                  label="Full Name"
                  placeholder="Enter your full name"
                  value={name}
                  onChangeText={setName}
                  error={validationErrors.name}
                  leftIcon={<User size={20} color="#A78B71" />}
                  darkMode
                />

                <CustomInput
                  label="Email"
                  placeholder="Enter your email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  error={validationErrors.email}
                  leftIcon={<Mail size={20} color="#A78B71" />}
                  darkMode
                />

                <CustomInput
                  label="Password"
                  placeholder="Create a password"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                  isPassword
                  error={validationErrors.password}
                  leftIcon={<Lock size={20} color="#A78B71" />}
                  darkMode
                />

                <CustomInput
                  label="Confirm Password"
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry
                  isPassword
                  error={validationErrors.confirmPassword}
                  leftIcon={<Lock size={20} color="#A78B71" />}
                  darkMode
                />

                <Button
                  title="Sign Up"
                  onPress={handleRegister}
                  variant="primary"
                  size="large"
                  isLoading={isLoading}
                  style={styles.button}
                />

                <View style={styles.orContainer}>
                  <View style={styles.orLine} />
                  <Text style={styles.orText}>Or continue with</Text>
                  <View style={styles.orLine} />
                </View>

                <TouchableOpacity 
                  style={styles.googleButton}
                  onPress={handleGoogleSignUp}
                  disabled={isLoading}
                >
                  <Image
                    source={require('../../assets/images/google-icon.png')}
                    style={styles.googleIcon}
                  />
                  <Text style={styles.googleButtonText}>Sign up with Google</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.footer}>
                <Text style={styles.footerText}>Already have an account?</Text>
                <TouchableOpacity onPress={handleLogin}>
                  <Text style={styles.footerLink}>Sign In</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </LinearGradient>
    </ImageBackground>
  );
}

// Input component for this screen only
const CustomInput = ({ label, leftIcon, darkMode = false, helperText, error, ...props }: any) => {
  return (
    <View style={inputStyles.container}>
      {label && <Text style={[inputStyles.label, darkMode && inputStyles.labelDark]}>{label}</Text>}
      <View style={[inputStyles.inputContainer, darkMode && inputStyles.inputContainerDark]}>
        {leftIcon && <View style={inputStyles.iconContainer}>{leftIcon}</View>}
        <TextInput
          style={[
            inputStyles.input,
            leftIcon && inputStyles.inputWithIcon,
            darkMode && inputStyles.inputDark
          ]}
          placeholderTextColor={darkMode ? "#A78B71" : colors.textSecondary}
          {...props}
        />
      </View>
      {helperText && !error && (
        <Text style={[inputStyles.helperText, darkMode && inputStyles.helperTextDark]}>
          {helperText}
        </Text>
      )}
      {error && <Text style={[inputStyles.errorText, darkMode && inputStyles.errorTextDark]}>{error}</Text>}
    </View>
  );
};

const inputStyles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  labelDark: {
    color: '#fff',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  inputContainerDark: {
    borderColor: 'rgba(167, 139, 113, 0.5)',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  iconContainer: {
    paddingLeft: 16,
  },
  input: {
    flex: 1,
    height: 50,
    paddingHorizontal: 16,
    fontSize: 16,
    color: colors.text,
  },
  inputDark: {
    color: '#fff',
  },
  inputWithIcon: {
    paddingLeft: 8,
  },
  errorText: {
    color: colors.error,
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  errorTextDark: {
    color: '#FF6B6B',
  },
  helperText: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 4,
    marginLeft: 4,
  },
  helperTextDark: {
    color: '#A78B71',
  },
});

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  gradient: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  backButton: {
    marginBottom: 16,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#A78B71',
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#E0E0E0',
    textAlign: 'center',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(254, 202, 202, 0.8)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    gap: 8,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    flex: 1,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(209, 250, 229, 0.8)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    gap: 8,
  },
  successText: {
    color: colors.success,
    fontSize: 14,
    flex: 1,
  },
  form: {
    marginBottom: 24,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(167, 139, 113, 0.3)',
  },
  button: {
    marginTop: 16,
    backgroundColor: '#A78B71',
  },
  orContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  orLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(167, 139, 113, 0.5)',
  },
  orText: {
    color: '#A78B71',
    marginHorizontal: 16,
    fontSize: 14,
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 4,
    padding: 12,
    marginBottom: 24,
    elevation: 1,
  },
  googleIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  googleButtonText: {
    color: '#757575',
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
    gap: 4,
  },
  footerText: {
    color: '#E0E0E0',
    fontSize: 14,
  },
  footerLink: {
    color: '#A78B71',
    fontSize: 14,
    fontWeight: '600',
  },
});

