import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image,
  ImageBackground,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import Button from '@/components/Button';
import { useAuthStore } from '@/store/auth-store';
import { ArrowLeft, CheckCircle, AlertCircle, Mail } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function VerifyEmailScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { verifyEmail, error, isLoading } = useAuthStore();
  
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error'>('pending');
  const [statusMessage, setStatusMessage] = useState('Verifying your email...');
  
  // Get oobCode from URL params
  const oobCode = params.oobCode as string;
  
  useEffect(() => {
    if (oobCode) {
      verifyEmailWithCode(oobCode);
    } else {
      setVerificationStatus('error');
      setStatusMessage('Invalid verification link. Please request a new one.');
    }
  }, [oobCode]);
  
  const verifyEmailWithCode = async (code: string) => {
    try {
      const success = await verifyEmail(code);
      if (success) {
        setVerificationStatus('success');
        setStatusMessage('Your email has been verified successfully! You can now set up your password.');

        // Don't auto-redirect, let user choose to set up password
      } else {
        setVerificationStatus('error');
        setStatusMessage('Failed to verify your email. Please try again.');
      }
    } catch (err) {
      setVerificationStatus('error');
      setStatusMessage(error || 'Failed to verify your email. The link may have expired.');
    }
  };
  
  const handleBack = () => {
    router.replace('/(auth)/login');
  };

  const handleSetupPassword = () => {
    // Extract email from params if available, or navigate to setup without email
    const email = params.email as string;
    if (email) {
      router.replace({
        pathname: '/(auth)/setup-password',
        params: { email }
      });
    } else {
      router.replace('/(auth)/setup-password');
    }
  };
  
  return (
    <ImageBackground 
      source={{ uri: 'https://images.unsplash.com/photo-1500595046743-cd271d694d30?q=80&w=2070&auto=format&fit=crop' }} 
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      <LinearGradient
        colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0.85)']}
        style={styles.gradient}
      >
        <SafeAreaView style={styles.container}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="#fff" />
          </TouchableOpacity>
          
          <View style={styles.content}>
            <View style={styles.logoContainer}>
              <Image 
                source={{ uri: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1170&auto=format&fit=crop' }} 
                style={styles.logoImage}
              />
            </View>
            
            <Text style={styles.title}>Email Verification</Text>
            
            <View style={styles.statusContainer}>
              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <Mail size={60} color="#A78B71" />
                  <Text style={styles.statusMessage}>Verifying your email...</Text>
                </View>
              ) : verificationStatus === 'success' ? (
                <View style={styles.successContainer}>
                  <CheckCircle size={60} color={colors.success} />
                  <Text style={styles.successMessage}>{statusMessage}</Text>
                  <Button
                    title="Set Up Password"
                    onPress={handleSetupPassword}
                    variant="primary"
                    size="medium"
                    style={{ marginTop: 16, backgroundColor: colors.primary, width: '100%' }}
                  />
                </View>
              ) : (
                <View style={styles.errorContainer}>
                  <AlertCircle size={60} color={colors.error} />
                  <Text style={styles.errorMessage}>{statusMessage}</Text>
                </View>
              )}
            </View>
            
            <Button
              title="Back to Login"
              onPress={handleBack}
              variant="primary"
              size="large"
              style={styles.button}
            />
          </View>
        </SafeAreaView>
      </LinearGradient>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  gradient: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 24,
  },
  backButton: {
    marginBottom: 16,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#A78B71',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 32,
    textAlign: 'center',
  },
  statusContainer: {
    width: '100%',
    padding: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderWidth: 1,
    borderColor: 'rgba(167, 139, 113, 0.3)',
    alignItems: 'center',
    marginBottom: 32,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  successContainer: {
    alignItems: 'center',
  },
  errorContainer: {
    alignItems: 'center',
  },
  statusMessage: {
    color: '#A78B71',
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
    textAlign: 'center',
  },
  successMessage: {
    color: colors.success,
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
    textAlign: 'center',
  },
  errorMessage: {
    color: colors.error,
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
    textAlign: 'center',
  },
  redirectMessage: {
    color: '#E0E0E0',
    fontSize: 14,
    marginTop: 8,
    fontStyle: 'italic',
  },
  button: {
    backgroundColor: '#A78B71',
    width: '100%',
  },
});