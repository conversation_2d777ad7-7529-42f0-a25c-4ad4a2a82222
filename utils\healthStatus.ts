import { Animal } from '@/types/animal';
import { HealthCheck } from '@/types/healthCheck';
import { AnimalRecord, RecordType } from '@/types/record';

// Constants for health status rules
export const HEALTH_CHECK_INTERVAL = 7; // Default: 7 days
export const VACCINATION_INTERVAL = 30; // 1 month in days
export const HEALTH_CHECK_OVERDUE_THRESHOLD = 14; // 14 days

/**
 * Determines if a health check is due for an animal
 */
export function isHealthCheckDue(animal: Animal): boolean {
  const now = Date.now();
  
  // If no next health check date is set, it's due
  if (!animal.nextHealthCheck) return true;
  
  // Check if current date is past or equal to the next health check date
  return now >= animal.nextHealthCheck;
}

/**
 * Determines if a health check is overdue (more than threshold days)
 */
export function isHealthCheckOverdue(animal: Animal): boolean {
  const now = Date.now();
  
  // If no next health check date is set, consider it overdue
  if (!animal.nextHealthCheck) return true;
  
  // Check if current date is more than threshold days past the next health check date
  const overdueThreshold = animal.nextHealthCheck + (HEALTH_CHECK_OVERDUE_THRESHOLD * 24 * 60 * 60 * 1000);
  return now >= overdueThreshold;
}

/**
 * Determines if a vaccination is due for an animal
 */
export function isVaccinationDue(animal: Animal): boolean {
  const now = Date.now();
  
  // If no next vaccination date is set, it's due
  if (!animal.nextVaccination) return true;
  
  // Check if current date is past or equal to the next vaccination date
  return now >= animal.nextVaccination;
}

/**
 * Calculates the next health check date based on the last check and interval
 */
export function calculateNextHealthCheckDate(lastCheckDate: number, interval: number = HEALTH_CHECK_INTERVAL): number {
  // Default interval is 7 days if not specified
  const intervalMs = interval * 24 * 60 * 60 * 1000;
  return lastCheckDate + intervalMs;
}

/**
 * Calculates the next vaccination date based on the last vaccination
 */
export function calculateNextVaccinationDate(lastVaccinationDate: number): number {
  // Vaccination interval is 30 days (1 month)
  const intervalMs = VACCINATION_INTERVAL * 24 * 60 * 60 * 1000;
  return lastVaccinationDate + intervalMs;
}

/**
 * Checks if an animal has any health issues based on the latest health check
 */
export function hasHealthIssues(healthCheck: HealthCheck | null): boolean {
  if (!healthCheck) return false;
  
  return healthCheck.abnormalities;
}

/**
 * Determines if an animal is healthy based on all health rules
 */
export function isAnimalHealthy(
  animal: Animal, 
  latestHealthCheck: HealthCheck | null,
  vaccinationRecords: AnimalRecord[]
): boolean {
  // Check if health check is overdue
  if (isHealthCheckOverdue(animal)) return false;
  
  // Check if the animal has health issues from the latest check
  if (latestHealthCheck && latestHealthCheck.abnormalities) return false;
  
  // Check if vaccination is due
  if (isVaccinationDue(animal)) return false;
  
  // If all checks pass, the animal is healthy
  return true;
}

/**
 * Gets the reason why an animal is not healthy
 */
export function getUnhealthyReason(
  animal: Animal, 
  latestHealthCheck: HealthCheck | null,
  vaccinationRecords: AnimalRecord[]
): string {
  if (isHealthCheckOverdue(animal)) {
    return "Health check overdue";
  }
  
  if (latestHealthCheck && latestHealthCheck.abnormalities) {
    return "Health issues reported";
  }
  
  if (isVaccinationDue(animal)) {
    return "Vaccination due";
  }
  
  return "Unknown issue";
}

/**
 * Updates an animal's health status based on a new health check
 */
export function updateAnimalHealthStatus(
  animal: Animal, 
  healthCheck: HealthCheck
): Partial<Animal> {
  const interval = animal.healthCheckInterval || HEALTH_CHECK_INTERVAL;
  const nextCheckDate = calculateNextHealthCheckDate(healthCheck.date, interval);
  
  return {
    lastHealthCheck: healthCheck.date,
    nextHealthCheck: nextCheckDate,
    hasHealthIssues: healthCheck.abnormalities,
  };
}

/**
 * Updates an animal's vaccination status
 */
export function updateAnimalVaccinationStatus(
  animal: Animal, 
  vaccinationDate: number
): Partial<Animal> {
  const nextVaccinationDate = calculateNextVaccinationDate(vaccinationDate);
  
  return {
    lastVaccination: vaccinationDate,
    nextVaccination: nextVaccinationDate,
  };
}
