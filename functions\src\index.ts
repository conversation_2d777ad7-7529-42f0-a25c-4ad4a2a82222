import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as nodemailer from 'nodemailer';

// Initialize Firebase Admin
admin.initializeApp();

// Configure email transporter (you'll need to set up your email service)
const transporter = nodemailer.createTransporter({
  service: 'gmail', // or your preferred email service
  auth: {
    user: functions.config().email.user, // Set via: firebase functions:config:set email.user="<EMAIL>"
    pass: functions.config().email.pass  // Set via: firebase functions:config:set email.pass="your-app-password"
  }
});

// Cloud Function to send user registration email
export const sendUserRegistrationEmail = functions.https.onCall(async (data, context) => {
  // Verify the request is from an authenticated user
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { email, name, role, farmNames, tempPassword, verificationLink } = data;

  // Validate required fields
  if (!email || !name || !role || !verificationLink) {
    throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
  }

  try {
    // Create email content based on role
    const emailSubject = `Welcome to Animal Health Tracker - ${role.charAt(0).toUpperCase() + role.slice(1)} Access`;
    
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Welcome to Animal Health Tracker</h2>
        
        <p>Hello ${name},</p>
        
        <p>You have been invited to join Animal Health Tracker as a <strong>${role}</strong>.</p>
        
        ${farmNames && farmNames.length > 0 ? `
          <p><strong>Assigned Farms:</strong></p>
          <ul>
            ${farmNames.map((farm: string) => `<li>${farm}</li>`).join('')}
          </ul>
        ` : ''}
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #374151; margin-top: 0;">Your Account Details:</h3>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Temporary Password:</strong> <code style="background-color: #e5e7eb; padding: 2px 4px; border-radius: 4px;">${tempPassword}</code></p>
          <p><strong>Role:</strong> ${role.charAt(0).toUpperCase() + role.slice(1)}</p>
        </div>
        
        <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b; margin: 20px 0;">
          <p style="margin: 0;"><strong>Important:</strong> Please verify your email and set up your own password within 24 hours. This link will expire after 1 day.</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationLink}" 
             style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Verify Email & Set Password
          </a>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <h4>Next Steps:</h4>
          <ol>
            <li>Click the "Verify Email & Set Password" button above</li>
            <li>Verify your email address</li>
            <li>Set your own secure password</li>
            <li>Download the Animal Health Tracker app</li>
            <li>Log in with your email and new password</li>
          </ol>
        </div>
        
        <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
          If you have any questions, please contact your farm administrator.
        </p>
        
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          Animal Health Tracker Team
        </p>
      </div>
    `;

    // Send email
    await transporter.sendMail({
      from: functions.config().email.user,
      to: email,
      subject: emailSubject,
      html: emailHtml
    });

    return { success: true, message: 'Registration email sent successfully' };
  } catch (error) {
    console.error('Error sending registration email:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send registration email');
  }
});

// Cloud Function to generate custom email verification link with 1-day expiry
export const generateVerificationLink = functions.https.onCall(async (data, context) => {
  // Verify the request is from an authenticated user
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { email } = data;

  if (!email) {
    throw new functions.https.HttpsError('invalid-argument', 'Email is required');
  }

  try {
    // Generate email verification link with custom URL
    const link = await admin.auth().generateEmailVerificationLink(email, {
      url: 'https://kissandost-9570f.firebaseapp.com/verify-email.html',
      handleCodeInApp: true,
    });

    return { verificationLink: link };
  } catch (error) {
    console.error('Error generating verification link:', error);
    throw new functions.https.HttpsError('internal', 'Failed to generate verification link');
  }
});

// Cloud Function to create custom token for password setup
export const createPasswordSetupToken = functions.https.onCall(async (data, context) => {
  // Verify the request is from an authenticated user
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { uid, email } = data;

  if (!uid || !email) {
    throw new functions.https.HttpsError('invalid-argument', 'UID and email are required');
  }

  try {
    // Create custom token with 1-day expiry
    const customToken = await admin.auth().createCustomToken(uid, {
      email: email,
      purpose: 'password-setup',
      expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    });

    return { customToken };
  } catch (error) {
    console.error('Error creating custom token:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create custom token');
  }
});
