import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAnimalStore } from '@/store/animal-store';
import { useHealthCheckStore } from '@/store/health-check-store';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import Button from '@/components/Button';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import {
  Edit,
  Trash2,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Thermometer,
  Scale,
  Apple,
  Droplets,
  Wind,
  Footprints,
  CircleDot,
  Shirt,
  Eye,
  Ear,
  FileText,
  ArrowLeft
} from 'lucide-react-native';
import { HealthCheck } from '@/types/healthCheck';
import { Animal } from '@/types/animal';
import { LinearGradient } from 'expo-linear-gradient';

export default function HealthCheckDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getAnimal } = useAnimalStore();
  const { healthChecks, deleteHealthCheck, isLoading, getHealthCheck } = useHealthCheckStore();
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();

  const [healthCheck, setHealthCheck] = useState<HealthCheck | undefined>();
  const [animal, setAnimal] = useState<Animal | undefined>(undefined);

  useEffect(() => {
    // Find the health check in the store
    const check = getHealthCheck(id);
    setHealthCheck(check);

    if (check) {
      const animalData = getAnimal(check.animalId);
      setAnimal(animalData);
    }
  }, [id, healthChecks, getHealthCheck, getAnimal]);

  const handleEdit = () => {
    router.push(`/health-checks/${id}/edit`);
  };

  const handleDelete = () => {
    Alert.alert(
      t('healthChecks.title'),
      t('healthChecks.deleteConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          onPress: async () => {
            await deleteHealthCheck(id);
            router.back();
          },
          style: 'destructive',
        },
      ]
    );
  };

  const formatDate = (timestamp: number) => {
    try {
      const date = new Date(timestamp);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date detected:', timestamp);
        return 'Invalid date';
      }
      
      // Use the appropriate locale based on the selected language
      const locale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';

      return date.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Error formatting date:', error, timestamp);
      return 'Error';
    }
  };

  const formatValue = (value: string | number | undefined, unit?: string) => {
    if (value === undefined || value === null) return t('healthChecks.notRecorded');
    return unit ? `${value} ${unit}` : value;
  };

  const getStatusColor = (status: string) => {
    if (status === 'normal') return themedColors.success;
    if (status === 'increased' || status === 'decreased' || status === 'dehydrated' ||
        status === 'overhydrated' || status === 'limping' || status === 'stiff' ||
        status === 'dull' || status === 'discharge' || status === 'cloudy' ||
        status === 'red' || status === 'swollen') {
      return themedColors.warning;
    }
    return themedColors.error;
  };

  const getStatusIcon = (parameter: string, status: string) => {
    const iconSize = 20;
    const iconColor = getStatusColor(status);

    switch (parameter) {
      case 'appetite':
        return <Apple size={iconSize} color={iconColor} />;
      case 'hydration':
        return <Droplets size={iconSize} color={iconColor} />;
      case 'respiration':
        return <Wind size={iconSize} color={iconColor} />;
      case 'gait':
        return <Footprints size={iconSize} color={iconColor} />;
      case 'fecal':
        return <CircleDot size={iconSize} color={iconColor} />;
      case 'coat':
        return <Shirt size={iconSize} color={iconColor} />;
      case 'eyes':
        return <Eye size={iconSize} color={iconColor} />;
      case 'ears':
        return <Ear size={iconSize} color={iconColor} />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return <LoadingIndicator fullScreen message={t('healthChecks.loadingDetails')} />;
  }

  if (!healthCheck || !animal) {
    return (
      <EmptyState
        title={t('healthChecks.notFound')}
        message={t('healthChecks.notFoundMessage')}
        actionLabel={t('common.goBack')}
        onAction={() => router.back()}
      />
    );
  }

  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('healthChecks.checkDetails'),
        headerTitleAlign: language === 'ur' ? 'center' : 'left',
        headerTitleStyle: { fontWeight: 'bold' },
        headerStyle: {
          backgroundColor: themedColors.background,
        },
        headerTintColor: themedColors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={themedColors.text} />
          </TouchableOpacity>
        )
      }} />

      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <View style={styles.dateSection}>
            <Calendar size={24} color={themedColors.primary} />
            <Text style={[styles.dateText, language === 'ur' ? styles.urduText : null]}>{formatDate(healthCheck.date)}</Text>
          </View>

          <View style={styles.statusBadge}>
            {healthCheck.abnormalities ? (
              <>
                <AlertTriangle size={16} color="white" />
                <Text style={[styles.statusText, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.abnormal')}</Text>
              </>
            ) : (
              <>
                <CheckCircle size={16} color="white" />
                <Text style={[styles.statusText, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.normal')}</Text>
              </>
            )}
          </View>
        </View>

        <View style={styles.animalSection}>
          <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('animals.title')}</Text>
          <TouchableOpacity
            style={styles.animalCard}
            onPress={() => router.push(`/animals/${animal.id}`)}
          >
            <View style={styles.animalInfo}>
              <Text style={[styles.animalName, language === 'ur' ? styles.urduText : null]}>{animal.name}</Text>
              <Text style={[styles.animalSpecies, language === 'ur' ? styles.urduText : null]}>{animal.species} {animal.breed ? `• ${animal.breed}` : ''}</Text>
            </View>
            <View style={[
              styles.statusIndicator,
              healthCheck.abnormalities ? styles.abnormalStatus : styles.normalStatus
            ]}>
              {healthCheck.abnormalities ? (
                <AlertTriangle size={16} color="white" />
              ) : (
                <CheckCircle size={16} color="white" />
              )}
            </View>
          </TouchableOpacity>
        </View>

        <View style={styles.vitalSection}>
          <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.vitalSigns')}</Text>
          <View style={styles.vitalGrid}>
            <View style={styles.vitalItem}>
              <Thermometer size={24} color={themedColors.primary} />
              <View style={styles.vitalContent}>
                <Text style={[styles.vitalLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.temperature')}</Text>
                <Text style={[styles.vitalValue, language === 'ur' ? styles.urduText : null]}>{formatValue(healthCheck.temperature, '°C')}</Text>
              </View>
            </View>
            <View style={styles.vitalItem}>
              <Scale size={24} color={themedColors.primary} />
              <View style={styles.vitalContent}>
                <Text style={[styles.vitalLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.weight')}</Text>
                <Text style={[styles.vitalValue, language === 'ur' ? styles.urduText : null]}>{formatValue(healthCheck.weight, 'kg')}</Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.conditionSection}>
          <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.physicalCondition')}</Text>
          <View style={styles.conditionGrid}>
            <View style={styles.conditionItem}>
              {getStatusIcon('appetite', healthCheck.appetite)}
              <View style={styles.conditionContent}>
                <Text style={[styles.conditionLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.appetite')}</Text>
                <Text style={[
                  styles.conditionValue,
                  { color: getStatusColor(healthCheck.appetite) },
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {healthCheck.appetite.charAt(0).toUpperCase() + healthCheck.appetite.slice(1)}
                </Text>
              </View>
            </View>
            <View style={styles.conditionItem}>
              {getStatusIcon('hydration', healthCheck.hydration)}
              <View style={styles.conditionContent}>
                <Text style={[styles.conditionLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.hydration')}</Text>
                <Text style={[
                  styles.conditionValue,
                  { color: getStatusColor(healthCheck.hydration) },
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {healthCheck.hydration.charAt(0).toUpperCase() +
                   healthCheck.hydration.slice(1)}
                </Text>
              </View>
            </View>
            <View style={styles.conditionItem}>
              {getStatusIcon('respiration', healthCheck.respiration)}
              <View style={styles.conditionContent}>
                <Text style={[styles.conditionLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.respiration')}</Text>
                <Text style={[
                  styles.conditionValue,
                  { color: getStatusColor(healthCheck.respiration) },
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {healthCheck.respiration.charAt(0).toUpperCase() +
                   healthCheck.respiration.slice(1)}
                </Text>
              </View>
            </View>
            <View style={styles.conditionItem}>
              {getStatusIcon('gait', healthCheck.gait)}
              <View style={styles.conditionContent}>
                <Text style={[styles.conditionLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.gait')}</Text>
                <Text style={[
                  styles.conditionValue,
                  { color: getStatusColor(healthCheck.gait) },
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {healthCheck.gait.charAt(0).toUpperCase() +
                   healthCheck.gait.slice(1)}
                </Text>
              </View>
            </View>
            <View style={styles.conditionItem}>
              {getStatusIcon('fecal', healthCheck.fecal)}
              <View style={styles.conditionContent}>
                <Text style={[styles.conditionLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.fecal')}</Text>
                <Text style={[
                  styles.conditionValue,
                  { color: getStatusColor(healthCheck.fecal) },
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {healthCheck.fecal.charAt(0).toUpperCase() +
                   healthCheck.fecal.slice(1)}
                </Text>
              </View>
            </View>
            <View style={styles.conditionItem}>
              {getStatusIcon('coat', healthCheck.coat)}
              <View style={styles.conditionContent}>
                <Text style={[styles.conditionLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.coat')}</Text>
                <Text style={[
                  styles.conditionValue,
                  { color: getStatusColor(healthCheck.coat) },
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {healthCheck.coat.charAt(0).toUpperCase() +
                   healthCheck.coat.slice(1)}
                </Text>
              </View>
            </View>
            <View style={styles.conditionItem}>
              {getStatusIcon('eyes', healthCheck.eyes)}
              <View style={styles.conditionContent}>
                <Text style={[styles.conditionLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.eyes')}</Text>
                <Text style={[
                  styles.conditionValue,
                  { color: getStatusColor(healthCheck.eyes) },
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {healthCheck.eyes.charAt(0).toUpperCase() +
                   healthCheck.eyes.slice(1)}
                </Text>
              </View>
            </View>
            <View style={styles.conditionItem}>
              {getStatusIcon('ears', healthCheck.ears)}
              <View style={styles.conditionContent}>
                <Text style={[styles.conditionLabel, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.ears')}</Text>
                <Text style={[
                  styles.conditionValue,
                  { color: getStatusColor(healthCheck.ears) },
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {healthCheck.ears.charAt(0).toUpperCase() +
                   healthCheck.ears.slice(1)}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {healthCheck.notes && (
          <View style={styles.notesSection}>
            <View style={styles.sectionTitleContainer}>
              <FileText size={20} color={themedColors.primary} />
              <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>{t('healthChecks.notes')}</Text>
            </View>
            <View style={styles.notesCard}>
              <Text style={[styles.notesText, language === 'ur' ? styles.urduText : null]}>{healthCheck.notes}</Text>
            </View>
          </View>
        )}

        <View style={styles.nextCheckSection}>
          <View style={styles.sectionTitleContainer}>
            <Calendar size={20} color={themedColors.primary} />
            <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null, {marginTop:10}]}>{t('healthChecks.nextHealthCheck')}</Text>
          </View>
          <View style={styles.nextCheckCard}>
            <Text style={[styles.nextCheckDate, language === 'ur' ? styles.urduText : null]}>{formatDate(healthCheck.nextCheckDate)}</Text>
          </View>
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, styles.editButton]}
            onPress={handleEdit}
          >
            <Edit size={20} color="white" />
            <Text style={[styles.actionButtonText, language === 'ur' ? styles.urduText : null]}>{t('common.edit')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={handleDelete}
          >
            <Trash2 size={20} color="white" />
            <Text style={[styles.actionButtonText, language === 'ur' ? styles.urduText : null]}>{t('common.delete')}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title={t('healthChecks.scheduleNextCheck')}
          onPress={() => router.push('/health-checks/add')}
          variant="primary"
          size="large"
          leftIcon={<Calendar size={20} color="white" />}
        />
      </View>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    flex: 1,
    // backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    backgroundColor: themedColors.card,
    borderBottomColor: themedColors.border,
  },
  dateSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dateText: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  statusText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  animalSection: {
    padding: 16,
    // backgroundColor: themedColors.card,
    marginTop: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 12,
  },
  animalCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 4,
  },
  animalSpecies: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  statusIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  normalStatus: {
    backgroundColor: themedColors.success,
  },
  abnormalStatus: {
    backgroundColor: themedColors.error,
  },
  vitalSection: {
    padding: 16,
    marginTop: 16,
  },
  vitalGrid: {
    gap: 16,
  },
  vitalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
    gap: 12,
  },
  vitalContent: {
    flex: 1,
  },
  vitalLabel: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 4,
  },
  vitalValue: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
  },
  conditionSection: {
    padding: 16,
    marginTop: 16,
  },
  conditionGrid: {
    gap: 12,
  },
  conditionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
    gap: 12,
  },
  conditionContent: {
    flex: 1,
  },
  conditionLabel: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 4,
  },
  conditionValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  notesSection: {
    padding: 16,
    backgroundColor: themedColors.card,
    marginTop: 16,
  },
  notesCard: {
    backgroundColor: themedColors.background,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  notesText: {
    fontSize: 16,
    color: themedColors.text,
    lineHeight: 24,
  },
  nextCheckSection: {
    padding: 16,
    marginTop: 16,

  },
  nextCheckCard: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
    alignItems: 'center',
  },
  nextCheckDate: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  editButton: {
    backgroundColor: themedColors.primary,
  },
  deleteButton: {
    backgroundColor: themedColors.error,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    padding: 16,
    backgroundColor: themedColors.card,
    borderTopWidth: 1,
    borderTopColor: themedColors.border,
  },
});
