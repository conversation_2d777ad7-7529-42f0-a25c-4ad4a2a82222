import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { Pregnancy } from '@/store/pregnancy-store';
import { gestationPeriods } from '@/constants/breeds';
import { Sparkles, ChevronDown } from 'lucide-react-native';
import GenericDropdown from '@/components/GenericDropdown';

interface DietTabProps {
  pregnancy: Pregnancy;
  colors: any;
}

interface DietRecommendation {
  week: number;
  stage: 'early' | 'mid' | 'late';
  title: string;
  description: string;
  nutrients: string[];
  foods: string[];
}

interface GroupedRecommendations {
  early?: DietRecommendation[];
  mid?: DietRecommendation[];
  late?: DietRecommendation[];
  [key: string]: DietRecommendation[] | undefined;
}

const DietTab = ({ pregnancy, colors }: DietTabProps) => {
  const { t, language } = useTranslation();

  // Use AI-generated diet plan if available
  const useAIGeneratedPlan = pregnancy.useAIPlans && pregnancy.aiGeneratedDietPlan;

  // State for week filter
  const [selectedWeek, setSelectedWeek] = useState<string>('all');
  
  const styles = StyleSheet.create({
    container: {
      padding: 16,
    },
    section: {
      marginBottom: 20,
      backgroundColor: colors.card,
      borderRadius: 8,
      padding: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 10,
      color: colors.text,
    },
    recommendationItem: {
      marginBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      paddingBottom: 16,
    },
    recommendationTitle: {
      fontSize: 16,
      fontWeight: '500',
      color: colors.text,
      marginBottom: 4,
    },
    recommendationDesc: {
      fontSize: 14,
      color: colors.textSecondary,
      lineHeight: 20,
    },
    weekTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
      marginBottom: 8,
      marginTop: 8,
    },
    stageHeader: {
      fontSize: 17,
      fontWeight: '700',
      color: colors.primary,
      marginBottom: 12,
      marginTop: 4,
      backgroundColor: colors.backgroundSecondary,
      padding: 8,
      borderRadius: 6,
    },
    urduText: {
      fontFamily: 'UrduFont',
    },
    currentWeekIndicator: {
      backgroundColor: colors.primary + '20', // Add transparency
      borderLeftWidth: 3,
      borderLeftColor: colors.primary,
      paddingLeft: 12,
    },
    bulletPoint: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 4,
    },
    bulletPointRTL: {
      flexDirection: 'row-reverse',
      alignItems: 'flex-start',
      marginBottom: 4,
    },
    bullet: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: colors.primary,
      marginTop: 6,
      marginRight: 8,
    },
    bulletRTL: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: colors.primary,
      marginTop: 6,
      marginLeft: 8,
      marginRight: 0,
    },
    bulletText: {
      flex: 1,
      fontSize: 14,
      color: colors.textSecondary,
    },
    aiGeneratedBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primaryLight || `${colors.primary}20`,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      alignSelf: 'flex-start',
      marginTop: 4,
      marginBottom: 8,
    },
    aiGeneratedText: {
      fontSize: 12,
      color: colors.primary,
      marginLeft: 4,
    },
    titleRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    weekDropdownContainer: {
      width: '100%',
      marginTop: 5,
    },

  });

  // Calculate current week of pregnancy
  const calculateCurrentWeek = () => {
    if (!pregnancy.conceptionDate) return 1;
    
    const conceptionDate = new Date(pregnancy.conceptionDate);
    const today = new Date();
    const diffTime = today.getTime() - conceptionDate.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const currentWeek = Math.floor(diffDays / 7) + 1;
    
    return currentWeek;
  };

  const currentWeek = calculateCurrentWeek();
  
  // Get gestation period based on species
  const getGestationPeriod = () => {
    const species = pregnancy.species?.toLowerCase() || 'default';
    return gestationPeriods[species] || gestationPeriods.default;
  };
  
  const gestationPeriod = getGestationPeriod();
  const totalWeeks = Math.ceil(gestationPeriod / 7);
  
  // Helper functions for species-specific content
  const getSpeciesSpecificTitle = (species: string, stage: string, week: number): string => {
    const stageTranslated = t(`pregnancy.stages.${stage}`, { defaultValue: stage.charAt(0).toUpperCase() + stage.slice(1) });

    switch (species) {
      case 'cow':
        if (stage === 'early') {
          return t('pregnancy.diet.cow.early.title', {
            defaultValue: `Week {{week}}: Foundational Nutrition`,
            week
          });
        } else if (stage === 'mid') {
          return t('pregnancy.diet.cow.mid.title', {
            defaultValue: `Week {{week}}: Growth Support`,
            week
          });
        } else {
          return t('pregnancy.diet.cow.late.title', {
            defaultValue: `Week {{week}}: Pre-Calving Preparation`,
            week
          });
        }
      case 'goat':
        if (stage === 'early') {
          return t('pregnancy.diet.goat.early.title', {
            defaultValue: `Week {{week}}: Early Kidding Nutrition`,
            week
          });
        } else if (stage === 'mid') {
          return t('pregnancy.diet.goat.mid.title', {
            defaultValue: `Week {{week}}: Mid-Gestation Support`,
            week
          });
        } else {
          return t('pregnancy.diet.goat.late.title', {
            defaultValue: `Week {{week}}: Late Gestation Preparation`,
            week
          });
        }
      case 'sheep':
        if (stage === 'early') {
          return t('pregnancy.diet.sheep.early.title', {
            defaultValue: `Week {{week}}: Early Lambing Nutrition`,
            week
          });
        } else if (stage === 'mid') {
          return t('pregnancy.diet.sheep.mid.title', {
            defaultValue: `Week {{week}}: Fetal Development Support`,
            week
          });
        } else {
          return t('pregnancy.diet.sheep.late.title', {
            defaultValue: `Week {{week}}: Pre-Lambing Preparation`,
            week
          });
        }
      case 'pig':
        if (stage === 'early') {
          return t('pregnancy.diet.pig.early.title', {
            defaultValue: `Week {{week}}: Early Farrowing Diet`,
            week
          });
        } else if (stage === 'mid') {
          return t('pregnancy.diet.pig.mid.title', {
            defaultValue: `Week {{week}}: Fetal Growth Support`,
            week
          });
        } else {
          return t('pregnancy.diet.pig.late.title', {
            defaultValue: `Week {{week}}: Pre-Farrowing Preparation`,
            week
          });
        }
      default:
        return t('pregnancy.diet.general.title', {
          defaultValue: `Week {{week}}: {{stage}} Stage Nutrition`,
          week,
          stage: stageTranslated
        });
    }
  };

  const getSpeciesSpecificDescription = (species: string, stage: string): string => {
    switch (species) {
      case 'cow':
        if (stage === 'early') {
          return t('pregnancy.diet.cow.early.description', {
            defaultValue: `Focus on quality forage and balanced minerals to support early fetal development.`
          });
        } else if (stage === 'mid') {
          return t('pregnancy.diet.cow.mid.description', {
            defaultValue: `Increase energy intake to support growing fetus and maintain body condition.`
          });
        } else {
          return t('pregnancy.diet.cow.late.description', {
            defaultValue: `Prepare for calving with higher energy diet and proper calcium balance to prevent milk fever.`
          });
        }
      case 'goat':
        if (stage === 'early') {
          return t('pregnancy.diet.goat.early.description', {
            defaultValue: `Provide quality forage and balanced minerals for early fetal development.`
          });
        } else if (stage === 'mid') {
          return t('pregnancy.diet.goat.mid.description', {
            defaultValue: `Gradually increase feed quality to support multiple fetuses common in goats.`
          });
        } else {
          return t('pregnancy.diet.goat.late.description', {
            defaultValue: `Focus on energy-dense nutrition to prevent pregnancy toxemia and support final fetal growth.`
          });
        }
      case 'sheep':
        if (stage === 'early') {
          return t('pregnancy.diet.sheep.early.description', {
            defaultValue: `Maintain moderate body condition with quality forage and mineral supplementation.`
          });
        } else if (stage === 'mid') {
          return t('pregnancy.diet.sheep.mid.description', {
            defaultValue: `Gradually increase nutrition to support wool growth and fetal development.`
          });
        } else {
          return t('pregnancy.diet.sheep.late.description', {
            defaultValue: `Increase energy intake to prevent pregnancy toxemia, especially for ewes carrying multiples.`
          });
        }
      case 'pig':
        if (stage === 'early') {
          return t('pregnancy.diet.pig.early.description', {
            defaultValue: `Maintain moderate feed intake with balanced protein to support implantation.`
          });
        } else if (stage === 'mid') {
          return t('pregnancy.diet.pig.mid.description', {
            defaultValue: `Gradually increase feed to support growing litter size.`
          });
        } else {
          return t('pregnancy.diet.pig.late.description', {
            defaultValue: `Increase feed quality while maintaining fiber to prevent constipation before farrowing.`
          });
        }
      default:
        return t('pregnancy.diet.general.description', {
          defaultValue: `Focus on balanced nutrition appropriate for this stage of pregnancy.`
        });
    }
  };

  const getSpeciesSpecificNutrients = (species: string, stage: string): string[] => {
    switch (species) {
      case 'cow':
        if (stage === 'early') {
          return [
            t('pregnancy.diet.cow.early.nutrient1', { defaultValue: 'Calcium and Phosphorus (1:1 ratio)' }),
            t('pregnancy.diet.cow.early.nutrient2', { defaultValue: 'Vitamin A and E' }),
            t('pregnancy.diet.cow.early.nutrient3', { defaultValue: 'Quality protein (12-14%)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.diet.cow.mid.nutrient1', { defaultValue: 'Increased energy (TDN 60-65%)' }),
            t('pregnancy.diet.cow.mid.nutrient2', { defaultValue: 'Protein (12-14%)' }),
            t('pregnancy.diet.cow.mid.nutrient3', { defaultValue: 'Trace minerals (Selenium, Copper)' })
          ];
        } else {
          return [
            t('pregnancy.diet.cow.late.nutrient1', { defaultValue: 'Higher energy (TDN 65-70%)' }),
            t('pregnancy.diet.cow.late.nutrient2', { defaultValue: 'Balanced calcium (prevent milk fever)' }),
            t('pregnancy.diet.cow.late.nutrient3', { defaultValue: 'Vitamin E and Selenium' })
          ];
        }
      case 'goat':
        if (stage === 'early') {
          return [
            t('pregnancy.diet.goat.early.nutrient1', { defaultValue: 'Calcium and Phosphorus' }),
            t('pregnancy.diet.goat.early.nutrient2', { defaultValue: 'Vitamin A' }),
            t('pregnancy.diet.goat.early.nutrient3', { defaultValue: 'Quality protein (14%)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.diet.goat.mid.nutrient1', { defaultValue: 'Increased energy' }),
            t('pregnancy.diet.goat.mid.nutrient2', { defaultValue: 'Protein (14-16%)' }),
            t('pregnancy.diet.goat.mid.nutrient3', { defaultValue: 'Copper and Selenium' })
          ];
        } else {
          return [
            t('pregnancy.diet.goat.late.nutrient1', { defaultValue: 'Higher energy density' }),
            t('pregnancy.diet.goat.late.nutrient2', { defaultValue: 'Calcium (prevent toxemia)' }),
            t('pregnancy.diet.goat.late.nutrient3', { defaultValue: 'Vitamin E' })
          ];
        }
      case 'sheep':
        if (stage === 'early') {
          return [
            t('pregnancy.diet.sheep.early.nutrient1', { defaultValue: 'Selenium' }),
            t('pregnancy.diet.sheep.early.nutrient2', { defaultValue: 'Vitamin E' }),
            t('pregnancy.diet.sheep.early.nutrient3', { defaultValue: 'Quality protein (12-14%)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.diet.sheep.mid.nutrient1', { defaultValue: 'Increased energy' }),
            t('pregnancy.diet.sheep.mid.nutrient2', { defaultValue: 'Protein (14%)' }),
            t('pregnancy.diet.sheep.mid.nutrient3', { defaultValue: 'Copper (caution with levels)' })
          ];
        } else {
          return [
            t('pregnancy.diet.sheep.late.nutrient1', { defaultValue: 'Higher energy density' }),
            t('pregnancy.diet.sheep.late.nutrient2', { defaultValue: 'Calcium' }),
            t('pregnancy.diet.sheep.late.nutrient3', { defaultValue: 'Vitamin B complex' })
          ];
        }
      case 'pig':
        if (stage === 'early') {
          return [
            t('pregnancy.diet.pig.early.nutrient1', { defaultValue: 'Folic acid' }),
            t('pregnancy.diet.pig.early.nutrient2', { defaultValue: 'Quality protein (12-14%)' }),
            t('pregnancy.diet.pig.early.nutrient3', { defaultValue: 'Vitamin A' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.diet.pig.mid.nutrient1', { defaultValue: 'Increased lysine' }),
            t('pregnancy.diet.pig.mid.nutrient2', { defaultValue: 'Calcium and Phosphorus' }),
            t('pregnancy.diet.pig.mid.nutrient3', { defaultValue: 'B vitamins' })
          ];
        } else {
          return [
            t('pregnancy.diet.pig.late.nutrient1', { defaultValue: 'Fiber' }),
            t('pregnancy.diet.pig.late.nutrient2', { defaultValue: 'Increased energy' }),
            t('pregnancy.diet.pig.late.nutrient3', { defaultValue: 'Vitamin E and Selenium' })
          ];
        }
      default:
        return [
          t('pregnancy.diet.general.nutrient1', { defaultValue: 'Balanced minerals' }),
          t('pregnancy.diet.general.nutrient2', { defaultValue: 'Quality protein' }),
          t('pregnancy.diet.general.nutrient3', { defaultValue: 'Essential vitamins' })
        ];
    }
  };

  const getSpeciesSpecificFoodsWithQuantity = (species: string, stage: string): string[] => {
    switch (species) {
      case 'cow':
        if (stage === 'early') {
          return [
            t('pregnancy.diet.cow.early.food1', { defaultValue: 'High-quality hay or pasture (10-15 kg/day)' }),
            t('pregnancy.diet.cow.early.food2', { defaultValue: 'Balanced mineral mix (100-150g/day)' }),
            t('pregnancy.diet.cow.early.food3', { defaultValue: 'Limited grain if needed (1-2 kg/day)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.diet.cow.mid.food1', { defaultValue: 'Quality forage (12-18 kg/day)' }),
            t('pregnancy.diet.cow.mid.food2', { defaultValue: 'Moderate grain supplementation (2-3 kg/day)' }),
            t('pregnancy.diet.cow.mid.food3', { defaultValue: 'Mineral mix with trace elements (150g/day)' })
          ];
        } else {
          return [
            t('pregnancy.diet.cow.late.food1', { defaultValue: 'Energy-dense feed (15-20 kg/day total)' }),
            t('pregnancy.diet.cow.late.food2', { defaultValue: 'Properly balanced minerals (150-200g/day)' }),
            t('pregnancy.diet.cow.late.food3', { defaultValue: 'Quality hay with legumes (10-12 kg/day)' })
          ];
        }
      case 'goat':
        if (stage === 'early') {
          return [
            t('pregnancy.diet.goat.early.food1', { defaultValue: 'Browse and forage (2-3 kg/day)' }),
            t('pregnancy.diet.goat.early.food2', { defaultValue: 'Alfalfa hay (0.5-1 kg/day)' }),
            t('pregnancy.diet.goat.early.food3', { defaultValue: 'Goat-specific mineral mix (25-30g/day)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.diet.goat.mid.food1', { defaultValue: 'Quality hay (2-3 kg/day)' }),
            t('pregnancy.diet.goat.mid.food2', { defaultValue: 'Limited grain mix (0.5-0.75 kg/day)' }),
            t('pregnancy.diet.goat.mid.food3', { defaultValue: 'Mineral supplement with copper (30g/day)' })
          ];
        } else {
          return [
            t('pregnancy.diet.goat.late.food1', { defaultValue: 'Alfalfa hay (2-3 kg/day)' }),
            t('pregnancy.diet.goat.late.food2', { defaultValue: 'Balanced grain mix (0.75-1 kg/day)' }),
            t('pregnancy.diet.goat.late.food3', { defaultValue: 'Molasses as energy source (50-100g/day)' })
          ];
        }
      case 'sheep':
        if (stage === 'early') {
          return [
            t('pregnancy.diet.sheep.early.food1', { defaultValue: 'Quality pasture/hay (1.5-2 kg/day)' }),
            t('pregnancy.diet.sheep.early.food2', { defaultValue: 'Sheep-specific mineral mix (20-25g/day)' }),
            t('pregnancy.diet.sheep.early.food3', { defaultValue: 'Limited grain (0.2-0.4 kg/day)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.diet.sheep.mid.food1', { defaultValue: 'Mixed hay (2-2.5 kg/day)' }),
            t('pregnancy.diet.sheep.mid.food2', { defaultValue: 'Moderate grain (0.4-0.6 kg/day)' }),
            t('pregnancy.diet.sheep.mid.food3', { defaultValue: 'Selenium supplement in deficient areas (as prescribed)' })
          ];
        } else {
          return [
            t('pregnancy.diet.sheep.late.food1', { defaultValue: 'Energy-dense feed (2.5-3 kg/day total)' }),
            t('pregnancy.diet.sheep.late.food2', { defaultValue: 'Quality hay (1.5-2 kg/day)' }),
            t('pregnancy.diet.sheep.late.food3', { defaultValue: 'Balanced grain mix (0.6-0.8 kg/day)' })
          ];
        }
      case 'pig':
        if (stage === 'early') {
          return [
            t('pregnancy.diet.pig.early.food1', { defaultValue: 'Balanced sow feed (2-2.5 kg/day)' }),
            t('pregnancy.diet.pig.early.food2', { defaultValue: 'Fresh water (ad libitum)' }),
            t('pregnancy.diet.pig.early.food3', { defaultValue: 'Limited treats (100-200g/day)' })
          ];
        } else if (stage === 'mid') {
          return [
            t('pregnancy.diet.pig.mid.food1', { defaultValue: 'Increased sow ration (2.5-3 kg/day)' }),
            t('pregnancy.diet.pig.mid.food2', { defaultValue: 'Fresh vegetables (0.5 kg/day)' }),
            t('pregnancy.diet.pig.mid.food3', { defaultValue: 'Clean water (ad libitum)' })
          ];
        } else {
          return [
            t('pregnancy.diet.pig.late.food1', { defaultValue: 'High-fiber feed (3-3.5 kg/day)' }),
            t('pregnancy.diet.pig.late.food2', { defaultValue: 'Bran supplements (0.2-0.3 kg/day)' }),
            t('pregnancy.diet.pig.late.food3', { defaultValue: 'Increased feed volume (3.5-4 kg/day total)' })
          ];
        }
      default:
        return [
          t('pregnancy.diet.general.food1', { defaultValue: 'Quality forage (adjust to body weight)' }),
          t('pregnancy.diet.general.food2', { defaultValue: 'Balanced feed mix (adjust to body weight)' }),
          t('pregnancy.diet.general.food3', { defaultValue: 'Fresh water (ad libitum)' })
        ];
    }
  };

  // Generate diet recommendations by week
  const generateRecommendations = () => {
    // If we have AI-generated plans, use those instead
    if (useAIGeneratedPlan) {
      return pregnancy.aiGeneratedDietPlan;
    }
    
    const recommendations: DietRecommendation[] = [];
    const species = pregnancy.species?.toLowerCase() || 'default';
    
    // Early stage (first trimester)
    const earlyWeeks = Math.floor(totalWeeks / 3);
    
    for (let week = 1; week <= earlyWeeks; week++) {
      recommendations.push({
        week,
        stage: 'early',
        title: getSpeciesSpecificTitle(species, 'early', week),
        description: getSpeciesSpecificDescription(species, 'early'),
        nutrients: getSpeciesSpecificNutrients(species, 'early'),
        foods: getSpeciesSpecificFoodsWithQuantity(species, 'early')
      });
    }
    
    // Mid stage (second trimester)
    const midWeeks = Math.floor(totalWeeks * 2 / 3);
    
    for (let week = earlyWeeks + 1; week <= midWeeks; week++) {
      recommendations.push({
        week,
        stage: 'mid',
        title: getSpeciesSpecificTitle(species, 'mid', week),
        description: getSpeciesSpecificDescription(species, 'mid'),
        nutrients: getSpeciesSpecificNutrients(species, 'mid'),
        foods: getSpeciesSpecificFoodsWithQuantity(species, 'mid')
      });
    }
    
    // Late stage (third trimester)
    for (let week = midWeeks + 1; week <= totalWeeks; week++) {
      recommendations.push({
        week,
        stage: 'late',
        title: getSpeciesSpecificTitle(species, 'late', week),
        description: getSpeciesSpecificDescription(species, 'late'),
        nutrients: getSpeciesSpecificNutrients(species, 'late'),
        foods: getSpeciesSpecificFoodsWithQuantity(species, 'late')
      });
    }
    
    return recommendations;
  };

  // Group recommendations by stage
  const groupRecommendationsByStage = (recommendations: DietRecommendation[]): GroupedRecommendations => {
    return recommendations.reduce((acc: GroupedRecommendations, rec) => {
      if (!acc[rec.stage]) {
        acc[rec.stage] = [];
      }
      acc[rec.stage]?.push(rec);
      return acc;
    }, {});
  };

  const recommendations = generateRecommendations();
  const groupedRecommendations = groupRecommendationsByStage(recommendations || []);

  // Generate week options for dropdown
  const generateWeekOptions = () => {
    const options = [{ id: 'all', label: t('common.all', { defaultValue: 'All' }) }];

    for (let week = 1; week <= totalWeeks; week++) {
      options.push({
        id: week.toString(),
        label: t('pregnancy.weekNumber', { defaultValue: 'Week {{week}}', week })
      });
    }

    return options;
  };

  const weekOptions = generateWeekOptions();

  // Filter recommendations based on selected week
  const filterRecommendationsByWeek = (recs: DietRecommendation[]) => {
    if (selectedWeek === 'all') return recs;
    const weekNumber = parseInt(selectedWeek);
    return recs.filter(rec => rec.week === weekNumber);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <View style={styles.titleRow}>
          <Text style={[styles.sectionTitle, language === 'ur' ? styles.urduText : null]}>
            {t('pregnancy.dietRecommendations', { defaultValue: 'Diet Recommendations' })}
          </Text>
          {useAIGeneratedPlan && (
            <View style={styles.aiGeneratedBadge}>
              <Sparkles size={14} color={colors.primary} />
              <Text style={styles.aiGeneratedText}>{t('pregnancy.aiGenerated')}</Text>
            </View>
          )}
        </View>

        <Text style={[styles.recommendationDesc, language === 'ur' ? styles.urduText : null]}>
          {t('pregnancy.dietSummary', {
            defaultValue: `This {{species}} pregnancy is currently in week {{currentWeek}} of approximately {{totalWeeks}} weeks.`,
            species: pregnancy.species || t('common.animal', { defaultValue: 'animal' }),
            currentWeek: currentWeek,
            totalWeeks: totalWeeks,
          })}
        </Text>

        <View style={styles.weekDropdownContainer}>
          <GenericDropdown
            placeholder={t('pregnancy.selectWeek', { defaultValue: 'Select Week' })}
            items={weekOptions}
            value={selectedWeek}
            onSelect={setSelectedWeek}
            modalTitle={t('pregnancy.selectWeek', { defaultValue: 'Select Week' })}
            searchPlaceholder={t('common.search', { defaultValue: 'Search...' })}
            height={40}
          />
        </View>
      </View>
      
      {Object.entries(groupedRecommendations).map(([stage, recs]) => {
        const filteredRecs = filterRecommendationsByWeek(recs || []);

        if (filteredRecs.length === 0) return null;

        return (
          <View key={stage} style={styles.section}>
            <Text style={[styles.stageHeader, language === 'ur' ? styles.urduText : null]}>
              {t(`pregnancy.stages.${stage}`, { defaultValue: stage.charAt(0).toUpperCase() + stage.slice(1) })}
            </Text>

            {filteredRecs.map((rec, index) => (
            <View 
              key={index} 
              style={[
                styles.recommendationItem, 
                rec.week === currentWeek ? styles.currentWeekIndicator : null,
                index === filteredRecs.length - 1 ? { borderBottomWidth: 0 } : null
              ]}
            >
              <Text style={styles.weekTitle}>
                {t('pregnancy.weekNumber', { defaultValue: 'Week {{week}}', week: rec.week })}
                {rec.week === currentWeek ? ` (${t('pregnancy.current', { defaultValue: 'Current' })})` : ''}
              </Text>
              
              <Text style={[styles.recommendationTitle, language === 'ur' ? styles.urduText : null]}>{rec.title}</Text>
              <Text style={[styles.recommendationDesc, language === 'ur' ? styles.urduText : null]}>{rec.description}</Text>

              <Text style={[styles.recommendationTitle, { marginTop: 8 }, language === 'ur' ? styles.urduText : null]}>
                {t('pregnancy.diet.keyNutrients', { defaultValue: 'Key Nutrients' })}
              </Text>
              {rec.nutrients.map((nutrient, i) => (
                <View key={i} style={language === 'ur' ? styles.bulletPointRTL : styles.bulletPoint}>
                  <View style={language === 'ur' ? styles.bulletRTL : styles.bullet} />
                  <Text style={[styles.bulletText, language === 'ur' ? styles.urduText : null]}>
                    {nutrient.replace(/^AI-recommended\s+/i, '')}
                  </Text>
                </View>
              ))}
              
              <Text style={[styles.recommendationTitle, { marginTop: 8 }, language === 'ur' ? styles.urduText : null]}>
                {t('pregnancy.diet.recommendedFoods', { defaultValue: 'Recommended Foods' })}
              </Text>
              {rec.foods.map((food, i) => (
                <View key={i} style={language === 'ur' ? styles.bulletPointRTL : styles.bulletPoint}>
                  <View style={language === 'ur' ? styles.bulletRTL : styles.bullet} />
                  <Text style={[styles.bulletText, language === 'ur' ? styles.urduText : null]}>
                    {food.replace(/^AI-recommended\s+/i, '')}
                  </Text>
                </View>
              ))}
            </View>
          ))}
        </View>
        );
      })}
    </ScrollView>
  );
};

export default DietTab;












