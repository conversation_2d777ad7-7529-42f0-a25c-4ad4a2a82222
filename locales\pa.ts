export const pa = {
  common: {
    loading: "ਲੋਡ ਹੋ ਰਿਹਾ ਹੈ...",
    cancel: "ਰੱਦ ਕਰੋ",
    save: "ਸੇਵ ਕਰੋ",
    delete: "ਮਿਟਾਓ",
    edit: "ਸੋਧੋ",
    yes: "ਹਾਂ",
    no: "ਨਹੀਂ",
    ok: "ਠੀਕ ਹੈ",
    confirm: "ਪੁਸ਼ਟੀ ਕਰੋ",
    back: "ਵਾਪਸ",
    next: "ਅੱਗੇ",
    done: "ਹੋ ਗਿਆ",
    search: "ਖੋਜ",
    filter: "ਫਿਲਟਰ",
    all: "ਸਾਰੇ",
    user: "ਯੂਜ਼ਰ",
    error: "ਗਲਤੀ",
    success: "ਸਫਲਤਾ",
    warning: "ਚੇਤਾਵਨੀ",
    info: "ਜਾਣਕਾਰੀ",
    permissionDenied: "ਅਨੁਮਤੀ ਨਹੀਂ ਮਿਲੀ",
    dismiss: "ਖਾਰਜ ਕਰੋ",
    pageNotFound: "ਇਹ ਸਕਰੀਨ ਮੌਜੂਦ ਨਹੀਂ ਹੈ।",
    goToHome: "ਹੋਮ ਸਕਰੀਨ 'ਤੇ ਜਾਓ!",
    somethingWentWrong: "ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ",
    checkLogs: "ਵਧੇਰੇ ਵੇਰਵਿਆਂ ਲਈ ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੇ ਡਿਵਾਈਸ ਲੋਗ ਜਾਂਚੋ।",
    selectDate: "ਤਾਰੀਖ ਚੁਣੋ",
    notAvailable: "ਉਪਲਬਧ ਨਹੀਂ",
    weekdays: {
      sun: "ਐਤ",
      mon: "ਸੋਮ",
      tue: "ਮੰਗਲ",
      wed: "ਬੁੱਧ",
      thu: "ਵੀਰ",
      fri: "ਸ਼ੁੱਕਰ",
      sat: "ਸ਼ਨੀ"
    },
  },

  auth: {
    login: "ਲੌਗਇਨ",
    register: "ਰਜਿਸਟਰ",
    forgotPassword: "ਪਾਸਵਰਡ ਭੁੱਲ ਗਏ",
    email: "ਈਮੇਲ",
    password: "ਪਾਸਵਰਡ",
    confirmPassword: "ਪਾਸਵਰਡ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ",
    name: "ਨਾਮ",
    phone: "ਫੋਨ",
    loginWithGoogle: "Google ਨਾਲ ਲੌਗਇਨ ਕਰੋ",
    loginWithOTP: "OTP ਨਾਲ ਲੌਗਇਨ ਕਰੋ",
    dontHaveAccount: "ਖਾਤਾ ਨਹੀਂ ਹੈ?",
    alreadyHaveAccount: "ਪਹਿਲਾਂ ਤੋਂ ਹੀ ਖਾਤਾ ਹੈ?",
    signUp: "ਸਾਈਨ ਅੱਪ",
    signIn: "ਸਾਈਨ ਇਨ",
    enterOTP: "OTP ਦਰਜ ਕਰੋ",
    verifyOTP: "OTP ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ",
    resendOTP: "OTP ਦੁਬਾਰਾ ਭੇਜੋ",
    otpSent: "ਤਸਦੀਕ ਈਮੇਲ ਤੁਹਾਡੇ ਇਨਬਾਕਸ ਵਿੱਚ ਭੇਜੀ ਗਈ ਹੈ",
    invalidOTP: "ਅਵੈਧ OTP",
    invalidCredentials: "ਅਵੈਧ ਈਮੇਲ ਜਾਂ ਪਾਸਵਰਡ",
    passwordsDontMatch: "ਪਾਸਵਰਡ ਮੇਲ ਨਹੀਂ ਖਾਂਦੇ",
    accountCreated: "ਖਾਤਾ ਸਫਲਤਾਪੂਰਵਕ ਬਣਾਇਆ ਗਿਆ",
    verified: "ਤਸਦੀਕ ਕੀਤਾ",
    verifyEmailPrompt: "ਸਾਰੀਆਂ ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ ਤੱਕ ਪਹੁੰਚਣ ਲਈ ਆਪਣੇ ਈਮੇਲ ਪਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ",
    verifyNow: "ਹੁਣੇ ਤਸਦੀਕ ਕਰੋ",
    currentPassword: "ਮੌਜੂਦਾ ਪਾਸਵਰਡ",
    newPassword: "ਨਵਾਂ ਪਾਸਵਰਡ",
    updateEmail: "ਈਮੇਲ ਅੱਪਡੇਟ ਕਰੋ",
    updatePassword: "ਪਾਸਵਰਡ ਅੱਪਡੇਟ ਕਰੋ",
    emailUpdated: "ਈਮੇਲ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤੀ ਗਈ",
    passwordUpdated: "ਪਾਸਵਰਡ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ",
    updateFailed: "ਅੱਪਡੇਟ ਅਸਫਲ ਹੋਇਆ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।",
  },

  dashboard: {
    welcome: "ਜੀ ਆਇਆਂ ਨੂੰ",
    sync: "ਸਿੰਕ",
    monitoring: "ਨਿਗਰਾਨੀ",
    animals: "ਜਾਨਵਰ",
    yourAnimals: "ਤੁਹਾਡੇ ਜਾਨਵਰ",
    sickAnimals: "ਬੀਮਾਰ ਜਾਨਵਰ",
    healthyAnimals: "ਸਿਹਤਮੰਦ ਜਾਨਵਰ",
    healthChecks: "ਸਿਹਤ ਜਾਂਚਾਂ",
    stats: {
      totalAnimals: "ਕੁੱਲ ਜਾਨਵਰ",
      healthyAnimals: "ਸਿਹਤਮੰਦ ਜਾਨਵਰ",
      healthAlerts: "ਸਿਹਤ ਚੇਤਾਵਨੀਆਂ",
      checksLast30Days: "ਜਾਂਚ (30 ਦਿਨ)",
    },
    overdueChecks: "ਦੇਰੀ ਵਾਲੀਆਂ ਸਿਹਤ ਜਾਂਚਾਂ",
    isDueForCheck: "ਸਿਹਤ ਜਾਂਚ ਲਈ ਬਕਾਇਆ ਹੈ",
    scheduleCheck: "ਸਿਹਤ ਜਾਂਚ ਕਰੋ",
    healthCheckTypes: "ਸਿਹਤ ਜਾਂਚ ਦੀਆਂ ਕਿਸਮਾਂ",
    recentHealthChecks: "ਹਾਲੀਆ ਸਿਹਤ ਜਾਂਚਾਂ",
    recentHealthRecords: "ਹਾਲੀਆ ਸਿਹਤ ਰਿਕਾਰਡ",
    viewAll: "ਸਾਰੇ ਦੇਖੋ",
  },

  common: {
    ok: "ਠੀਕ ਹੈ",
    cancel: "ਰੱਦ ਕਰੋ",
    save: "ਸੇਵ ਕਰੋ",
    delete: "ਮਿਟਾਓ",
    edit: "ਸੋਧੋ",
    add: "ਸ਼ਾਮਲ ਕਰੋ",
    back: "ਵਾਪਸ",
    next: "ਅਗਲਾ",
    done: "ਹੋ ਗਿਆ",
    search: "ਖੋਜੋ",
    filter: "ਫਿਲਟਰ",
    all: "ਸਾਰੇ",
    loading: "ਲੋਡ ਹੋ ਰਿਹਾ ਹੈ...",
    error: "ਗਲਤੀ",
    success: "ਸਫਲਤਾ",
    confirm: "ਪੁਸ਼ਟੀ ਕਰੋ",
    yes: "ਹਾਂ",
    no: "ਨਹੀਂ",
    retry: "ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ",
    tryAgain: "ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ",
    viewAll: "ਸਾਰੇ ਦੇਖੋ",
    filters: "ਫਿਲਟਰ",
    activeFilters: "ਸਰਗਰਮ ਫਿਲਟਰ",
    clear: "ਸਾਫ਼ ਕਰੋ",
    apply: "ਲਾਗੂ ਕਰੋ",
    date: "ਤਾਰੀਖ",
    month: "ਮਹੀਨਾ",
    year: "ਸਾਲ",
    selectMonth: "ਮਹੀਨਾ ਚੁਣੋ",
    selectYear: "ਸਾਲ ਚੁਣੋ",
    unknown: "ਅਣਜਾਣ",
  },

  animals: {
    title: "ਜਾਨਵਰ",
    addAnimal: "ਜਾਨਵਰ ਸ਼ਾਮਲ ਕਰੋ",
    editAnimal: "ਜਾਨਵਰ ਸੋਧੋ",
    animalDetails: "ਜਾਨਵਰ ਦੇ ਵੇਰਵੇ",
    name: "ਨਾਮ",
    namePlaceholder: "ਜਾਨਵਰ ਦਾ ਨਾਮ ਦਰਜ ਕਰੋ",
    nameRequired: "ਨਾਮ ਜ਼ਰੂਰੀ ਹੈ",
    species: "ਪ੍ਰਜਾਤੀ",
    speciesPlaceholder: "ਜਿਵੇਂ ਕਿ ਗਾਂ, ਕੁੱਤਾ, ਬੱਕਰੀ",
    speciesRequired: "ਪ੍ਰਜਾਤੀ ਜ਼ਰੂਰੀ ਹੈ",
    breed: "ਨਸਲ",
    breedPlaceholder: "ਜਿਵੇਂ ਕਿ ਹੋਲਸਟੀਨ, ਜਰਮਨ ਸ਼ੈਫਰਡ",
    age: "ਉਮਰ (ਸਾਲ)",
    agePlaceholder: "ਜਿਵੇਂ ਕਿ 3",
    ageNumber: "ਉਮਰ ਇੱਕ ਨੰਬਰ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ",
    weight: "ਭਾਰ (ਕਿਲੋਗ੍ਰਾਮ)",
    weightPlaceholder: "ਜਿਵੇਂ ਕਿ 450",
    weightNumber: "ਭਾਰ ਇੱਕ ਨੰਬਰ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ",
    gender: "ਲਿੰਗ",
    male: "ਨਰ",
    female: "ਮਾਦਾ",
    unknown: "ਅਣਜਾਣ",
    birthdate: "ਜਨਮ ਮਿਤੀ (ਵਿਕਲਪਿਕ)",
    color: "ਰੰਗ",
    markings: "ਨਿਸ਼ਾਨ",
    tagId: "ਟੈਗ ਆਈਡੀ",
    tagIdPlaceholder: "ਵਿਕਲਪਿਕ ਪਛਾਣ ਟੈਗ",
    microchip: "ਮਾਈਕ੍ਰੋਚਿਪ",
    notes: "ਨੋਟਸ",
    lastHealthCheck: "ਆਖਰੀ ਸਿਹਤ ਜਾਂਚ",
    nextHealthCheck: "ਅਗਲੀ ਸਿਹਤ ਜਾਂਚ",
    healthRecords: "ਸਿਹਤ ਰਿਕਾਰਡ",
    healthChecks: "ਸਿਹਤ ਜਾਂਚਾਂ",
    deleteConfirm: "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ 'ਤੇ ਇਸ ਜਾਨਵਰ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?",
    deleteSuccess: "ਜਾਨਵਰ ਸਫਲਤਾਪੂਰਵਕ ਮਿਟਾਇਆ ਗਿਆ",
    noAnimals: "ਕੋਈ ਜਾਨਵਰ ਨਹੀਂ ਮਿਲੇ",
    addAnimalsFirst: "ਦੁੱਧ ਦਾ ਡੇਟਾ ਰਿਕਾਰਡ ਕਰਨ ਤੋਂ ਪਹਿਲਾਂ ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਫਾਰਮ ਵਿੱਚ ਜਾਨਵਰ ਸ਼ਾਮਲ ਕਰੋ",
    addYourFirstAnimal: "ਸ਼ੁਰੂ ਕਰਨ ਲਈ ਆਪਣਾ ਪਹਿਲਾ ਜਾਨਵਰ ਸ਼ਾਮਲ ਕਰੋ",
    addPhoto: "ਫੋਟੋ ਸ਼ਾਮਲ ਕਰੋ",
    takePhoto: "ਫੋਟੋ ਲਓ",
    choosePhoto: "ਲਾਇਬ੍ਰੇਰੀ ਤੋਂ ਚੁਣੋ",
    saveAnimal: "ਜਾਨਵਰ ਸੇਵ ਕਰੋ",
    addError: "ਜਾਨਵਰ ਸ਼ਾਮਲ ਕਰਨ ਵਿੱਚ ਅਸਫਲ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।",
    cameraPermission: "ਫੋਟੋਆਂ ਲੈਣ ਲਈ ਸਾਨੂੰ ਕੈਮਰਾ ਅਨੁਮਤੀ ਦੀ ਲੋੜ ਹੈ",
    cameraRollPermission: "ਫੋਟੋਆਂ ਅਪਲੋਡ ਕਰਨ ਲਈ ਸਾਨੂੰ ਕੈਮਰਾ ਰੋਲ ਅਨੁਮਤੀ ਦੀ ਲੋੜ ਹੈ",
    healthy: "ਸਿਹਤਮੰਦ",
    unhealthy: "ਬੀਮਾਰ",
    needAttention: "ਧਿਆਨ ਦੀ ਲੋੜ ਹੈ",
    lastVaccine: "ਆਖਰੀ ਟੀਕਾ",
    noVaccineYet: "ਅਜੇ ਤੱਕ ਕੋਈ ਟੀਕਾ ਨਹੀਂ",
    noCheckYet: "ਅਜੇ ਤੱਕ ਕੋਈ ਜਾਂਚ ਨਹੀਂ",
  },

  settings: {
    title: "ਸੈਟਿੰਗਾਂ",
    account: "ਖਾਤਾ",
    profile: "ਪ੍ਰੋਫਾਈਲ",
    profileDescription: "ਆਪਣੀ ਪ੍ਰੋਫਾਈਲ ਦੀ ਜਾਣਕਾਰੀ ਦੇਖੋ",
    language: "ਭਾਸ਼ਾ",
    languages: {
      en: "ਅੰਗਰੇਜ਼ੀ",
      ur: "ਉਰਦੂ",
      pa: "ਪੰਜਾਬੀ",
    },
    darkMode: "ਡਾਰਕ ਮੋਡ",
    notifications: "ਨੋਟੀਫਿਕੇਸ਼ਨ",
    notificationsEnabled: "ਨੋਟੀਫਿਕੇਸ਼ਨ ਸਫਲਤਾਪੂਰਵਕ ਚਾਲੂ ਕੀਤੇ ਗਏ",
    syncSettings: "ਸਿੰਕ ਸੈਟਿੰਗਾਂ",
    offlineMode: "ਔਫਲਾਈਨ ਮੋਡ",
    autoSync: "ਆਟੋ ਸਿੰਕ",
    dataUsage: "ਡਾਟਾ ਵਰਤੋਂ",
    accessibility: "ਪਹੁੰਚਯੋਗਤਾ",
    audioFeedback: "ਆਡੀਓ ਫੀਡਬੈਕ",
    textSize: "ਟੈਕਸਟ ਆਕਾਰ",
    about: "ਬਾਰੇ",
    help: "ਮਦਦ ਅਤੇ ਸਹਾਇਤਾ",
    contactUs: "ਸਾਡੇ ਨਾਲ ਸੰਪਰਕ ਕਰੋ",
    termsOfService: "ਸੇਵਾ ਦੀਆਂ ਸ਼ਰਤਾਂ",
    privacyPolicy: "ਪਰਾਈਵੇਸੀ ਨੀਤੀ",
    logout: "ਲੌਗਆਉਟ",
    logoutTitle: "ਲੌਗਆਉਟ",
    logoutMessage: "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ 'ਤੇ ਲੌਗਆਉਟ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?",
    logoutConfirm: "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ 'ਤੇ ਲੌਗਆਉਟ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?",
    logoutErrorTitle: "ਲੌਗਆਉਟ ਗਲਤੀ",
    logoutErrorMessage: "ਲੌਗਆਉਟ ਕਰਨ ਵਿੱਚ ਸਮੱਸਿਆ ਸੀ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।",
    version: "ਵਰਜ਼ਨ",
    emailPassword: "ਈਮੇਲ ਅਤੇ ਪਾਸਵਰਡ",
    privacySecurity: "ਪਰਾਈਵੇਸੀ ਅਤੇ ਸੁਰੱਖਿਆ",
    support: "ਸਹਾਇਤਾ",
  },

  tabs: {
    dashboard: "ਡੈਸ਼ਬੋਰਡ",
    animals: "ਜਾਨਵਰ",
    farms: "ਫਾਰਮ",
    expenses: "ਖਰਚੇ",
    settings: "ਸੈਟਿੰਗਾਂ",
  },

  account: {
    updateAccount: "ਖਾਤਾ ਅੱਪਡੇਟ ਕਰੋ",
    updateEmail: "ਈਮੇਲ ਅੱਪਡੇਟ ਕਰੋ",
    updatePassword: "ਪਾਸਵਰਡ ਅੱਪਡੇਟ ਕਰੋ",
    currentEmail: "ਮੌਜੂਦਾ ਈਮੇਲ",
    newEmail: "ਨਵੀਂ ਈਮੇਲ",
    currentPassword: "ਮੌਜੂਦਾ ਪਾਸਵਰਡ",
    newPassword: "ਨਵਾਂ ਪਾਸਵਰਡ",
    confirmNewPassword: "ਨਵੇਂ ਪਾਸਵਰਡ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ",
    saveChanges: "ਤਬਦੀਲੀਆਂ ਸੇਵ ਕਰੋ",
    emailUpdated: "ਈਮੇਲ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤੀ ਗਈ",
    passwordUpdated: "ਪਾਸਵਰਡ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ",
    updateFailed: "ਅੱਪਡੇਟ ਅਸਫਲ ਹੋਇਆ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।",
    passwordsDontMatch: "ਨਵੇਂ ਪਾਸਵਰਡ ਮੇਲ ਨਹੀਂ ਖਾਂਦੇ",
    passwordTooShort: "ਪਾਸਵਰਡ ਘੱਟੋ-ਘੱਟ 6 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ",
    invalidEmail: "ਕਿਰਪਾ ਕਰਕੇ ਵੈਧ ਈਮੇਲ ਪਤਾ ਦਰਜ ਕਰੋ",
  },

  profile: {
    title: "ਪ੍ਰੋਫਾਈਲ",
    personalInfo: "ਨਿੱਜੀ ਜਾਣਕਾਰੀ",
    accountInfo: "ਖਾਤਾ ਜਾਣਕਾਰੀ",
    statistics: "ਅੰਕੜੇ",
    name: "ਨਾਮ",
    email: "ਈਮੇਲ",
    role: "ਭੂਮਿਕਾ",
    memberSince: "ਮੈਂਬਰ ਬਣਨ ਦੀ ਤਾਰੀਖ",
    emailVerified: "ਈਮੇਲ ਪੁਸ਼ਟੀ",
    verified: "ਪੁਸ਼ਟੀ ਹੋਈ",
    notVerified: "ਪੁਸ਼ਟੀ ਨਹੀਂ ਹੋਈ",
    farmsOwned: "ਮਾਲਕੀ ਫਾਰਮ",
    farmsAssigned: "ਸੌਂਪੇ ਗਏ ਫਾਰਮ",
    totalAnimals: "ਕੁੱਲ ਜਾਨਵਰ",
    profilePicture: "ਪ੍ਰੋਫਾਈਲ ਤਸਵੀਰ",
    changeProfilePicture: "ਪ੍ਰੋਫਾਈਲ ਤਸਵੀਰ ਬਦਲੋ",
    removeProfilePicture: "ਪ੍ਰੋਫਾਈਲ ਤਸਵੀਰ ਹਟਾਓ",
    selectImageSource: "ਤਸਵੀਰ ਦਾ ਸਰੋਤ ਚੁਣੋ",
    camera: "ਕੈਮਰਾ",
    gallery: "ਗੈਲਰੀ",
    roles: {
      owner: "ਮਾਲਕ",
      admin: "ਪ੍ਰਬੰਧਕ",
      caretaker: "ਦੇਖਭਾਲ ਕਰਨ ਵਾਲਾ",
    },
  },

  language: {
    selectLanguage: "ਭਾਸ਼ਾ ਚੁਣੋ",
    english: "ਅੰਗਰੇਜ਼ੀ",
    urdu: "ਉਰਦੂ",
    punjabi: "ਪੰਜਾਬੀ",
  },

  about: {
    appName: "ਲਾਈਵਸਟਾਕ ਟਰੈਕਰ",
    version: "ਵਰਜ਼ਨ",
    aboutTitle: "ਬਾਰੇ",
    description: "ਲਾਈਵਸਟਾਕ ਟਰੈਕਰ ਇੱਕ ਵਿਆਪਕ ਐਪ ਹੈ ਜੋ ਕਿਸਾਨਾਂ ਅਤੇ ਪਸ਼ੂਆਂ ਦੇ ਮਾਲਕਾਂ ਨੂੰ ਉਨ੍ਹਾਂ ਦੇ ਜਾਨਵਰਾਂ ਨੂੰ ਕੁਸ਼ਲਤਾ ਨਾਲ ਪ੍ਰਬੰਧਿਤ ਕਰਨ ਵਿੱਚ ਮਦਦ ਕਰਨ ਲਈ ਡਿਜ਼ਾਈਨ ਕੀਤੀ ਗਈ ਹੈ। ਸਿਹਤ ਰਿਕਾਰਡ ਰੱਖੋ, ਚੈੱਕ-ਅੱਪ ਸ਼ੈਡਿਊਲ ਕਰੋ, ਅਤੇ ਆਪਣੇ ਪਸ਼ੂਆਂ ਦੀ ਤੰਦਰੁਸਤੀ ਦੀ ਨਿਗਰਾਨੀ ਇੱਕ ਹੀ ਥਾਂ 'ਤੇ ਕਰੋ।",
    featuresTitle: "ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ",
    featureHealthTracking: "ਸਿਹਤ ਟਰੈਕਿੰਗ ਅਤੇ ਨਿਗਰਾਨੀ",
    featureSymptomId: "ਲੱਛਣਾਂ ਦੀ ਪਛਾਣ",
    featureOfflineAccess: "ਮਹੱਤਵਪੂਰਨ ਡਾਟਾ ਤੱਕ ਅੋਫਲਾਈਨ ਪਹੁੰਚ",
    featureLanguageSupport: "ਬਹੁ-ਭਾਸ਼ਾ ਸਹਾਇਤਾ",
    contactTitle: "ਸੰਪਰਕ",
    legalTitle: "ਕਾਨੂੰਨੀ",
    copyright: "ਲਾਈਵਸਟਾਕ ਟਰੈਕਰ। ਸਾਰੇ ਹੱਕ ਰਾਖਵੇਂ ਹਨ।",
  },
  forms: {
    tasks: {
      title: "ਕੰਮ",
      addTask: "ਕੰਮ ਸ਼ਾਮਲ ਕਰੋ",
      editTask: "ਕੰਮ ਸੋਧੋ",
      taskDetails: "ਕੰਮ ਦੇ ਵੇਰਵੇ",
      taskName: "ਕੰਮ ਦਾ ਨਾਮ",
      taskNamePlaceholder: "ਕੰਮ ਦਾ ਨਾਮ ਦਰਜ ਕਰੋ",
      taskNameRequired: "ਕੰਮ ਦਾ ਨਾਮ ਜ਼ਰੂਰੀ ਹੈ",
      description: "ਵੇਰਵਾ",
      descriptionPlaceholder: "ਕੰਮ ਦਾ ਵੇਰਵਾ ਦਰਜ ਕਰੋ",
      assignedTo: "ਸੌਂਪਿਆ ਗਿਆ",
      assignedToPlaceholder: "ਸਟਾਫ ਮੈਂਬਰ ਚੁਣੋ",
      assignedToRequired: "ਸੌਂਪੇ ਗਏ ਸਟਾਫ ਜ਼ਰੂਰੀ ਹੈ",
      dueDate: "ਨਿਯਤ ਤਾਰੀਖ",
      dueDateRequired: "ਨਿਯਤ ਤਾਰੀਖ ਜ਼ਰੂਰੀ ਹੈ",
      status: "ਸਥਿਤੀ",
      statusTodo: "ਕਰਨ ਲਈ"
    }
  },

  farms: {
    title: "ਫਾਰਮ",
    addFarm: "ਫਾਰਮ ਸ਼ਾਮਲ ਕਰੋ",
    editFarm: "ਫਾਰਮ ਸੋਧੋ",
    farmDetails: "ਫਾਰਮ ਵੇਰਵੇ",
    name: "ਫਾਰਮ ਦਾ ਨਾਮ",
    namePlaceholder: "ਫਾਰਮ ਦਾ ਨਾਮ ਦਰਜ ਕਰੋ",
    nameRequired: "ਫਾਰਮ ਦਾ ਨਾਮ ਜ਼ਰੂਰੀ ਹੈ",
    location: "ਸਥਾਨ",
    locationPlaceholder: "ਸਥਾਨ ਦਰਜ ਕਰੋ",
    locationRequired: "ਸਥਾਨ ਜ਼ਰੂਰੀ ਹੈ",
    status: "ਸਥਿਤੀ",
    statusActive: "ਸਰਗਰਮ",
    statusInactive: "ਨਿਸ਼ਕਰਿਆ",
    statusCompleted: "ਪੂਰਾ ਹੋਇਆ",
    statusPending: "ਬਕਾਇਆ",
    statusActiveDescription: "ਫਾਰਮ ਜੋ ਫਿਲਹਾਲ ਸਰਗਰਮ ਹਨ ਅਤੇ ਵਰਤੋਂ ਵਿੱਚ ਹਨ",
    statusInactiveDescription: "ਫਾਰਮ ਜੋ ਹੁਣ ਵਰਤੋਂ ਵਿੱਚ ਨਹੀਂ ਹਨ",
    statusCompletedDescription: "ਫਾਰਮ ਜੋ ਪੂਰੇ ਹੋ ਚੁੱਕੇ ਹਨ",
    statusPendingDescription: "ਫਾਰਮ ਜੋ ਮਨਜ਼ੂਰੀ ਜਾਂ ਕਾਰਵਾਈ ਦੀ ਉਡੀਕ ਕਰ ਰਹੇ ਹਨ",
    animals: "ਜਾਨਵਰ",
    staff: "ਸਟਾਫ",
    milking: "ਦੁੱਧ ਕੱਢਣਾ",
    noFarms: "ਕੋਈ ਫਾਰਮ ਨਹੀਂ ਮਿਲੇ",
    addYourFirstFarm: "ਸ਼ੁਰੂ ਕਰਨ ਲਈ ਆਪਣਾ ਪਹਿਲਾ ਫਾਰਮ ਸ਼ਾਮਲ ਕਰੋ",
    saveFarm: "ਫਾਰਮ ਸੇਵ ਕਰੋ",
    addError: "ਫਾਰਮ ਸ਼ਾਮਲ ਕਰਨ ਵਿੱਚ ਅਸਫਲ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।",
    addSuccess: "ਫਾਰਮ ਸਫਲਤਾਪੂਰਵਕ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ",
    deleteConfirm: "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ 'ਤੇ ਇਸ ਫਾਰਮ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?",
    deleteSuccess: "ਫਾਰਮ ਸਫਲਤਾਪੂਰਵਕ ਮਿਟਾਇਆ ਗਿਆ",
    viewAnimals: "ਜਾਨਵਰ ਦੇਖੋ",
    addEmployee: "ਕਰਮਚਾਰੀ ਸ਼ਾਮਲ ਕਰੋ",
    assignTask: "ਕੰਮ ਸੌਂਪੋ",
    filterFarms: "ਫਾਰਮ ਫਿਲਟਰ ਕਰੋ",
    filterByStatus: "ਸਥਿਤੀ ਅਨੁਸਾਰ ਫਿਲਟਰ ਕਰੋ",
    searchFarms: "ਫਾਰਮ ਖੋਜੋ",
    farmName: "ਫਾਰਮ ਦਾ ਨਾਮ",
    farmNamePlaceholder: "ਫਾਰਮ ਚੁਣੋ",
    farmNameRequired: "ਫਾਰਮ ਦਾ ਨਾਮ ਜ਼ਰੂਰੀ ਹੈ",
    requiredTitle: "ਫਾਰਮ ਜ਼ਰੂਰੀ ਹੈ",
    requiredMessage: "ਹੋਰ ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ ਤੱਕ ਪਹੁੰਚਣ ਤੋਂ ਪਹਿਲਾਂ ਤੁਹਾਨੂੰ ਇੱਕ ਫਾਰਮ ਬਣਾਉਣ ਦੀ ਲੋੜ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਪਹਿਲਾਂ ਇੱਕ ਫਾਰਮ ਸ਼ਾਮਲ ਕਰੋ।",
    updateSuccess: "ਫਾਰਮ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ",
    updateError: "ਫਾਰਮ ਅੱਪਡੇਟ ਕਰਨ ਵਿੱਚ ਅਸਫਲ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।",
    noStaff: "ਕੋਈ ਸਟਾਫ ਨਹੀਂ ਮਿਲਿਆ",
    addYourFirstStaff: "ਸ਼ੁਰੂ ਕਰਨ ਲਈ ਆਪਣਾ ਪਹਿਲਾ ਸਟਾਫ ਸ਼ਾਮਲ ਕਰੋ",

    tasks: {
      title: "ਕੰਮ",
      addTask: "ਕੰਮ ਸ਼ਾਮਲ ਕਰੋ",
      editTask: "ਕੰਮ ਸੋਧੋ",
      taskDetails: "ਕੰਮ ਦੇ ਵੇਰਵੇ",
      taskName: "ਕੰਮ ਦਾ ਨਾਮ",
      taskNamePlaceholder: "ਕੰਮ ਦਾ ਨਾਮ ਦਰਜ ਕਰੋ",
      taskNameRequired: "ਕੰਮ ਦਾ ਨਾਮ ਜ਼ਰੂਰੀ ਹੈ",
      description: "ਵੇਰਵਾ",
      descriptionPlaceholder: "ਕੰਮ ਦਾ ਵੇਰਵਾ ਦਰਜ ਕਰੋ",
      assignedTo: "ਸੌਂਪਿਆ ਗਿਆ",
      assignedToPlaceholder: "ਸਟਾਫ ਮੈਂਬਰ ਚੁਣੋ",
      assignedToRequired: "ਸੌਂਪੇ ਗਏ ਸਟਾਫ ਜ਼ਰੂਰੀ ਹੈ",
      dueDate: "ਨਿਯਤ ਤਾਰੀਖ",
      dueDateRequired: "ਨਿਯਤ ਤਾਰੀਖ ਜ਼ਰੂਰੀ ਹੈ",
      status: "ਸਥਿਤੀ",
      statusTodo: "ਕਰਨ ਲਈ",
      statusInProgress: "ਪ੍ਰਗਤੀ ਵਿੱਚ",
      statusCompleted: "ਪੂਰਾ ਹੋਇਆ",
      statusOverdue: "ਮਿਆਦ ਪੁੱਗੀ",
      saveTask: "ਕੰਮ ਸੇਵ ਕਰੋ",
      addError: "ਕੰਮ ਸ਼ਾਮਲ ਕਰਨ ਵਿੱਚ ਅਸਫਲ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।",
      addSuccess: "ਕੰਮ ਸਫਲਤਾਪੂਰਵਕ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ",
      deleteConfirm: "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ 'ਤੇ ਇਸ ਕੰਮ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?",
      deleteSuccess: "ਕੰਮ ਸਫਲਤਾਪੂਰਵਕ ਮਿਟਾਇਆ ਗਿਆ",
      noTasks: "ਕੋਈ ਕੰਮ ਨਹੀਂ ਮਿਲੇ",
      addYourFirstTask: "ਸ਼ੁਰੂ ਕਰਨ ਲਈ ਆਪਣਾ ਪਹਿਲਾ ਕੰਮ ਸ਼ਾਮਲ ਕਰੋ"
    },
    staffSection: {
      title: "ਸਟਾਫ",
      addStaff: "ਸਟਾਫ ਮੈਂਬਰ ਸ਼ਾਮਲ ਕਰੋ",
      editStaff: "ਸਟਾਫ ਮੈਂਬਰ ਸੋਧੋ",
      staffDetails: "ਸਟਾਫ ਵੇਰਵੇ",
      name: "ਨਾਮ",
      namePlaceholder: "ਸਟਾਫ ਦਾ ਨਾਮ ਦਰਜ ਕਰੋ",
      nameRequired: "ਸਟਾਫ ਦਾ ਨਾਮ ਜ਼ਰੂਰੀ ਹੈ",
      role: "ਭੂਮਿਕਾ",
      rolePlaceholder: "ਸਟਾਫ ਦੀ ਭੂਮਿਕਾ ਦਰਜ ਕਰੋ",
      roleRequired: "ਸਟਾਫ ਦੀ ਭੂਮਿਕਾ ਜ਼ਰੂਰੀ ਹੈ",
      contact: "ਸੰਪਰਕ",
      contactPlaceholder: "ਸੰਪਰਕ ਜਾਣਕਾਰੀ ਦਰਜ ਕਰੋ",
      contactRequired: "ਸੰਪਰਕ ਜਾਣਕਾਰੀ ਜ਼ਰੂਰੀ ਹੈ",
      saveStaff: "ਸਟਾਫ ਮੈਂਬਰ ਸੇਵ ਕਰੋ",
      addError: "ਸਟਾਫ ਮੈਂਬਰ ਸ਼ਾਮਲ ਕਰਨ ਵਿੱਚ ਅਸਫਲ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।",
      addSuccess: "ਸਟਾਫ ਮੈਂਬਰ ਸਫਲਤਾਪੂਰਵਕ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ",
      deleteConfirm: "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ 'ਤੇ ਇਸ ਸਟਾਫ ਮੈਂਬਰ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?",
      deleteSuccess: "ਸਟਾਫ ਮੈਂਬਰ ਸਫਲਤਾਪੂਰਵਕ ਮਿਟਾਇਆ ਗਿਆ",
      noStaff: "ਕੋਈ ਸਟਾਫ ਮੈਂਬਰ ਨਹੀਂ ਮਿਲੇ",
      addYourFirstStaff: "ਸ਼ੁਰੂ ਕਰਨ ਲਈ ਆਪਣਾ ਪਹਿਲਾ ਸਟਾਫ ਮੈਂਬਰ ਸ਼ਾਮਲ ਕਰੋ"
    }
  },
  expenses: {
    title: "ਖਰਚੇ",
    noExpenses: "ਕੋਈ ਖਰਚੇ ਨਹੀਂ",
    addYourFirstExpense: "ਲਾਗਤਾਂ ਨੂੰ ਟਰੈਕ ਕਰਨ ਲਈ ਆਪਣਾ ਪਹਿਲਾ ਖਰਚਾ ਜੋੜੋ",
    addExpense: "ਖਰਚਾ ਜੋੜੋ",
    addNew: "ਨਵਾਂ ਖਰਚਾ ਜੋੜੋ",
    editExpense: "ਖਰਚਾ ਸੋਧੋ",
    expenseDetails: "ਖਰਚੇ ਦੇ ਵੇਰਵੇ",
    amount: "ਰਕਮ",
    amountPlaceholder: "ਰਕਮ ਦਰਜ ਕਰੋ",
    date: "ਤਾਰੀਖ",
    selectCurrency: "ਮੁਦਰਾ ਚੁਣੋ",
    selectStaff: "ਸਟਾਫ ਚੁਣੋ",
    selectStaffPlaceholder: "ਸਟਾਫ ਮੈਂਬਰ ਚੁਣੋ",
    searchStaff: "ਸਟਾਫ ਖੋਜੋ",
    errors: {
      invalidAmount: "ਕਿਰਪਾ ਕਰਕੇ ਵੈਧ ਰਕਮ ਦਰਜ ਕਰੋ",
      categoryRequired: "ਕਿਰਪਾ ਕਰਕੇ ਸ਼੍ਰੇਣੀ ਚੁਣੋ",
      farmRequired: "ਕਿਰਪਾ ਕਰਕੇ ਫਾਰਮ ਚੁਣੋ",
      paymentMethodRequired: "ਕਿਰਪਾ ਕਰਕੇ ਭੁਗਤਾਨ ਵਿਧੀ ਚੁਣੋ",
      staffRequired: "ਕਿਰਪਾ ਕਰਕੇ ਸਟਾਫ ਮੈਂਬਰ ਚੁਣੋ"
    },
    category: {
      label: "ਸ਼੍ਰੇਣੀ",
      select: "ਸ਼੍ਰੇਣੀ ਚੁਣੋ",
      animalPurchase: "ਜਾਨਵਰ ਖਰੀਦ",
      feed: "ਫੀਡ",
      medication: "ਦਵਾਈ",
      vaccination: "ਟੀਕਾਕਰਨ",
      veterinary: "ਪਸ਼ੂ ਚਿਕਿਤਸਾ ਸੇਵਾਵਾਂ",
      equipment: "ਉਪਕਰਣ",
      utilities: "ਯੂਟੀਲਿਟੀਜ਼",
      labor: "ਮਜ਼ਦੂਰੀ",
      maintenance: "ਰੱਖ-ਰਖਾਵ",
      other: "ਹੋਰ"
    },
    paymentMethod: {
      label: "ਭੁਗਤਾਨ ਵਿਧੀ",
      select: "ਭੁਗਤਾਨ ਵਿਧੀ ਚੁਣੋ",
      cash: "ਨਕਦ",
      card: "ਕਾਰਡ",
      bankTransfer: "ਬੈਂਕ ਟ੍ਰਾਂਸਫਰ",
      other: "ਹੋਰ"
    },
    receipt: "ਰਸੀਦ",
    descriptionPlaceholder: "ਇਸ ਖਰਚੇ ਬਾਰੇ ਵੇਰਵਾ ਜਾਂ ਨੋਟ ਦਰਜ ਕਰੋ",
    filterByFarm: "ਫਾਰਮ ਦੁਆਰਾ ਫਿਲਟਰ ਕਰੋ",
    validation: {
      amount: "ਕਿਰਪਾ ਕਰਕੇ ਵੈਧ ਰਕਮ ਦਰਜ ਕਰੋ",
      category: "ਕਿਰਪਾ ਕਰਕੇ ਸ਼੍ਰੇਣੀ ਚੁਣੋ",
      farm: "ਕਿਰਪਾ ਕਰਕੇ ਫਾਰਮ ਚੁਣੋ",
      paymentMethod: "ਕਿਰਪਾ ਕਰਕੇ ਭੁਗਤਾਨ ਵਿਧੀ ਚੁਣੋ"
    },
    addSuccess: "ਖਰਚਾ ਜੋੜਿਆ ਗਿਆ",
    addSuccessDetail: "ਖਰਚਾ ਸਫਲਤਾਪੂਰਵਕ ਜੋੜਿਆ ਗਿਆ ਹੈ",
    addError: "ਖਰਚਾ ਜੋੜਨ ਵਿੱਚ ਅਸਫਲ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।",
  },
  milking: {
    title: "ਦੁੱਧ ਕੱਢਣ ਦੇ ਰਿਕਾਰਡ",
    addRecord: "ਦੁੱਧ ਕੱਢਣ ਦਾ ਰਿਕਾਰਡ ਸ਼ਾਮਲ ਕਰੋ",
    editRecord: "ਦੁੱਧ ਕੱਢਣ ਦਾ ਰਿਕਾਰਡ ਸੋਧੋ",
    recordDetails: "ਦੁੱਧ ਕੱਢਣ ਦੇ ਰਿਕਾਰਡ ਦੇ ਵੇਰਵੇ",
    noRecords: "ਦੁੱਧ ਕੱਢਣ ਦੇ ਕੋਈ ਰਿਕਾਰਡ ਨਹੀਂ",
    recordNotFound: "ਰਿਕਾਰਡ ਨਹੀਂ ਮਿਲਿਆ",
    addYourFirstRecord: "ਉਤਪਾਦਨ ਨੂੰ ਟਰੈਕ ਕਰਨ ਲਈ ਆਪਣਾ ਪਹਿਲਾ ਦੁੱਧ ਕੱਢਣ ਦਾ ਰਿਕਾਰਡ ਸ਼ਾਮਲ ਕਰੋ",
    noRecordsForFilter: "ਚੁਣੇ ਗਏ ਫਿਲਟਰਾਂ ਲਈ ਕੋਈ ਰਿਕਾਰਡ ਨਹੀਂ ਮਿਲੇ",
    tryDifferentFilters: "ਆਪਣੇ ਫਿਲਟਰ ਬਦਲੋ ਜਾਂ ਨਵੇਂ ਦੁੱਧ ਕੱਢਣ ਦੇ ਰਿਕਾਰਡ ਸ਼ਾਮਲ ਕਰੋ",
    averagePerRecord: "ਪ੍ਰਤੀ ਰਿਕਾਰਡ ਔਸਤ",
    recentRecords: "ਹਾਲੀਆ ਰਿਕਾਰਡ",
    statistics: "ਦੁੱਧ ਕੱਢਣ ਦੇ ਅੰਕੜੇ",
    totalProduction: "ਕੁੱਲ ਉਤਪਾਦਨ",
    dailyAverage: "ਰੋਜ਼ਾਨਾ ਔਸਤ",
    monthlyTrend: "ਮਾਸਿਕ ਰੁਝਾਨ",
    liters: "ਲਿਟਰ",
    records: "ਰਿਕਾਰਡ",
    quantity: "ਮਾਤਰਾ (ਲਿਟਰ)",
    quality: "ਗੁਣਵੱਤਾ",
    session: {
      morning: "ਸਵੇਰ",
      afternoon: "ਦੁਪਹਿਰ",
      evening: "ਸ਼ਾਮ"
    },
    quality: {
      excellent: "ਸ਼ਾਨਦਾਰ",
      good: "ਚੰਗਾ",
      fair: "ਠੀਕ",
      poor: "ਮਾੜਾ"
    },
    animal: "ਜਾਨਵਰ",
    date: "ਤਾਰੀਖ",
    notes: "ਨੋਟਸ",
    milkedBy: "ਦੁੱਧ ਕੱਢਣ ਵਾਲਾ",
    temperature: "ਤਾਪਮਾਨ (°C)",
    fat: "ਚਰਬੀ %",
    protein: "ਪ੍ਰੋਟੀਨ %",
    addSuccess: "ਦੁੱਧ ਕੱਢਣ ਦਾ ਰਿਕਾਰਡ ਸਫਲਤਾਪੂਰਵਕ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ",
    updateSuccess: "ਦੁੱਧ ਕੱਢਣ ਦਾ ਰਿਕਾਰਡ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ",
    deleteSuccess: "ਦੁੱਧ ਕੱਢਣ ਦਾ ਰਿਕਾਰਡ ਸਫਲਤਾਪੂਰਵਕ ਮਿਟਾਇਆ ਗਿਆ",
    deleteConfirm: "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ 'ਤੇ ਇਸ ਦੁੱਧ ਕੱਢਣ ਦੇ ਰਿਕਾਰਡ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?",
    errors: {
      animalRequired: "ਜਾਨਵਰ ਜ਼ਰੂਰੀ ਹੈ",
      quantityRequired: "ਮਾਤਰਾ ਜ਼ਰੂਰੀ ਹੈ",
      qualityRequired: "ਗੁਣਵੱਤਾ ਜ਼ਰੂਰੀ ਹੈ",
      sessionRequired: "ਸੈਸ਼ਨ ਜ਼ਰੂਰੀ ਹੈ",
      dateRequired: "ਤਾਰੀਖ ਜ਼ਰੂਰੀ ਹੈ",
      invalidFat: "ਕਿਰਪਾ ਕਰਕੇ ਵੈਧ ਚਰਬੀ ਪ੍ਰਤੀਸ਼ਤ ਦਰਜ ਕਰੋ",
      invalidProtein: "ਕਿਰਪਾ ਕਰਕੇ ਵੈਧ ਪ੍ਰੋਟੀਨ ਪ੍ਰਤੀਸ਼ਤ ਦਰਜ ਕਰੋ",
      invalidTemperature: "ਕਿਰਪਾ ਕਰਕੇ ਵੈਧ ਤਾਪਮਾਨ ਦਰਜ ਕਰੋ"
    }
  },
};


